# 锁仓订单表设计与改造方案

## 1. 背景分析

### 当前系统现状
- **UserProduct表**: 主要用于记录用户购买商品的记录，包含节点产品、普通商品和债券产品
- **Job3**: 当前使用UserProduct表进行静态动态收益计算
- **产品类型**: 
  - type=1: 预售节点产品
  - type=2: 普通商品（360天线性释放）
  - type=3: 债券产品（根据债券属性决定锁仓天数和收益规则）
  - type=4: 城主商品（不生成锁仓订单，增加4倍收益次数）

### 存在问题
1. UserProduct表职责混乱，既要记录购买记录，又要承担锁仓计算逻辑
2. 静态动态收益计算逻辑与购买记录耦合过紧
3. 缺乏专门的锁仓订单管理机制
4. 难以支持复杂的锁仓规则和收益计算

## 2. 锁仓订单表设计

### 2.1 Product表扩展

首先需要确保Product表有sold字段来记录已售数量：

```sql
-- 检查并添加Product表的sold字段（如果不存在）
ALTER TABLE xyc.product ADD COLUMN sold DECIMAL(25,8) DEFAULT 0 COMMENT '已售数量';

-- 创建索引优化查询
CREATE INDEX idx_product_sold ON xyc.product(sold);
```

### 2.2 锁仓订单表结构设计

```sql
-- 锁仓订单表
DROP TABLE IF EXISTS xyc.lock_order;
CREATE TABLE xyc.lock_order (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(64) NOT NULL COMMENT '锁仓订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    source_type TINYINT NOT NULL COMMENT '来源类型: 1-节点产品 2-普通商品 3-债券产品 4-质押 5-其他',
    source_id BIGINT NULL COMMENT '来源ID(user_product.id或其他业务ID)',
    token_id VARCHAR(32) NOT NULL COMMENT '代币ID',
    
    -- 锁仓基本信息
    lock_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '锁仓总金额',
    released_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '已释放金额',
    available_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '可提取金额(已释放未提取)',
    
    -- 释放规则
    release_type TINYINT NOT NULL COMMENT '释放类型: 1-线性释放 2-阶梯释放 3-一次性释放',
    total_days INT NOT NULL COMMENT '总锁仓天数',
    daily_release_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '每日释放金额',
    
    -- 收益相关
    static_rate DECIMAL(10,6) NOT NULL DEFAULT 0 COMMENT '静态收益率',
    enable_static TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用静态收益: 0-否 1-是',
    enable_dynamic TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用动态收益: 0-否 1-是',
    compound_interest TINYINT NOT NULL DEFAULT 0 COMMENT '是否复利: 0-否 1-是',
    
    -- 状态和释放计数
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-待激活 1-锁仓中 2-已完成 3-已取消',
    released_days INT NOT NULL DEFAULT 0 COMMENT '已释放天数',
    
    -- 扩展配置
    lock_config JSON NULL COMMENT '锁仓配置(阶梯释放规则等)',
    
    -- 审计字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_order_no (order_no),
    KEY idx_user_id (user_id),
    KEY idx_status (status),
    KEY idx_token_id (token_id),
    KEY idx_source (source_type, source_id),
    KEY idx_released_days (released_days)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='锁仓订单表';
```

### 2.2 使用现有user_log表记录收益

利用现有的user_log表记录锁仓相关的操作和收益，通过type字段区分不同类型，无需修改表结构：

```sql
-- 锁仓相关的user_log类型定义（在代码中定义，不需要修改数据库）
-- 释放相关
-- LOCK_RELEASE_LINEAR = 201;     // 线性释放
-- LOCK_RELEASE_STEP = 202;       // 阶梯释放  
-- LOCK_RELEASE_ONETIME = 203;    // 一次性释放

-- 收益相关
-- LOCK_STATIC_REWARD = 211;      // 静态收益
-- LOCK_DYNAMIC_REWARD = 212;     // 动态收益
-- LOCK_COMPOUND_REWARD = 213;    // 复利收益

-- 产品购买相关
-- PRODUCT_PURCHASE = 230;        // 购买产品

-- 城主商品相关
-- CITY_LORD_PURCHASE = 231;      // 购买城主商品
-- QUADRUPLE_REWARD_ADD = 232;    // 增加4倍收益次数
-- QUADRUPLE_REWARD_USE = 233;    // 使用4倍收益次数

-- 锁仓状态变更
-- LOCK_ORDER_CREATE = 221;       // 锁仓订单创建
-- LOCK_ORDER_COMPLETE = 222;     // 锁仓订单完成
-- LOCK_ORDER_CANCEL = 223;       // 锁仓订单取消
```

## 3. 改造方案

### 3.1 UserProduct表改造

**保持UserProduct表作为购买记录表，简化其职责：**

```sql
-- UserProduct表字段调整
-- 移除释放相关字段，这些逻辑迁移到lock_order表
-- ALTER TABLE xyc.user_product DROP COLUMN daily_release_amount;
-- ALTER TABLE xyc.user_product DROP COLUMN total_released;
-- ALTER TABLE xyc.user_product DROP COLUMN available_amount;
-- ALTER TABLE xyc.user_product DROP COLUMN last_release_date;

-- 保留基本购买信息
-- id, user_id, order_no, product_id, type, quantity, price, amount, status, create_time等
```

### 3.2 业务流程改造

#### 3.2.1 用户购买商品流程

```java
// 伪代码示例
@Transactional(rollbackFor = Exception.class)
public void purchaseProduct(User user, Product product, BigDecimal amount, Integer quantity) {
    // 1. 创建购买记录
    UserProduct userProduct = new UserProduct(user.getId(), orderNo, product, amount);
    userProduct.setQuantity(quantity); // 设置购买数量
    userProductService.save(userProduct);
    
    // 2. 通用购买后处理（更新已售数量、记录日志）
    afterProductPurchase(userProduct, product);
    
    // 3. 根据产品类型创建锁仓订单或特殊处理
    switch (product.getType()) {
        case 1:
            // 节点产品 - 创建节点锁仓订单
            createNodeLockOrder(userProduct, product, amount);
            break;
        case 2:
            // 普通商品 - 创建线性释放锁仓订单
            createNormalProductLockOrder(userProduct, product, amount);
            break;
        case 3:
            // 债券产品 - 创建债券锁仓订单
            createBondLockOrder(userProduct, product, amount);
            break;
        case 4:
            // 城主商品 - 不创建锁仓订单，处理4倍收益次数
            processCityLordProduct(userProduct, product, amount);
            break;
        default:
            throw new IllegalArgumentException("不支持的产品类型: " + product.getType());
    }
}

// 通用的产品购买后处理
private void afterProductPurchase(UserProduct userProduct, Product product) {
    // 1. 更新产品已售数量
    updateProductSoldQuantity(product.getId(), userProduct.getQuantity());
    
    // 2. 记录产品购买日志
    String remark = String.format("购买产品: %s，数量: %d - 订单ID: %d, 产品类型: %d", 
                                 product.getName(), userProduct.getQuantity(), 
                                 userProduct.getId(), product.getType());
    
    UserLog userLog = new UserLog();
    userLog.setUserId(userProduct.getUserId());
    userLog.setType(UserLogType.PRODUCT_PURCHASE.getCode());
    userLog.setAmount(userProduct.getAmount());
    userLog.setRemark(remark);
    userLog.setCreateTime(new Date());
    userLogService.save(userLog);
}

// 更新产品已售数量
private void updateProductSoldQuantity(Long productId, Integer quantity) {
    boolean updateResult = productService.update(
        new LambdaUpdateWrapper<Product>()
            .eq(Product::getId, productId)
            .setSql("sold = COALESCE(sold, 0) + " + quantity)
    );
    
    if (!updateResult) {
        throw new RuntimeException("更新产品已售数量失败");
    }
    
    log.info("更新产品已售数量成功，产品ID = {}, 增加数量 = {}", productId, quantity);
}

private void createNodeLockOrder(UserProduct userProduct, Product product, BigDecimal amount) {
    LockOrder lockOrder = new LockOrder();
    lockOrder.setOrderNo(generateLockOrderNo());
    lockOrder.setUserId(userProduct.getUserId());
    lockOrder.setSourceType(1); // 节点产品
    lockOrder.setSourceId(userProduct.getId());
    lockOrder.setTokenId("XYC");
    lockOrder.setLockAmount(amount);
    lockOrder.setReleaseType(1); // 线性释放
    lockOrder.setTotalDays(product.getDay()); // 使用产品配置的天数
    lockOrder.setDailyReleaseAmount(amount.divide(new BigDecimal(product.getDay()), 8, RoundingMode.HALF_UP));
    lockOrder.setStaticRate(product.getRate()); // 使用产品配置的收益率
    lockOrder.setEnableStatic(1);
    lockOrder.setEnableDynamic(1);
    lockOrder.setCompoundInterest(1); // 节点产品复利
    lockOrder.setStatus(1);
    lockOrder.setStartDate(new Date());
    lockOrder.setEndDate(DateUtils.addDays(new Date(), product.getDay()));
    
    lockOrderService.save(lockOrder);
}

private void createNormalProductLockOrder(UserProduct userProduct, Product product, BigDecimal amount) {
    LockOrder lockOrder = new LockOrder();
    lockOrder.setOrderNo(generateLockOrderNo());
    lockOrder.setUserId(userProduct.getUserId());
    lockOrder.setSourceType(2); // 普通商品
    lockOrder.setSourceId(userProduct.getId());
    lockOrder.setTokenId("XYC");
    lockOrder.setLockAmount(amount);
    lockOrder.setReleaseType(1); // 线性释放
    lockOrder.setTotalDays(product.getDay() != null ? product.getDay() : 360); // 使用产品配置的天数，默认360天
    lockOrder.setDailyReleaseAmount(amount.divide(new BigDecimal(lockOrder.getTotalDays()), 8, RoundingMode.HALF_UP));
    lockOrder.setStaticRate(product.getRate() != null ? product.getRate() : getConfigStaticRate());
    lockOrder.setEnableStatic(1);
    lockOrder.setEnableDynamic(1);
    lockOrder.setCompoundInterest(0); // 普通商品不复利
    lockOrder.setStatus(1);
    lockOrder.setStartDate(new Date());
    lockOrder.setEndDate(DateUtils.addDays(new Date(), lockOrder.getTotalDays()));
    
    lockOrderService.save(lockOrder);
}

private void createBondLockOrder(UserProduct userProduct, Product product, BigDecimal amount) {
    LockOrder lockOrder = new LockOrder();
    lockOrder.setOrderNo(generateLockOrderNo());
    lockOrder.setUserId(userProduct.getUserId());
    lockOrder.setSourceType(3); // 债券产品
    lockOrder.setSourceId(userProduct.getId());
    lockOrder.setTokenId("XYC");
    lockOrder.setLockAmount(amount);
    
    // 债券产品根据具体类型决定释放方式
    if (isBondLinearRelease(product)) {
        lockOrder.setReleaseType(1); // 线性释放
        lockOrder.setDailyReleaseAmount(amount.divide(new BigDecimal(product.getDay()), 8, RoundingMode.HALF_UP));
    } else if (isBondStepRelease(product)) {
        lockOrder.setReleaseType(2); // 阶梯释放
        lockOrder.setDailyReleaseAmount(BigDecimal.ZERO); // 阶梯释放不设置每日金额
    } else {
        lockOrder.setReleaseType(3); // 一次性释放
        lockOrder.setDailyReleaseAmount(BigDecimal.ZERO);
    }
    
    lockOrder.setTotalDays(product.getDay()); // 使用产品配置的天数
    lockOrder.setStaticRate(product.getRate()); // 使用产品配置的收益率
    lockOrder.setEnableStatic(1);
    lockOrder.setEnableDynamic(1);
    lockOrder.setCompoundInterest(isBondCompoundInterest(product) ? 1 : 0); // 根据债券类型决定是否复利
    lockOrder.setStatus(1);
    lockOrder.setStartDate(new Date());
    lockOrder.setEndDate(DateUtils.addDays(new Date(), product.getDay()));
    
    lockOrderService.save(lockOrder);
}

// 辅助方法：判断债券是否线性释放
private boolean isBondLinearRelease(Product product) {
    // 根据产品属性或配置判断
    // 例如：产品名称包含"线性"或特定标识
    return product.getName().contains("线性") || 
           (product.getDescription() != null && product.getDescription().contains("LINEAR"));
}

// 辅助方法：判断债券是否阶梯释放
private boolean isBondStepRelease(Product product) {
    return product.getName().contains("阶梯") || 
           (product.getDescription() != null && product.getDescription().contains("STEP"));
}

// 辅助方法：判断债券是否复利
private boolean isBondCompoundInterest(Product product) {
    return product.getName().contains("复利") || 
           (product.getDescription() != null && product.getDescription().contains("COMPOUND"));
}

// 城主商品处理 - 不创建锁仓订单，增加4倍收益次数
private void processCityLordProduct(UserProduct userProduct, Product product, BigDecimal amount) {
    // 城主商品不创建锁仓订单，而是增加用户的4倍收益次数
    // 根据文档：每个城主商品可有两次普通质押静态收益*4权益
    
    // 1. 记录城主商品购买日志
    String remark = String.format("购买城主商品 - 订单ID: %d, 产品类型: 4", userProduct.getId());
    UserLog userLog = new UserLog();
    userLog.setUserId(userProduct.getUserId());
    userLog.setType(UserLogType.CITY_LORD_PURCHASE.getCode());
    userLog.setAmount(amount);
    userLog.setRemark(remark);
    userLog.setCreateTime(new Date());
    userLogService.save(userLog);
    
    // 2. 增加用户的4倍收益次数
    // 这里需要在用户相关表中记录或更新4倍收益次数
    // 具体实现需要根据现有的4倍收益次数存储机制
    addUserQuadrupleRewardTimes(userProduct.getUserId(), 2); // 每个城主商品增加2次
    
    log.info("用户购买城主商品，增加4倍收益次数，UID = {}, 商品 = {}, 次数 = 2", 
        userProduct.getUserId(), product.getName());
}


// 增加用户4倍收益次数
private void addUserQuadrupleRewardTimes(Long userId, int times) {
    // 根据现有系统的实现方式来增加4倍收益次数
    // 可能是在StakeUser表中有相关字段，或者在其他配置表中
    // 这里需要根据实际的数据结构来实现
    
    // 示例实现（需要根据实际情况调整）:
    // stakeUserService.addQuadrupleRewardTimes(userId, times);
}
```

#### 3.2.2 Job3改造

```java
@Component
@Slf4j
public class Job3 {
    
    @Resource
    private ILockOrderService lockOrderService;
    
    @Resource
    private IUserLogService userLogService;
    
    @Scheduled(cron = "0 0 8,20 * * ?") // 每天8点和20点执行
    public void run() {
        log.info("开始执行锁仓订单结算Job3");
        
        try {
            // 1. 处理不同类型的释放
            processAllReleaseTypes();
            
            // 2. 计算静态收益
            calculateStaticRewards();
            
            // 3. 计算动态收益
            calculateDynamicRewards();
            
            log.info("锁仓订单结算Job3执行完成");
        } catch (Exception e) {
            log.error("锁仓订单结算Job3执行失败", e);
        }
    }
    
    private void processAllReleaseTypes() {
        // 1. 处理线性释放
        processLinearRelease();
        
        // 2. 处理阶梯释放
        processStepRelease();
        
        // 3. 处理一次性释放
        processOneTimeRelease();
    }
    
    private void processLinearRelease() {
        List<LockOrder> lockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1) // 锁仓中
                .eq(LockOrder::getReleaseType, 1) // 线性释放
                .lt(LockOrder::getReleasedDays, LockOrder::getTotalDays) // 还未释放完成
        );
        
        for (LockOrder lockOrder : lockOrders) {
            processLinearLockOrderRelease(lockOrder);
        }
    }
    
    private void processStepRelease() {
        List<LockOrder> lockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1) // 锁仓中
                .eq(LockOrder::getReleaseType, 2) // 阶梯释放
                .lt(LockOrder::getReleasedDays, LockOrder::getTotalDays) // 还未释放完成
        );
        
        for (LockOrder lockOrder : lockOrders) {
            processStepLockOrderRelease(lockOrder);
        }
    }
    
    private void processOneTimeRelease() {
        List<LockOrder> lockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1) // 锁仓中
                .eq(LockOrder::getReleaseType, 3) // 一次性释放
                .ge(LockOrder::getReleasedDays, LockOrder::getTotalDays) // 已达到释放天数
        );
        
        for (LockOrder lockOrder : lockOrders) {
            processOneTimeLockOrderRelease(lockOrder);
        }
    }
    
    private void processLinearLockOrderRelease(LockOrder lockOrder) {
        // 线性释放逻辑
        BigDecimal dailyAmount = lockOrder.getDailyReleaseAmount();
        if (dailyAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 检查是否已达到总天数
            if (lockOrder.getReleasedDays() >= lockOrder.getTotalDays()) {
                // 已释放完成，无需继续处理
                if (lockOrder.getStatus() != 2) {
                    lockOrder.setStatus(2); // 标记为已完成
                    lockOrderService.updateById(lockOrder);
                }
                return;
            }
            
            BigDecimal newAvailable = lockOrder.getAvailableAmount().add(dailyAmount);
            BigDecimal newReleased = lockOrder.getReleasedAmount().add(dailyAmount);
            int newReleasedDays = lockOrder.getReleasedDays() + 1;
            
            // 检查是否释放完成
            if (newReleasedDays >= lockOrder.getTotalDays()) {
                // 最后一次释放，确保释放完全部金额
                BigDecimal remainingAmount = lockOrder.getLockAmount().subtract(lockOrder.getReleasedAmount());
                newAvailable = lockOrder.getAvailableAmount().add(remainingAmount);
                newReleased = lockOrder.getLockAmount();
                lockOrder.setStatus(2); // 已完成
            }
            
            lockOrder.setAvailableAmount(newAvailable);
            lockOrder.setReleasedAmount(newReleased);
            lockOrder.setReleasedDays(newReleasedDays);
            lockOrderService.updateById(lockOrder);
            
            // 记录释放日志
            recordLockOrderLog(lockOrder.getUserId(), 
                UserLogType.LOCK_RELEASE_LINEAR, dailyAmount, 
                String.format("锁仓订单线性释放，第%d天", newReleasedDays), 
                lockOrder.getId(), lockOrder.getSourceType());
        }
    }
    
    private void processStepLockOrderRelease(LockOrder lockOrder) {
        // 阶梯释放逻辑 - 根据lock_config配置处理
        // 需要解析JSON配置，计算当前应该释放的金额
        String lockConfig = lockOrder.getLockConfig();
        if (lockConfig != null) {
            // 解析阶梯配置并处理释放
            processStepReleaseByConfig(lockOrder, lockConfig);
        }
    }
    
    private void processStepReleaseByConfig(LockOrder lockOrder, String lockConfig) {
        // 解析JSON配置，根据当前已释放天数判断是否需要释放
        // 示例配置: {"steps": [{"day": 30, "percent": 30}, {"day": 60, "percent": 40}, {"day": 90, "percent": 30}]}
        
        int currentDay = lockOrder.getReleasedDays() + 1; // 当前要释放的天数
        
        // 这里需要根据具体的JSON配置格式来解析
        // 判断当前天数是否匹配某个阶梯释放点
        BigDecimal releaseAmount = calculateStepReleaseAmount(lockOrder, lockConfig, currentDay);
        
        if (releaseAmount.compareTo(BigDecimal.ZERO) > 0) {
            lockOrder.setAvailableAmount(lockOrder.getAvailableAmount().add(releaseAmount));
            lockOrder.setReleasedAmount(lockOrder.getReleasedAmount().add(releaseAmount));
            lockOrder.setReleasedDays(currentDay);
            
            // 检查是否释放完成
            if (currentDay >= lockOrder.getTotalDays()) {
                lockOrder.setStatus(2); // 已完成
            }
            
            lockOrderService.updateById(lockOrder);
            
            // 记录释放日志
            recordLockOrderLog(lockOrder.getUserId(), 
                UserLogType.LOCK_RELEASE_STEP, releaseAmount, 
                String.format("锁仓订单阶梯释放，第%d天", currentDay), 
                lockOrder.getId(), lockOrder.getSourceType());
        }
    }
    
    private BigDecimal calculateStepReleaseAmount(LockOrder lockOrder, String lockConfig, int currentDay) {
        // 根据配置和当前天数计算应释放金额
        // 这里需要根据实际的JSON配置格式来实现
        // 返回当前天数应该释放的金额，如果不是释放日则返回0
        return BigDecimal.ZERO; // 示例返回，实际需要根据配置计算
    }
    
    private void processOneTimeLockOrderRelease(LockOrder lockOrder) {
        // 一次性释放逻辑 - 到期全部释放
        BigDecimal remainingAmount = lockOrder.getLockAmount().subtract(lockOrder.getReleasedAmount());
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            lockOrder.setAvailableAmount(lockOrder.getAvailableAmount().add(remainingAmount));
            lockOrder.setReleasedAmount(lockOrder.getLockAmount());
            lockOrder.setReleasedDays(lockOrder.getTotalDays()); // 设置为总天数
            lockOrder.setStatus(2); // 已完成
            lockOrderService.updateById(lockOrder);
            
            // 记录释放日志
            recordLockOrderLog(lockOrder.getUserId(), 
                UserLogType.LOCK_RELEASE_ONETIME, remainingAmount, 
                "锁仓订单一次性释放", 
                lockOrder.getId(), lockOrder.getSourceType());
        }
    }
    
    private void calculateStaticRewards() {
        // 基于lock_order表计算静态收益
        // 只对启用静态收益的订单计算
        List<LockOrder> lockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1) // 锁仓中
                .eq(LockOrder::getEnableStatic, 1) // 启用静态收益
        );
        
        for (LockOrder lockOrder : lockOrders) {
            BigDecimal staticReward = calculateLockOrderStaticReward(lockOrder);
            if (staticReward.compareTo(BigDecimal.ZERO) > 0) {
                // 记录静态收益日志
                recordLockOrderLog(lockOrder.getUserId(), 
                    UserLogType.LOCK_STATIC_REWARD, staticReward, 
                    "锁仓静态收益", lockOrder.getId(), lockOrder.getSourceType());
            }
        }
    }
    
    private void calculateDynamicRewards() {
        // 基于lock_order表计算动态收益
        // 只对启用动态收益的订单计算
        List<LockOrder> lockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1) // 锁仓中
                .eq(LockOrder::getEnableDynamic, 1) // 启用动态收益
        );
        
        for (LockOrder lockOrder : lockOrders) {
            BigDecimal dynamicReward = calculateLockOrderDynamicReward(lockOrder);
            if (dynamicReward.compareTo(BigDecimal.ZERO) > 0) {
                // 记录动态收益日志
                recordLockOrderLog(lockOrder.getUserId(), 
                    UserLogType.LOCK_DYNAMIC_REWARD, dynamicReward, 
                    "锁仓动态收益", lockOrder.getId(), lockOrder.getSourceType());
            }
        }
    }
}
```

### 3.3 产品类型处理逻辑

基于现有Product表的字段，通过业务逻辑判断不同产品类型的锁仓规则：

```java
/**
 * 根据产品类型和属性创建锁仓订单
 */
private LockOrder createLockOrderByProduct(UserProduct userProduct, Product product, BigDecimal amount) {
    LockOrder lockOrder = new LockOrder();
    lockOrder.setOrderNo(generateLockOrderNo());
    lockOrder.setUserId(userProduct.getUserId());
    lockOrder.setSourceType(product.getType());
    lockOrder.setSourceId(userProduct.getId());
    lockOrder.setTokenId("XYC");
    lockOrder.setLockAmount(amount);
    
    // 根据产品类型设置锁仓规则
    switch (product.getType()) {
        case 1: // 节点产品
            setupNodeLockOrder(lockOrder, product);
            break;
        case 2: // 普通商品  
            setupNormalProductLockOrder(lockOrder, product);
            break;
        case 3: // 债券产品
            setupBondLockOrder(lockOrder, product);
            break;
        default:
            throw new IllegalArgumentException("不支持的产品类型: " + product.getType());
    }
    
    lockOrder.setStatus(1);
    lockOrder.setStartDate(new Date());
    lockOrder.setEndDate(DateUtils.addDays(new Date(), lockOrder.getTotalDays()));
    
    return lockOrder;
}

private void setupNodeLockOrder(LockOrder lockOrder, Product product) {
    lockOrder.setReleaseType(1); // 线性释放
    lockOrder.setTotalDays(product.getDay());
    lockOrder.setDailyReleaseAmount(
        lockOrder.getLockAmount().divide(new BigDecimal(product.getDay()), 8, RoundingMode.HALF_UP)
    );
    lockOrder.setStaticRate(product.getRate());
    lockOrder.setEnableStatic(1);
    lockOrder.setEnableDynamic(1);
    lockOrder.setCompoundInterest(1); // 节点产品复利
    lockOrder.setStatus(1);
    lockOrder.setReleasedDays(0); // 初始化已释放天数为0
}

private void setupNormalProductLockOrder(LockOrder lockOrder, Product product) {
    lockOrder.setReleaseType(1); // 线性释放
    lockOrder.setTotalDays(360); // 固定360天
    lockOrder.setDailyReleaseAmount(
        lockOrder.getLockAmount().divide(new BigDecimal(360), 8, RoundingMode.HALF_UP)
    );
    lockOrder.setStaticRate(getConfigStaticRate()); // 从配置获取
    lockOrder.setEnableStatic(1);
    lockOrder.setEnableDynamic(1);
    lockOrder.setCompoundInterest(0); // 普通商品不复利
    lockOrder.setStatus(1);
    lockOrder.setReleasedDays(0); // 初始化已释放天数为0
}

private void setupBondLockOrder(LockOrder lockOrder, Product product) {
    // 债券产品根据产品名称或描述判断释放类型
    if (isBondLinearRelease(product)) {
        lockOrder.setReleaseType(1); // 线性释放
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(product.getDay()), 8, RoundingMode.HALF_UP)
        );
    } else if (isBondStepRelease(product)) {
        lockOrder.setReleaseType(2); // 阶梯释放
        lockOrder.setDailyReleaseAmount(BigDecimal.ZERO);
        // 阶梯配置可以硬编码或从产品描述解析
        lockOrder.setLockConfig(generateStepConfig(product));
    } else {
        lockOrder.setReleaseType(3); // 一次性释放
        lockOrder.setDailyReleaseAmount(BigDecimal.ZERO);
    }
    
    lockOrder.setTotalDays(product.getDay());
    lockOrder.setStaticRate(product.getRate());
    lockOrder.setEnableStatic(1);
    lockOrder.setEnableDynamic(1);
    lockOrder.setCompoundInterest(isBondCompoundInterest(product) ? 1 : 0);
    lockOrder.setStatus(1);
    lockOrder.setReleasedDays(0); // 初始化已释放天数为0
}
```

### 3.4 数据迁移方案

由于当前收益还未开始发放，数据迁移相对简单，只需要将UserProduct的基本信息复制到LockOrder表：

```sql
-- 简化的数据迁移脚本
-- 将现有UserProduct数据复制到LockOrder表

INSERT INTO xyc.lock_order (
    order_no, user_id, source_type, source_id, token_id,
    lock_amount, released_amount, available_amount,
    release_type, total_days, daily_release_amount,
    static_rate, enable_static, enable_dynamic, compound_interest,
    status, released_days, create_time
)
SELECT 
    CONCAT('LOCK_', up.order_no) as order_no,
    up.user_id,
    up.type as source_type,
    up.id as source_id,
    'XYC' as token_id,
    up.amount as lock_amount,
    0 as released_amount,  -- 还未开始释放
    0 as available_amount, -- 还未有可提取金额
    1 as release_type,     -- 默认线性释放
    CASE 
        WHEN up.type = 1 THEN up.day        -- 节点产品使用产品天数
        WHEN up.type = 2 THEN 360           -- 普通商品固定360天
        WHEN up.type = 3 THEN up.day        -- 债券产品使用产品天数
        ELSE up.day 
    END as total_days,
    CASE 
        WHEN up.type = 1 THEN up.amount / up.day
        WHEN up.type = 2 THEN up.amount / 360
        WHEN up.type = 3 THEN up.amount / up.day
        ELSE up.amount / up.day
    END as daily_release_amount,
    COALESCE(up.rate, 0.004) as static_rate,
    1 as enable_static,
    1 as enable_dynamic,
    CASE 
        WHEN up.type = 1 THEN 1  -- 节点产品复利
        ELSE 0                   -- 其他产品不复利
    END as compound_interest,
    1 as status,  -- 锁仓中
    0 as released_days,  -- 还未开始释放，初始化为0
    up.create_time
FROM xyc.user_product up
WHERE up.type IN (1, 2, 3)    -- 迁移节点、普通商品、债券产品（城主商品type=4不生成锁仓订单）
  AND up.status = 1;          -- 只迁移有效订单
```

## 4. 新增服务接口

### 4.1 LockOrderService

```java
public interface ILockOrderService extends IService<LockOrder> {
    
    /**
     * 创建锁仓订单
     */
    LockOrder createLockOrder(CreateLockOrderRequest request);
    
    /**
     * 处理锁仓订单释放
     */
    void processRelease(Long lockOrderId, Date releaseDate);
    
    /**
     * 计算静态收益
     */
    BigDecimal calculateStaticReward(Long lockOrderId, Date rewardDate);
    
    /**
     * 获取用户锁仓总额
     */
    BigDecimal getUserTotalLockAmount(Long userId, String tokenId);
    
    /**
     * 获取用户可提取金额
     */
    BigDecimal getUserAvailableAmount(Long userId, String tokenId);
    
    /**
     * 获取用户锁仓收益统计
     */
    LockOrderRewardSummary getUserLockRewardSummary(Long userId, Date startDate, Date endDate);
}

/**
 * 锁仓收益统计VO
 */
public class LockOrderRewardSummary {
    private BigDecimal totalStaticReward;    // 总静态收益
    private BigDecimal totalDynamicReward;   // 总动态收益
    private BigDecimal totalReleaseAmount;   // 总释放金额
    private BigDecimal totalLockAmount;      // 总锁仓金额
    private BigDecimal availableAmount;      // 可提取金额
    
    // 按产品类型分组统计
    private Map<Integer, ProductTypeReward> rewardByType;
    
    // getter/setter...
}

public class ProductTypeReward {
    private Integer sourceType;              // 产品类型
    private BigDecimal staticReward;         // 静态收益
    private BigDecimal dynamicReward;        // 动态收益
    private BigDecimal releaseAmount;        // 释放金额
    private BigDecimal lockAmount;           // 锁仓金额
    
    // getter/setter...
}
```

### 4.2 UserLogType枚举扩展

```java
public enum UserLogType {
    // ... 现有类型
    
    // 锁仓释放相关
    LOCK_RELEASE_LINEAR(201, "线性释放"),
    LOCK_RELEASE_STEP(202, "阶梯释放"),
    LOCK_RELEASE_ONETIME(203, "一次性释放"),
    
    // 锁仓收益相关
    LOCK_STATIC_REWARD(211, "静态收益"),
    LOCK_DYNAMIC_REWARD(212, "动态收益"),
    LOCK_COMPOUND_REWARD(213, "复利收益"),
    
    // 锁仓状态变更
    LOCK_ORDER_CREATE(221, "锁仓订单创建"),
    LOCK_ORDER_COMPLETE(222, "锁仓订单完成"),
    LOCK_ORDER_CANCEL(223, "锁仓订单取消"),
    
    // 产品购买相关
    PRODUCT_PURCHASE(230, "购买产品"),
    
    // 城主商品相关
    CITY_LORD_PURCHASE(231, "购买城主商品"),
    QUADRUPLE_REWARD_ADD(232, "增加4倍收益次数"),
    QUADRUPLE_REWARD_USE(233, "使用4倍收益次数");
    
    private final int code;
    private final String description;
    
    UserLogType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    // getter methods...
}
```

### 4.3 UserLog使用方式

使用现有的UserLog构造方法，通过remark字段记录详细信息：

```java
// 锁仓相关日志记录示例
public void recordLockOrderLog(Long userId, UserLogType type, BigDecimal amount, 
                              String operation, Long lockOrderId, Integer sourceType) {
    String remark = String.format("%s - 锁仓订单ID: %d, 产品类型: %d", 
                                 operation, lockOrderId, sourceType);
    
    UserLog userLog = new UserLog();
    userLog.setUserId(userId);
    userLog.setType(type.getCode());
    userLog.setAmount(amount);
    userLog.setRemark(remark);
    userLog.setCreateTime(new Date());
    
    userLogService.save(userLog);
}
```

## 5. 实施步骤

### 阶段一：表结构和基础代码
1. 确保Product表有sold字段，创建相关索引
2. 创建lock_order表
3. 创建相关索引（无需修改user_log表）

### 阶段二：服务层开发
1. 开发LockOrder实体类和Mapper
2. 开发LockOrderService接口和实现
3. 扩展UserLogType枚举，添加锁仓相关类型
4. 开发锁仓日志记录工具方法

### 阶段三：Job3逻辑改造
1. 重写Job3，基于LockOrder表进行计算
2. 实现不同释放类型的处理逻辑
3. 集成user_log记录功能
4. 单元测试验证

### 阶段四：购买流程改造
1. 修改商品购买流程，同时创建UserProduct和LockOrder
2. 集成Product表sold字段更新逻辑
3. 根据产品类型设置不同的锁仓规则
4. 测试验证购买流程

### 阶段五：数据迁移和上线
1. 执行简化的数据迁移脚本
2. 验证迁移数据的完整性
3. 切换Job3到新逻辑
4. 监控系统运行状态

## 6. 优势分析

### 6.1 职责分离
- **UserProduct**: 专注于购买记录管理
- **LockOrder**: 专注于锁仓和收益计算
- **LockOrderReward**: 专注于收益记录和统计

### 6.2 扩展性增强
- 支持多种释放类型（线性、阶梯、一次性）
- 支持不同的收益计算规则
- 支持多种来源的锁仓订单

### 6.3 性能优化
- 专门的索引优化查询性能
- 收益计算逻辑更加清晰高效
- 支持分页和批量处理

### 6.4 数据完整性
- 通过user_log表完整记录所有锁仓操作
- 支持按类型查询和统计
- 数据一致性保障
- 便于审计和对账

## 7. 产品类型总结

### 7.1 支持的产品类型

| 产品类型 | type值 | 默认锁仓天数 | 释放方式 | 复利 | 静态收益 | 动态收益 | 备注 |
|---------|--------|-------------|----------|------|----------|----------|------|
| 节点产品 | 1 | 产品配置 | 线性释放 | 是 | 是 | 是 | 根据产品day字段 |
| 普通商品 | 2 | 360天 | 线性释放 | 否 | 是 | 是 | 固定360天线性释放 |
| 债券产品 | 3 | 产品配置 | 可配置 | 可配置 | 可配置 | 可配置 | 最灵活的产品类型 |
| 城主商品 | 4 | 不锁仓 | 不适用 | 不适用 | 不适用 | 不适用 | 增加4倍收益次数，不生成锁仓订单 |

### 7.3 城主商品特殊处理

城主商品(type=4)是一个特殊的产品类型，它不生成锁仓订单，而是为用户增加4倍收益次数：

#### 功能特点
- **不生成锁仓订单**: 购买后只记录在UserProduct表中
- **增加收益倍数**: 每个城主商品增加2次4倍收益权益
- **收益增强**: 用户在获得静态收益时可以使用4倍收益次数

#### 实现逻辑
```java
// 城主商品购买流程
public void purchaseCityLordProduct(User user, Product product, BigDecimal amount) {
    // 1. 创建购买记录（正常流程）
    UserProduct userProduct = new UserProduct(user.getId(), orderNo, product, amount);
    userProductService.save(userProduct);
    
    // 2. 不创建锁仓订单，而是增加4倍收益次数
    processCityLordProduct(userProduct, product, amount);
}

// 在收益计算时使用4倍收益
private BigDecimal calculateStaticRewardWithMultiplier(LockOrder lockOrder, BigDecimal baseReward) {
    // 检查用户是否有4倍收益次数
    int quadrupleTimes = getUserQuadrupleRewardTimes(lockOrder.getUserId());
    
    if (quadrupleTimes > 0) {
        // 使用一次4倍收益
        useQuadrupleRewardTimes(lockOrder.getUserId(), 1);
        
        // 记录使用日志
        userLogService.save(new UserLog(lockOrder.getUserId(), 
            UserLogType.QUADRUPLE_REWARD_USE, baseReward.multiply(new BigDecimal("3")), 
            "使用4倍收益次数", lockOrder.getId(), lockOrder.getSourceType()));
        
        return baseReward.multiply(new BigDecimal("4")); // 4倍收益
    }
    
    return baseReward; // 正常收益
}
```

#### 数据存储
4倍收益次数的存储可能在以下位置（需要根据现有实现确认）：
- StakeUser表中的某个字段
- 独立的用户权益表
- 或者通过user_log表的记录来计算剩余次数


#### 线性债券
```json
{
  "type": "linear",
  "description": "180天线性释放债券"
}
```

#### 阶梯债券
```json
{
  "type": "step",
  "steps": [
    {"day": 30, "percent": 30, "description": "第30天释放30%"},
    {"day": 60, "percent": 40, "description": "第60天释放40%"},
    {"day": 90, "percent": 30, "description": "第90天释放30%"}
  ]
}
```

#### 定期债券
```json
{
  "type": "fixed",
  "maturity_day": 365,
  "description": "365天到期一次性释放"
}
```

## 8. 风险控制

### 8.1 数据一致性
- 双写期间保持数据同步
- 定期数据校验和修复
- 回滚方案准备
- 关键业务数据备份

### 8.2 性能影响
- 分批迁移数据，避免长时间锁表
- 监控系统性能指标
- 准备降级方案
- 数据库连接池优化

### 8.3 业务连续性
- 灰度发布，逐步切换
- 保持旧接口兼容性
- 24小时监控和支持
- 紧急回滚预案

### 8.4 配置管理
- 产品配置变更审批流程
- 锁仓规则配置版本管理
- 配置变更影响评估
- 配置回滚机制

## 9. 总结

这个锁仓订单表的设计方案具有以下特点：

1. **统一管理**: 所有类型的锁仓订单都在一个表中管理，便于统计和查询
2. **逻辑清晰**: 基于现有Product表字段，通过业务逻辑判断锁仓规则
3. **简化实施**: 不需要修改Product表结构，数据迁移简单直接
4. **职责分离**: UserProduct专注购买记录，LockOrder专注锁仓逻辑和收益计算
5. **日志完整**: 通过user_log表记录所有锁仓操作，便于审计和查询
6. **扩展性强**: 支持多种释放类型，可以灵活应对不同产品需求

### 核心优势

- **最小化改动**: 复用现有表结构，减少系统改动风险
- **业务导向**: 重点关注逻辑改造，而非数据结构调整  
- **快速实施**: 由于收益未发放，数据迁移简单，可以快速上线
- **向后兼容**: 保持UserProduct表的完整性，不影响现有功能

通过这个改造方案，系统将能够更好地支持节点产品、普通商品和债券产品的锁仓管理，同时保持实施的简洁性和可靠性。