package com.aic.app.job;

import com.aic.app.mapper.PhoneUserRelationMapper;
import com.aic.app.model.PhoneUserRelation;
import com.aic.app.model.User;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class Job3 {

    @Resource
    private PhoneUserRelationMapper phoneUserRelationMapper;
    // cache
    final private Map<String, String> pathCache = new HashMap<>();
    final private Map<String, PhoneUserRelation> userCache = new HashMap<>();

    @Scheduled(cron = "*/15 * * * * ?")
    public void run() {
        int count = phoneUserRelationMapper.syncUsers();
        log.info("sync relation count:{}", count);

        List<User> syncUsers2 = phoneUserRelationMapper.findUnSyncUsers2();
        log.info("sync relation change count:{}", syncUsers2.size());
        for (User user : syncUsers2) {
            phoneUserRelationMapper.update(new LambdaUpdateWrapper<PhoneUserRelation>()
                    .set(PhoneUserRelation::getPid, user.getPid())
                    .set(PhoneUserRelation::getPath, null)
                    .eq(PhoneUserRelation::getId, user.getId()));
            phoneUserRelationMapper.update(new LambdaUpdateWrapper<PhoneUserRelation>()
                    .set(PhoneUserRelation::getPath, null)
                    .like(PhoneUserRelation::getPath, "/" + user.getId() + "/"));
        }

        List<PhoneUserRelation> users = phoneUserRelationMapper.findUnSyncUsers();
        if (users.isEmpty()) {
            return;
        }
        for (PhoneUserRelation user : users) {
            userCache.put(user.getId(), user);
        }
        for (PhoneUserRelation user : users) {
            log.info("sync: {}", user);
            setPath(user);
        }

        pathCache.clear();
        userCache.clear();
    }
    
    public void setPath(PhoneUserRelation user) {
        if (user.getPath() == null) {
            String parentPath = getParentPath(user.getPid());
            if (parentPath != null) {
                pathCache.put(user.getPid(), parentPath);
            }
            String path = parentPath + "/" + user.getId();
            user.setPath(path);
            phoneUserRelationMapper.update(new UpdateWrapper<PhoneUserRelation>().set("path", path).eq("id", user.getId()));
        }
    }
    
    private String getParentPath(String parentId) {
        if (pathCache.containsKey(parentId)) {
            return pathCache.get(parentId);
        }
        StringBuilder pathBuilder = new StringBuilder();
        
        Set<String> idSet = new HashSet<>();
        while (parentId != null && !parentId.isEmpty()) {
            if (idSet.contains(parentId)) {
                break;
            }

            PhoneUserRelation parent = userCache.get(parentId);
            if (parent == null) {
                parent = phoneUserRelationMapper.selectById(parentId);
            }
            if (parent == null) {
                break;
            }

            idSet.add(parentId);

            if (parent.getPath() != null) {
                pathBuilder.insert(0, parent.getPath());
                break;
            } else {
                pathBuilder.insert(0, "/" + parentId);
            }
            parentId = parent.getPid();
        }
        if (idSet.isEmpty()) {
            return "";
        }
        return pathBuilder.toString();
    }

}
