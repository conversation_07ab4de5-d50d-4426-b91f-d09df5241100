# /api/stake 接口实现文档

## 概述

实现了 `/api/stake` 接口，用于获取质押相关数据，包括全网统计数据和用户个人数据。

## 接口详情

### 请求信息
- **URL**: `/api/stake`
- **方法**: `GET`
- **认证**: 可选（未登录用户返回个人数据为0）

### 响应数据结构

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "totalStakeAmount": 1000000,      // 全网总质押数量
    "currentIndex": 1.5,              // 当前指数
    "stakePrincipal": 800000,         // 质押本金
    "totalInterest": 200000,          // 总利息
    "apy": 0.15,                      // APY（年化收益率）
    "myStakeAmount": 0,               // 我的质押总量（已确认）
    "pendingAmount": 0,               // 待确认数量
    "stakeStaticPool": 0,             // 质押静态池
    "stakeDynamicPool": 0,            // 质押动态池
    "lockStaticPool": 0,              // 锁仓静态池
    "lockDynamicPool": 0              // 锁仓动态池
  }
}
```

## 数据来源

### 全网数据（从Redis获取）
- **Redis Key**: `xyc:stats`
- **字段映射**:
  - `totalStakeAmount` → 全网总质押数量
  - `currentIndex` → 当前指数
  - `stakePrincipal` → 质押本金
  - `totalInterest` → 总利息
  - `apy` → APY

### 用户个人数据（从数据库获取）
- **数据源**: `stake_user` 表
- **字段映射**:
  - `current_amount` → 我的质押总量（已确认）
  - `pending_amount` → 待确认数量
  - `static_pool` → 质押静态池
  - `dynamic_pool` → 质押动态池
  - `lock_static_pool` → 锁仓静态池
  - `lock_dynamic_pool` → 锁仓动态池

## 实现变更

### 1. 数据库变更
- 在 `stake_user` 表中新增了两个字段：
  - `lock_static_pool` DECIMAL(25,8) - 锁仓静态池
  - `lock_dynamic_pool` DECIMAL(25,8) - 锁仓动态池
- 创建了数据库迁移脚本：`V4__Add_Lock_Pool_Fields_To_StakeUser.sql`

### 2. 代码变更
- **StakeUser.java**: 添加了 `lockStaticPool` 和 `lockDynamicPool` 字段
- **IndexController.java**: 更新了 `/api/stake` 接口实现
  - 从Redis获取全网统计数据
  - 从数据库获取用户个人数据
  - 支持未登录用户访问（个人数据返回0）

### 3. 测试
- 更新了 `StakeApiTest.java` 测试用例
- 验证了Redis数据获取功能
- 验证了未登录和已登录用户的不同响应

## 特性

1. **容错处理**: Redis数据获取失败时返回0值，不影响接口正常响应
2. **用户友好**: 未登录用户也能查看全网数据，个人数据显示为0
3. **数据一致性**: 所有BigDecimal字段都有null值保护，确保返回数据的一致性
4. **性能优化**: 直接从StakeUser表读取锁仓池数据，避免复杂计算

## 使用示例

### 未登录用户
```bash
curl -X GET "http://localhost:8094/api/stake"
```

### 已登录用户
```bash
curl -X GET "http://localhost:8094/api/stake" \
  -H "Authorization: Bearer your-token-here"
```

## 注意事项

1. Redis中的 `xyc:stats` 数据需要通过其他系统或定时任务维护
2. 锁仓池数据需要通过业务逻辑更新到 `stake_user` 表中
3. 接口支持GET请求，符合查询接口的RESTful设计原则
