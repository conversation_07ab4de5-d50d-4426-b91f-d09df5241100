# 统一提取接口实现文档

## 概述

根据 `doc/0720接口.md` 的需求，实现了统一的提取接口 `/api/user/withdraw`，支持不同类型的资金提取。

## 接口详情

### 请求路径
```
POST /api/user/withdraw
```

### 请求参数
```json
{
  "type": 0,           // 提取类型：0-4
  "amount": "100.00",  // 提取金额
  "id": 123,           // 锁仓记录ID（仅type=0时需要）
  "tokenId": "XYC"     // 代币ID，默认XYC
}
```

### 提取类型说明

| type | 说明 | 必需参数 | 数据来源 |
|------|------|----------|----------|
| 0 | 提取锁仓释放 | id（锁仓记录ID） | LockOrder.availableAmount |
| 1 | 提取质押静态池 | - | StakeUser.staticPool |
| 2 | 提取质押动态池 | - | StakeUser.dynamicPool |
| 3 | 提取锁仓静态池 | - | 锁仓静态收益池（暂未实现） |
| 4 | 提取锁仓动态池 | - | 锁仓动态收益池（暂未实现） |

### 响应格式
```json
{
  "success": true,
  "data": {
    "address": "0x...",
    "token": "0x...",
    "to": "0x...",
    "amount": "100000000000000000000",
    "deadline": 1642694400,
    "sign": "0x...",
    "type": 6,
    "nonce": 123456
  },
  "message": "操作成功"
}
```

## 实现细节

### 1. 新增文件

#### WithdrawUnifiedForm.java
- 统一提取请求表单类
- 包含类型验证和参数校验

#### WithdrawUnifiedTest.java
- 服务层单元测试
- 测试不同提取类型的业务逻辑

#### WithdrawApiTest.java
- API层集成测试
- 测试接口调用和响应格式

### 2. 扩展的服务接口

#### ILockOrderService
新增方法：
- `withdrawLockOrderRelease()` - 提取锁仓释放
- `getUserLockStaticPool()` - 获取锁仓静态池余额
- `getUserLockDynamicPool()` - 获取锁仓动态池余额
- `withdrawLockStaticPool()` - 提取锁仓静态池
- `withdrawLockDynamicPool()` - 提取锁仓动态池

#### IStakeUserService
新增方法：
- `withdrawStakeStaticPool()` - 提取质押静态池
- `withdrawStakeDynamicPool()` - 提取质押动态池

### 3. Withdraw记录类型扩展

新增提取类型：
- 6: 锁仓释放提取
- 7: 质押静态池提取
- 8: 质押动态池提取
- 9: 锁仓静态池提取
- 10: 锁仓动态池提取

### 4. 业务流程

1. **参数验证**：验证提取类型、金额、必需参数
2. **余额检查**：根据类型检查对应池子的可用余额
3. **扣减操作**：从对应池子扣减提取金额
4. **签名生成**：调用RustService生成提取签名
5. **记录创建**：创建Withdraw提取记录
6. **日志记录**：记录用户操作日志

## 安全考虑

1. **用户权限验证**：确保只能提取自己的资金
2. **余额检查**：防止超额提取
3. **原子操作**：使用事务确保数据一致性
4. **参数校验**：严格验证输入参数
5. **错误处理**：完善的异常处理机制

## 测试覆盖

1. **单元测试**：测试各个服务方法的业务逻辑
2. **集成测试**：测试API接口的完整流程
3. **边界测试**：测试无效参数和边界条件
4. **异常测试**：测试各种异常情况的处理

## 部署说明

1. 确保数据库表结构完整（lock_order, stake_user, withdraw, user_log）
2. 确保RustService的JNI库正常加载
3. 配置正确的合约地址和Token地址
4. 测试各种提取类型的功能

## 后续扩展

1. **锁仓静态池/动态池**：完善type=3,4的具体实现
2. **手续费支持**：添加提取手续费计算
3. **限额控制**：添加单日提取限额
4. **多币种支持**：支持除XYC外的其他代币
5. **批量提取**：支持一次提取多个订单

## 注意事项

1. type=3,4（锁仓静态池/动态池）目前返回错误，需要根据具体业务需求实现
2. 所有金额计算使用BigDecimal避免精度问题
3. 提取签名使用Wei单位（乘以1e18）
4. 确保与前端的错误处理机制对接
