
1. 质押功能，由链上事件触发，本地直接新增质押跟解除质押数据记录，由于事件还没有定义，所以直接加上Service接口即可

2. 质押每天结算两次，早晚8点执行一次，T+1结算，就是说先今日待确认池，结算一次后进入质押的池子，不用等具体的时间

2. 质押静态收益1%可配置，SysConfig.rewardRate 已经定义好了

4. 质押动态奖励分两种：分享奖励、社区奖励

* 分享奖励

| 个人质押数量 | 直推有效用户 | 获奖维度 | 奖励  |
|--------|--------|------|-----|
| 100U   | 1      | 1    | 12% |
| 300U   | 3      | 3    | 9%  |
| 1000U  | 6      | 6    | 5%  |
| 2000U  | 10     | 10   | 2%  |

下级参与质押100个及以上算一个有效用户

* 社区奖励

| 级别 | 个人质押    | 社区   | 奖励  | 平级 | 团队   |
|----|---------|------|-----|----|------|
| V1 | 100U    | 1W   | 10% |    |      |
| V2 | 1000U   | 3W   | 20% |    | 2个V1 |
| V3 | 5000U   | 10W  | 35% |    | 2个V2 |
| V4 | 20000U  | 30W  | 50% | 5% | 3个V3 |
| V5 | 50000U  | 100W | 60% | 5% | 3个V4 |
| V6 | 100000U | 300W | 70% | 5% | 3个V5 |

5、静态复利、动态不复利

6、社区奖励有烧伤

7、静态、动态都到池子里面

