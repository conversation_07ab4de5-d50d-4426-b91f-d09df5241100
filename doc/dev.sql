alter table xyc.user_product add column daily_release_amount decimal(25,8) default 0 comment '每日释放金额';
alter table xyc.user_product add column total_released decimal(25,8) default 0 comment '已释放总金额';
alter table xyc.user_product add column available_amount decimal(25,8) default 0 comment '可提取金额(已释放未提取)';
alter table xyc.user_product add column last_release_date date comment '最后释放日期';
alter table xyc.user_log add column token_id varchar(32) null comment 'TOKEN';

-- auto-generated definition
create table stake_user
(
    id             int auto_increment
        primary key,
    user_id        int                                      not null comment '用户ID',
    token_id       varchar(32)                              not null comment '资产ID',
    current_amount decimal(25, 8) default 0.00000000        not null comment '活期限额',
    pending_amount decimal(25, 8) default 0.00000000        not null comment '待确认',
    static_pool    decimal(25, 8) default 0.00000000        not null comment '静态池子',
    dynamic_pool   decimal(25, 8) default 0.00000000        not null comment '动态池子',
    today_static   decimal(25, 8) default 0.00000000        not null comment '今日静态收益',
    total_static   decimal(25, 8) default 0.00000000        not null comment '累计静态收益',
    today_dynamic  decimal(25, 8) default 0.00000000        not null comment '今日动态收益',
    total_dynamic  decimal(25, 8) default 0.00000000        not null comment '累计动态收益',
    today_buy      decimal(25, 8) default 0.00000000        not null comment '今日购买',
    total_buy      decimal(25, 8) default 0.00000000        not null comment '累计购买',
    can_receive    decimal(25, 8) default 0.00000000        not null comment '可领取',
    week_dynamic   decimal(25, 8) default 0.00000000        not null comment '周分红',
    team_perf      decimal(25, 8) default 0.00000000        not null comment '团队业绩',
    old_team_perf  decimal(25, 8) default 0.00000000        not null comment '上周团队业绩',
    max_team_perf  decimal(25, 8) default 0.00000000        not null comment '历史最大团队业绩',
    node           int            default 0                 not null comment '节点数量',
    node_perf      decimal(25, 8) default 0.00000000        not null comment '节点业绩',
    max_node_perf  decimal(25, 8) default 0.00000000        not null comment '历史最大节点业绩',
    node_reward    decimal(25, 8) default 0.00000000        not null comment '节点返佣',
    node_pool      decimal(25, 8) default 0.00000000        not null comment '节点池',
    sum_amount     decimal(25, 8) default 0.00000000        not null comment '伞下业绩',
    stake_first    tinyint(1)     default 0                 not null comment '是否完成首次质押',
    stake_limit    decimal(25, 8) default 0.00000000        not null comment '质押额度',
    burn_limit     decimal(25, 8) default 0.00000000        not null comment '销毁额度',
    create_time    datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time    datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
)
    comment '质押用户表';


create table sys_config
(
    id          int auto_increment
        primary key,
    reward_rate decimal(25, 8) not null comment '金额'
)
    comment '系统配置表';

-- auto-generated definition
drop table if exists user_stake;
create table user_stake
(
    id          int auto_increment
        primary key,
    type        int                                      not null comment '类型：0-质押 1-解除质押',
    user_id     int                                      not null comment '用户ID',
    token_id    varchar(32)                              not null comment 'TOKEN',
    quantity    decimal(25, 8) default 0.00000000        not null comment '数量',
    usd_amount DECIMAL(25,8) DEFAULT 0.00000000 NOT NULL COMMENT 'USD金额',
    txid        varchar(66)                              null comment '交易号',
    create_time datetime                                 not null comment '创建时间',
    update_time datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    status      bigint         default 0                 not null comment '0-待确认 1-有效'
)
    comment '用户质押记录';

-- auto-generated definition
create table withdraw
(
    id          int auto_increment
        primary key,
    type        int                                not null comment '类型：0 提币、 1 释放、 2 提周分红',
    user_id     int                                not null comment '用户ID',
    token_id    varchar(32)                        not null comment 'TOKEN',
    nonce       int                                not null comment '随机数',
    contract    varchar(66)                        null,
    address     varchar(66)                        null,
    token       varchar(66)                        null,
    sign        text                               null,
    product_id  int                                null,
    quantity    decimal(25, 8)                     null,
    fee         varchar(128)                       null,
    txid        varchar(128)                       null,
    data        text                               null,
    state       int                                null comment '状态 0-待确认 1-成功 2-失败',
    err         varchar(64)                        null,
    deadline    bigint                             null,
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
);

