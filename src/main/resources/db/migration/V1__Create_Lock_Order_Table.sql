-- 检查并添加Product表的sold字段（如果不存在）
ALTER TABLE product ADD COLUMN sold DECIMAL(25,8) DEFAULT 0 COMMENT '已售数量';

-- 创建索引优化查询
CREATE INDEX idx_product_sold ON product(sold);

-- 创建锁仓订单表
DROP TABLE IF EXISTS lock_order;
CREATE TABLE lock_order (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(64) NOT NULL COMMENT '锁仓订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    source_type TINYINT NOT NULL COMMENT '来源类型: 1-节点产品 2-普通商品 3-债券产品 4-质押 5-其他',
    source_id BIGINT NULL COMMENT '来源ID(user_product.id或其他业务ID)',
    token_id VARCHAR(32) NOT NULL COMMENT '代币ID',
    
    -- 锁仓基本信息
    lock_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '锁仓总金额',
    released_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '已释放金额',
    available_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '可提取金额(已释放未提取)',
    
    -- 释放规则
    total_days INT NOT NULL COMMENT '总锁仓天数',
    released_days INT NOT NULL DEFAULT 0 COMMENT '已释放天数',
    daily_release_amount DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '每日释放金额',
    
    -- 状态和释放计数
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-待激活 1-锁仓中 2-已完成 3-已取消',
    
    -- 扩展配置
    lock_config JSON NULL COMMENT '锁仓配置(阶梯释放规则等)',
    
    -- 审计字段
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_order_no (order_no),
    KEY idx_user_id (user_id),
    KEY idx_status (status),
    KEY idx_token_id (token_id),
    KEY idx_source (source_type, source_id),
    KEY idx_released_days (released_days)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='锁仓订单表';