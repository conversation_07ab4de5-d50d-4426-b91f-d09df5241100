-- 简化的数据迁移脚本
-- 将现有UserProduct数据复制到LockOrder表

INSERT INTO lock_order (
    order_no, user_id, source_type, source_id, token_id,
    lock_amount, released_amount, available_amount,
    release_type, total_days, daily_release_amount,
    static_rate, enable_static, enable_dynamic, compound_interest,
    status, released_days, create_time
)
SELECT 
    CONCAT('LOCK_', up.order_no) as order_no,
    up.user_id,
    up.type as source_type,
    up.id as source_id,
    'XYC' as token_id,
    up.amount as lock_amount,
    0 as released_amount,  -- 还未开始释放
    0 as available_amount, -- 还未有可提取金额
    1 as release_type,     -- 默认线性释放
    CASE 
        WHEN up.type = 1 THEN up.day        -- 节点产品使用产品天数
        WHEN up.type = 2 THEN 360           -- 普通商品固定360天
        WHEN up.type = 3 THEN up.day        -- 债券产品使用产品天数
        ELSE up.day 
    END as total_days,
    CASE 
        WHEN up.type = 1 THEN up.amount / up.day
        WHEN up.type = 2 THEN up.amount / 360
        WHEN up.type = 3 THEN up.amount / up.day
        ELSE up.amount / up.day
    END as daily_release_amount,
    COALESCE(up.rate, 0.004) as static_rate,
    1 as enable_static,
    1 as enable_dynamic,
    CASE 
        WHEN up.type = 1 THEN 1  -- 节点产品复利
        ELSE 0                   -- 其他产品不复利
    END as compound_interest,
    1 as status,  -- 锁仓中
    0 as released_days,  -- 还未开始释放，初始化为0
    up.create_time
FROM user_product up
WHERE up.type IN (1, 2, 3)    -- 迁移节点、普通商品、债券产品（城主商品type=4不生成锁仓订单）
  AND up.status = 1;          -- 只迁移有效订单