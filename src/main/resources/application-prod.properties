#spring.profiles.default=prod

spring.application.name=app
server.port=8094

# \u8BBE\u7F6E\u6700\u5927\u7EBF\u7A0B\u6570\uFF08\u6839\u636E\u5B9E\u9645\u9700\u6C42\u8C03\u4F18\uFF09
server.tomcat.threads.max=1000
# \u8BBE\u7F6E\u6700\u5927\u8FDE\u63A5\u6570
server.tomcat.accept-count=500
# \u8BBE\u7F6E\u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4
server.tomcat.connection-timeout=20000
# \u8BBE\u7F6E Keep-Alive \u8D85\u65F6\u65F6\u95F4
server.tomcat.keep-alive-timeout=15000
server.error.whitelabel.enabled=false


spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Druid ????
spring.datasource.druid.initial-size=5
spring.datasource.druid.max-active=100
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-wait=60000
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.validation-query=SELECT 1
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000

# Druid 
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/admin/druid/*
spring.datasource.druid.stat-view-servlet.login-username=admin
spring.datasource.druid.stat-view-servlet.login-password=admin+888
spring.datasource.druid.stat-view-servlet.reset-enable=false

# Druid Web 
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.web-stat-filter.url-pattern=/*
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*


#spring.datasource.hikari.connection-test-query=SELECT 1
#spring.datasource.hikari.minimum-idle=5
#spring.datasource.hikari.maximum-pool-size=100
#spring.datasource.hikari.idleTimeout=30000
#spring.datasource.hikari.connectionTimeout=30000
#spring.datasource.hikari.leakDetectionThreshold=5000

# swagger-ui custom path
springdoc.swagger-ui.path=/swagger-ui.html

spring.datasource.url=*******************************
spring.datasource.username=root
spring.datasource.password=dbadmin123
spring.data.redis.host=127.0.0.1
spring.data.redis.database=0
spring.data.redis.port=6379

#logging.pattern.level= %5p [%t] %-40.40logger{39} : %m%n
#logging.level.com.aic.app.mapper = debug
#logging.level.com.aic.app = debug
mybatis.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl

spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB


jc.host=https://api.jucoin.vc

app.open=false
app.hello====\u542F\u52A8\u73AF\u5883\uFF1A\u6B63\u5F0F====
app.initBalance=0


router.address=0xc585461F617c737934CAD3608F43a87258EBa59b
referral.address=0xCD69352A989711A7786fcf296f48A36f932DDc72