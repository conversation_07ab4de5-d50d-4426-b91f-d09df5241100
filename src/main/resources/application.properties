#spring.profiles.default=prod
#spring.profiles.default=test
spring.application.name=app
server.port=8094

#spring.main.allow-circular-references=true

# swagger-ui custom path
springdoc.swagger-ui.path=/swagger-ui.html


spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

# Druid ??????
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/admin/druid/*
spring.datasource.druid.stat-view-servlet.login-username=admin
spring.datasource.druid.stat-view-servlet.login-password=admin+888
spring.datasource.druid.stat-view-servlet.reset-enable=false

# Druid Web ???????
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.web-stat-filter.url-pattern=/*
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*

#spring.datasource.url=******************************************************************************************************
spring.datasource.url=*******************************
spring.datasource.username=root
spring.datasource.password=dbadmin123
spring.data.redis.host=127.0.0.1
spring.data.redis.database=0
spring.data.redis.port=6379

#logging.pattern.level= %5p [%t] %-40.40logger{39} : %m%n
logging.level.com.aic.app.mapper = debug
logging.level.com.aic.app.mapper.UserRelationMapper = INFO
logging.level.com.aic.app = debug
mybatis.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl

spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

jc.host=https://api.jucoin.vc

app.open=true
app.hello====\u542F\u52A8\u73AF\u5883\uFF1A\u5F00\u53D1====
app.initBalance=100000000

router.address=0x2bE080AF2eac3d6CD01DE1B21d31139d9AC6D9F7
referral.address=0xCD69352A989711A7786fcf296f48A36f932DDc72