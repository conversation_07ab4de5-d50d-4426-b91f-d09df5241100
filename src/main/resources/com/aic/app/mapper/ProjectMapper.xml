<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aic.app.mapper.ProjectMapper">

    <resultMap id="BaseResultMap" type="com.aic.app.model.Project">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="description" column="description" />
            <result property="token" column="token" />
            <result property="website" column="website" />
            <result property="twitter" column="twitter" />
            <result property="telegram" column="telegram" />
            <result property="state" column="state" />
            <result property="enable" column="enable" />
            <result property="total" column="total" />
            <result property="price" column="price" />
            <result property="quantity" column="quantity" />
            <result property="startTime" column="start_time" />
            <result property="presaleTime" column="presale_time" />
            <result property="endTime" column="end_time" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,description,token,website,twitter,
        telegram,state,enable,total,price,
        quantity,start_time,presale_time,end_time,create_time
    </sql>
</mapper>
