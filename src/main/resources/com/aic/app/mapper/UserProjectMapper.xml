<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aic.app.mapper.UserProjectMapper">

    <resultMap id="BaseResultMap" type="com.aic.app.model.UserProject">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="projectId" column="project_id" />
            <result property="state" column="state" />
            <result property="price" column="price" />
            <result property="quantity" column="quantity" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,project_id,state,price,quantity,
        create_time
    </sql>
</mapper>
