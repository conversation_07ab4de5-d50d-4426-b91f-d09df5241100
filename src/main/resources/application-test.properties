#spring.profiles.default=prod

spring.application.name=app
server.port=8094

# swagger-ui custom path
springdoc.swagger-ui.path=/swagger-ui.html

spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.url=*******************************
spring.datasource.username=root
spring.datasource.password=dbadmin123
spring.data.redis.host=127.0.0.1
spring.data.redis.database=0
spring.data.redis.port=6379

#logging.pattern.level= %5p [%t] %-40.40logger{39} : %m%n
logging.level.com.aic.app.mapper = debug
logging.level.com.aic.app = debug
mybatis.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl

spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

jc.host=https://api.jucoin.vc

app.open=true
app.hello====\u542F\u52A8\u73AF\u5883\uFF1A\u6D4B\u8BD5====
app.initBalance=100000000

# ??????
#spring.graphql.cors.allowed-headers=*
#spring.graphql.cors.allowed-methods=*

router.address=******************************************
referral.address=******************************************
