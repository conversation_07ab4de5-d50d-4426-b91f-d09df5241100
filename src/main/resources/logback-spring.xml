<configuration>
    <!-- 定义日志文件的路径 -->
<!--    <property name="LOG_HOME" value="logs" />-->
    <property name="LOG_HOME" value="${LOG_HOME:-logs}" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 业务日志文件输出 -->
    <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/xyc.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/xyc.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- SQL 日志文件输出 -->
    <appender name="SQL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/sql.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 开发环境配置 -->
    <springProfile name="default">
        <root level="INFO"/>
        <logger name="com.aic.app" level="DEBUG">
            <appender-ref ref="CONSOLE" />
        </logger>
    </springProfile>


    <!-- 其他环境配置 -->
    <springProfile name="!default">
        <!-- 设置 MyBatis 的日志级别 -->
        <logger name="com.aic.app.mapper" level="DEBUG" additivity="false">
            <appender-ref ref="SQL_FILE" />
        </logger>

        <!-- 设置业务日志级别 -->
        <logger name="com.aic.app" level="INFO" additivity="false">
            <appender-ref ref="BUSINESS_FILE" />
        </logger>

        <!-- 设置根日志级别 -->
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="BUSINESS_FILE" />
        </root>
    </springProfile>

</configuration>
