package com.aic.app.admin;

import com.aic.app.admin.form.SysUserForm;
import com.aic.app.admin.form.SysUserQuery;
import com.aic.app.model.admin.SysUser;
import com.aic.app.service.admin.ISysUserService;
import com.aic.app.util.BizAssert;
import com.aic.app.util.GoogleAuthenticator;
import com.aic.app.vo.Result;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin/sys-user")
@AllArgsConstructor
@Slf4j
@Hidden
@Tag(name = "系统用户管理", description = "系统用户管理相关接口")
public class SysUserController {

    private final ISysUserService sysUserService;

    @GetMapping
    @Operation(summary = "获取系统用户列表")
    public Result<IPage<SysUser>> list(SysUserQuery query) {
        // 使用QueryWrapper而非LambdaQueryWrapper来处理排序
        QueryWrapper<SysUser> qw = new QueryWrapper<SysUser>()
                .eq(query.getId() != null, "id", query.getId())
                .like(StringUtils.isNotEmpty(query.getAccount()), "account", query.getAccount())
                .like(StringUtils.isNotEmpty(query.getName()), "name", query.getName())
                .eq(query.getState() != null, "state", query.getState())
                .like(StringUtils.isNotEmpty(query.getGoogleCode()), "google_code", query.getGoogleCode())
                .eq(query.getRole() != null, "role", query.getRole());

        if (StringUtils.isNotEmpty(query.getOrder())) {
            qw.orderBy(true, "ascending".equals(query.getOrder()), query.getProp());
        } else {
            qw.orderByDesc("create_date");
        }

        IPage<SysUser> page = sysUserService.page(new Page<>(query.getPage(), query.getSize()), qw);

        // 不返回密码信息
        page.getRecords().forEach(user -> user.setPassword(null));

        return Result.success(page);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取系统用户详情")
    public Result<SysUser> get(@PathVariable Integer id) {
        SysUser sysUser = sysUserService.getById(id);
        BizAssert.notNull(sysUser, "用户不存在");
        // 不返回密码
        sysUser.setPassword(null);
        return Result.success(sysUser);
    }

    @PostMapping
    @Operation(summary = "添加系统用户")
    public Result<SysUser> add(@RequestBody @Valid SysUserForm form) {
        // 检查账号是否已存在
        SysUser existUser = sysUserService.getOne(
                new LambdaQueryWrapper<SysUser>().eq(SysUser::getAccount, form.getAccount()));
        BizAssert.isNull(existUser, "账号已存在");

        SysUser sysUser = new SysUser();
        sysUser.setAccount(form.getAccount());
        sysUser.setName(form.getName());
        sysUser.setState(form.getState() != null ? form.getState() : 1); // 默认启用
        sysUser.setCreateDate(new Date());
        sysUser.setGoogleCode(form.getGoogleCode());
        sysUser.setRole("NORMAL"); // 默认为普通角色

        // 密码加密处理
        String password = "$admin+" + form.getPassword();
        String encryptedPassword = DigestUtils.md5DigestAsHex(password.getBytes());
        sysUser.setPassword(encryptedPassword);

        sysUserService.save(sysUser);
        return Result.success("添加成功", sysUser);
    }

    @PutMapping
    @Operation(summary = "更新系统用户")
    public Result<SysUser> update(@RequestBody @Valid SysUserForm form) {
        BizAssert.notNull(form.getId(), "用户ID不能为空");
        SysUser sysUser = sysUserService.getById(form.getId());
        BizAssert.notNull(sysUser, "用户不存在");

        // 检查账号是否已存在（排除自身）
        SysUser existUser = sysUserService.getOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getAccount, form.getAccount())
                        .ne(SysUser::getId, form.getId()));
        BizAssert.isNull(existUser, "账号已存在");

        sysUser.setAccount(form.getAccount());
        sysUser.setName(form.getName());
        if (form.getState() != null) {
            sysUser.setState(form.getState());
        }

        // 更新谷歌验证码
        if (StringUtils.isNotEmpty(form.getGoogleCode())) {
            sysUser.setGoogleCode(form.getGoogleCode());
        }

        // 角色不能更新，保持原有角色

        // 如果提供了密码，则更新密码
        if (StringUtils.isNotEmpty(form.getPassword())) {
            String password = "$admin+" + form.getPassword();
            String encryptedPassword = DigestUtils.md5DigestAsHex(password.getBytes());
            sysUser.setPassword(encryptedPassword);
        }

        sysUserService.updateById(sysUser);
        return Result.success("更新成功", sysUser);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除系统用户")
    public Result<Void> delete(@PathVariable Integer id) {
        SysUser sysUser = sysUserService.getById(id);
        BizAssert.notNull(sysUser, "用户不存在");
        sysUserService.removeById(id);
        return Result.success("删除成功", null);
    }

//    @GetMapping("/check-google-code")
//    @Operation(summary = "检查是否绑定谷歌验证码")
//    public Result<Boolean> checkGoogleCode(@RequestAttribute("admin") SysUser admin) {
//        boolean hasBound = admin.getGoogleCode() != null && !admin.getGoogleCode().isEmpty();
//        return Result.success(hasBound);
//    }

    @GetMapping("/generate-google-code")
    @Operation(summary = "生成谷歌验证码")
    public Result<Map<String, String>> generateGoogleCode(@RequestAttribute("admin") SysUser admin) {
        // 生成密钥
        String secretKey = GoogleAuthenticator.generateSecretKey();

        // 生成二维码内容
        String qrCodeContent = GoogleAuthenticator.getQRBarcodeURL(admin.getAccount(), "dop-app", secretKey);

        Map<String, String> result = new HashMap<>();
        result.put("secretKey", secretKey);
        result.put("qrCodeContent", qrCodeContent);

        return Result.success(result);
    }

//    @PostMapping("/bind-google-code")
//    @Operation(summary = "绑定谷歌验证码")
//    public Result<Void> bindGoogleCode(@RequestAttribute("admin") SysUser admin, @RequestBody Map<String, String> params) {
//        String secretKey = params.get("secretKey");
//        String verifyCode = params.get("verifyCode");
//
//        BizAssert.notEmpty(secretKey, "密钥不能为空");
//        BizAssert.notEmpty(verifyCode, "验证码不能为空");
//
//        // 验证谷歌验证码
//        boolean isValid = GoogleAuthenticator.verify(secretKey, verifyCode);
//        BizAssert.isTrue(isValid, "验证码验证失败");
//
//        // 更新用户的谷歌验证码
//        admin.setGoogleCode(secretKey);
//        sysUserService.updateById(admin);
//
//        return Result.success("绑定成功", null);
//    }
//
//    @PostMapping("/unbind-google-code")
//    @Operation(summary = "解绑谷歌验证码")
//    public Result<Void> unbindGoogleCode(@RequestAttribute("admin") SysUser admin, @RequestBody Map<String, String> params) {
//        String verifyCode = params.get("verifyCode");
//
//        BizAssert.notEmpty(verifyCode, "验证码不能为空");
//        BizAssert.notEmpty(admin.getGoogleCode(), "未绑定谷歌验证码");
//
//        // 验证谷歌验证码
//        boolean isValid = GoogleAuthenticator.verify(admin.getGoogleCode(), verifyCode);
//        BizAssert.isTrue(isValid, "验证码验证失败");
//
//        // 清除用户的谷歌验证码
//        admin.setGoogleCode(null);
//        sysUserService.updateById(admin);
//
//        return Result.success("解绑成功", null);
//    }
}
