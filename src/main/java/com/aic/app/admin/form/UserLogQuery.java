package com.aic.app.admin.form;

import com.aic.app.form.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserLogQuery extends PageQuery {
    private String id;
    private Integer[] types;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date beginTime;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date endTime;
    private String tokenId;
    private String symbol;
    private String remark;
}
