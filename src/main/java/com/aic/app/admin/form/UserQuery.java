package com.aic.app.admin.form;

import com.aic.app.form.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserQuery extends PageQuery {
    
    private String id;
    private String address;
    private String code;
    private String pid;
    private String tokenId;
    private List<Integer> level;
    // 搜索类型：0-普通搜索 1-搜索伞下lv4 lv5
    private int searchType;
}
