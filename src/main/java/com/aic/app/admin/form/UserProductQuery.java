package com.aic.app.admin.form;

import com.aic.app.form.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserProductQuery extends PageQuery {

    @Schema(description = "用户ID或用户地址")
    private String userId;

    @Schema(description = "用户地址")
    private String address;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "产品类型列表")
    private Integer[] types;

    @Schema(description = "状态列表")
    private Integer[] status;

    @Schema(description = "产品ID列表")
    private List<Integer> productId;

    @Schema(description = "Token ID")
    private String tokenId;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始时间")
    private Date beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束时间")
    private Date endTime;
}
