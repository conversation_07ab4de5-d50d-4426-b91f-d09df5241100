package com.aic.app.admin.form;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminBuyForm {
    @NotNull
    String userId;
    int productId;
    @Positive(message = "请填写正确的理财数量")
    BigDecimal amount;
}
