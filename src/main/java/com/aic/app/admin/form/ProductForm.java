package com.aic.app.admin.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductForm {

    @Schema(description = "产品ID，更新时必填")
    private Long id;

    @NotNull(message = "产品类型不能为空")
    @Min(value = 1, message = "产品类型必须为1或2")
    @Max(value = 2, message = "产品类型必须为1或2")
    @Schema(description = "产品类型，1-预售节点 2-普通商品", required = true)
    private Integer type;

    @NotBlank(message = "产品名称不能为空")
    @Size(max = 100, message = "产品名称长度不能超过100个字符")
    @Schema(description = "产品名称", required = true)
    private String name;

    @Size(max = 500, message = "产品描述长度不能超过500个字符")
    @Schema(description = "产品描述")
    private String description;

//    @NotNull(message = "收益率不能为空")
//    @DecimalMin(value = "0", message = "收益率不能为负数")
//    @DecimalMax(value = "1", message = "收益率不能超过100%")
    @Schema(description = "日收益率，例如0.01表示1%", required = true)
    private BigDecimal rate;

//    @NotNull(message = "手续费不能为空")
//    @DecimalMin(value = "0", message = "手续费不能为负数")
    @Schema(description = "手续费", required = true)
    private BigDecimal fee;

//    @NotNull(message = "天数不能为空")
//    @Min(value = 1, message = "天数必须大于0")
    @Schema(description = "理财天数", required = true)
    private Integer day;

    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0", message = "价格不能为负数")
    @Schema(description = "产品价格", required = true)
    private BigDecimal price;

    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    @Schema(description = "产品图片URL")
    private String image;

    @NotNull(message = "启用状态不能为空")
    @Schema(description = "是否启用，true-启用 false-禁用", required = true)
    private Boolean enable;

}
