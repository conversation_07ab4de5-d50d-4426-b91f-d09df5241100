package com.aic.app.admin;

import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aic.app.vo.Result;
import com.aic.app.vo.Server;

/**
 * 服务器监控
 * 
 * <AUTHOR>
 */
@RestController
@Hidden
@RequestMapping("/admin/monitor/server")
public class ServerController {

    @GetMapping
    public Result<Server> server(ModelMap mmap) throws Exception {
        Server server = new Server();
        server.copyTo();
        // System.out.println(new ObjectMapper().writeValueAsString(server));
        return Result.success(server);
    }

}
