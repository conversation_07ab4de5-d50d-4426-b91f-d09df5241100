package com.aic.app.admin;

import java.math.BigDecimal;
import java.util.*;

import com.aic.app.admin.form.*;
import com.aic.app.mapper.ProductMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.vo.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.*;

import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/admin/user")
@AllArgsConstructor
@Hidden
@Slf4j
public class AdminUserController {

    private final ProductMapper productMapper;
    IUserService userService;
    IStakeUserService stakeUserService;
    IUserLogService userLogService;
    IRewardLogService rewardLogService;
    IUserProductService userProductService;
    ISysConfigService sysConfigService;
    IProductService productService;
    IRewardDayService rewardDayService;
//    Job3 job3;

    @GetMapping
    public Result<IPage<UserModel>> userList(UserQuery form) {
        QueryWrapper<User> qw = new QueryWrapper<User>();
        if (form.getId() != null && !NumberUtils.isCreatable(form.getId())) {
            User user = userService.getByCode(form.getId());
            if (user != null) {
                form.setId(user.getId().toString());
            }
        }
        if (form.getPid() != null && !NumberUtils.isCreatable(form.getPid())) {
            User user = userService.getByCode(form.getPid());
            if (user != null) {
                form.setPid(user.getId().toString());
            }
        }

        qw.eq(StringUtils.isNotEmpty(form.getId()), "a.id", form.getId())
                .eq(StringUtils.isNotEmpty(form.getPid()), "a.pid", form.getPid())
                .eq(StringUtils.isNotEmpty(form.getAddress()), "a.address", form.getAddress())
                .eq(StringUtils.isNotEmpty(form.getCode()), "a.code", form.getCode())
                .in(ObjectUtils.isNotEmpty(form.getLevel()), "a.level", form.getLevel())
                .last("order by a.create_time desc");
//                .orderByDesc(User::getId);

        IPage<UserModel> page = stakeUserService.findAllForAdminUser(new Page<>(form.getPage(), form.getSize()), qw);
        return Result.success(page);
    }
    
    @GetMapping("/stake")
    public Result<IPage<StakeUserModel>> stakeUserList(UserQuery form) {
        QueryWrapper<StakeUser> qw = new QueryWrapper<StakeUser>();
        if (form.getPid() != null && !NumberUtils.isCreatable(form.getPid())) {
            User user = userService.getByCode(form.getPid());
            if (user != null) {
                if (form.getSearchType() == 1) {
                    // 搜索等级 4-5
                    form.setLevel(Arrays.asList(4, 5));
                    qw.like("b.path", "/" + user.getId() + "/");
                    form.setPid(null);
                } else {
                    form.setPid(user.getId().toString());
                }
            }
        }
        if (form.getId() != null && !NumberUtils.isCreatable(form.getId())) {
            User user = userService.getByCode(form.getId());
            if (user != null) {
                form.setId(user.getId().toString());
            }
        }

        qw.eq(StringUtils.isNotEmpty(form.getId()), "a.id", form.getId())
            .eq(StringUtils.isNotEmpty(form.getPid()), "a.pid", form.getPid())
            .eq(StringUtils.isNotEmpty(form.getAddress()), "a.address", form.getAddress())
            .eq(StringUtils.isNotEmpty(form.getCode()), "a.code", form.getCode())
            .in(ObjectUtils.isNotEmpty(form.getLevel()), "a.level", form.getLevel())
            .in(ObjectUtils.isNotEmpty(form.getTokenId()), "c.token_id", form.getTokenId());
            // .last("order by a.create_time desc");
//                .orderByDesc(User::getId);

        if (StringUtils.isNotEmpty(form.getOrder())) {
            qw.orderBy(true, "ascending".equals(form.getOrder()), form.getProp());
        } else {
            qw.orderByDesc("a.create_time");
        }

        IPage<StakeUserModel> page = stakeUserService.findAllForAdmin(new Page<>(form.getPage(), form.getSize()), qw);
        if (!page.getRecords().isEmpty()) {
        }
        qw.select("ifnull(sum(c.current_amount), 0) as current_amount", 
            "ifnull(sum(c.pending_amount), 0) as pending_amount", 
            "ifnull(sum(c.static_pool), 0) as static_pool", 
            "ifnull(sum(c.dynamic_pool), 0) as dynamic_pool", 
            "ifnull(sum(c.node_pool), 0) as node_pool", 
            "ifnull(sum(c.week_dynamic), 0) as week_dynamic", 
            "ifnull(sum(c.node_perf), 0) as node_perf", 
            "ifnull(sum(c.max_team_perf), 0) as max_team_perf", 
            "ifnull(sum(c.max_node_perf), 0) as max_node_perf",
            "ifnull(sum(c.team_perf), 0) as team_perf",
            "ifnull(sum(c.node), 0) as node",
            "ifnull(sum(c.sum_amount), 0) as sum_amount",
            "ifnull(sum(c.node_reward), 0) as node_reward");
        StakeUserModel stakeUser = stakeUserService.findAllForAdmin(new Page<>(-1, -1), qw).getRecords().get(0);

        return Result.success(page).setTotal(stakeUser);
    }

    // get /stake-child
     @GetMapping("/stake-child")
     public Result<StakeChildVo> stakeChild(UserQuery form) {
         StakeChildVo vo = new StakeChildVo();
         // 查询伞下的累计静态跟累计动态
         User user = null;
         if (form.getId() != null && !NumberUtils.isCreatable(form.getId())) {
             user = userService.getById(form.getId());
         } else if (StringUtils.isNotEmpty(form.getCode())) {
             user = userService.getByCode(form.getCode());
         }
         if (user != null) {
             StakeUser stakeUser = stakeUserService.getStakeUser(user.getId(), AssetEnum.XYC.getTokenId());
             vo.setTotalStatic(stakeUser.getTotalStatic());
             vo.setTotalDynamic(stakeUser.getTotalDynamic());
             StakeUser sumStakeUser = stakeUserService.sumChilds(user.getId());
             vo.setSumTotalStatic(sumStakeUser.getTotalStatic());
             vo.setSumTotalDynamic(sumStakeUser.getTotalDynamic());
         }
         return Result.success(vo);
     }

    @GetMapping("/product")
    public Result<IPage<UserProductVo>> product(UserProductQuery form) {
        // 处理用户ID搜索（支持用户ID、用户代码、用户地址）
        if (form.getUserId() != null && !NumberUtils.isCreatable(form.getUserId())) {
            User user = userService.getByCode(form.getUserId());
            if (user != null) {
                form.setUserId(user.getId().toString());
            }
        } else if (StringUtils.isNotEmpty(form.getUserId())) {
            User user = userService.findByAddress(form.getUserId());
            if (user != null) {
                form.setUserId(user.getId().toString());
            }
        }

        // 构建查询条件
        QueryWrapper<UserProduct> qw = new QueryWrapper<UserProduct>()
                .like(StringUtils.isNotEmpty(form.getUserId()), "a.user_id", form.getUserId())
                .like(StringUtils.isNotEmpty(form.getAddress()), "c.address", form.getAddress())
                .like(StringUtils.isNotEmpty(form.getOrderNo()), "a.order_no", form.getOrderNo())
                .in(ObjectUtils.isNotEmpty(form.getProductId()), "a.product_id", form.getProductId());

        // 产品类型条件
        if (ObjectUtils.isNotEmpty(form.getTypes())) {
            qw.in("a.type", Arrays.asList(form.getTypes()));
        }

        // 状态条件
        if (ObjectUtils.isNotEmpty(form.getStatus())) {
            qw.in("a.status", Arrays.asList(form.getStatus()));
        }

        // 时间范围条件
        if (form.getBeginTime() != null || form.getEndTime() != null) {
            qw.between("a.create_time", Utils.formatBeginTime(form.getBeginTime()), Utils.formatEndTime(form.getEndTime()));
        }

        // 排序条件
        if (StringUtils.isNotEmpty(form.getOrder())) {
            String sortField = form.getProp();
            // 验证排序字段，防止SQL注入
            if ("createTime".equals(sortField)) {
                sortField = "a.create_time";
            } else if ("amount".equals(sortField)) {
                sortField = "a.amount";
            } else if ("orderNo".equals(sortField)) {
                sortField = "a.order_no";
            } else {
                sortField = "a.id"; // 默认按ID排序
            }
            qw.orderBy(true, "ascending".equals(form.getOrder()), sortField);
        } else {
            qw.orderByDesc("a.id");
        }

        IPage<UserProductVo> page = userProductService.page(new Page<>(form.getPage(), form.getSize()), qw)
                .convert(row -> new UserProductVo(row));

        return Result.success(page);
    }

    @GetMapping("/reward-day")
    public Result<IPage<RewardDay>> rewardDay(UserProductQuery form) {
        QueryWrapper<RewardDay> qw = new QueryWrapper<RewardDay>()
                .orderByDesc("id");
//        if (ObjectUtils.isNotEmpty(form.getTypes())) {
//            qw.in("type", Arrays.asList(form.getTypes()));
//        }
//        if (ObjectUtils.isNotEmpty(form.getStatus())) {
//            qw.in("status", Arrays.asList(form.getStatus()));
//        }
        IPage<RewardDay> page = rewardDayService.page(new Page<>(form.getPage(), form.getSize()), qw);
        return Result.success(page);
    }

//    @PostMapping("/airdrop")
//    public Result<Boolean> airdrop(@RequestBody UserAsset userAsset) {
//        StakeUser user = userService.getById(userAsset.getUserId());
//        log.info("[admin] 空投 uid = {}, tokenId = {}, amount = {}", userAsset.getUserId(), userAsset.getTokenId(), userAsset.getBalance());
//        userAssetService.plus(userAsset.getUserId(), userAsset.getTokenId(), userAsset.getBalance(), UserLogType.Airdrop.getLabel());
//        userLogService.addLog(user, UserLogType.Airdrop, userAsset.getBalance(), UserLogType.Airdrop.getLabel(), userAsset.getTokenId());
//        return Result.success(true);
//    }


    @GetMapping("/userlog")
    public Result<IPage<UserLogVo>> userLogList(UserLogQuery form) {
        if (form.getId() != null && !NumberUtils.isCreatable(form.getId())) {
            User user = userService.getByCode(form.getId());
            if (user != null) {
                form.setId(user.getId().toString());
            }
        } else if (StringUtils.isNotEmpty(form.getId())) {
            User user = userService.findByAddress(form.getId());
            if (user != null) {
                form.setId(user.getId().toString());
            }
        }
        
        QueryWrapper<UserLog> qw = new QueryWrapper<UserLog>()
                .eq(StringUtils.isNotEmpty(form.getId()), "user_id", form.getId())
                .eq(StringUtils.isNotEmpty(form.getTokenId()), "symbol", form.getTokenId());
        if (StringUtils.isNotEmpty(form.getRemark())) {
            qw.like("remark", form.getRemark());
        }
        if (ObjectUtils.isNotEmpty(form.getTypes())) {
            qw.in("type", Arrays.asList(form.getTypes()));
        }
        if (form.getBeginTime() != null || form.getEndTime() != null) {
            qw.between("create_time", Utils.formatBeginTime(form.getBeginTime()), Utils.formatEndTime(form.getEndTime()));
        }
        if (StringUtils.isNotEmpty(form.getOrder())) {
            qw.orderBy(true, "ascending".equals(form.getOrder()), form.getProp());
        } else {
            qw.orderByDesc("create_time");
        }
        qw.orderByAsc("id");
        IPage<UserLogVo> page = userLogService.page(new Page<>(form.getPage(), form.getSize()), qw)
                .convert(UserLogVo::new);
        qw.select("ifnull(sum(amount), 0) as amount");
        UserLog total = userLogService.getOne(qw);

        return Result.success(page).setTotal(total);
    }

    @GetMapping("/rewardlog")
    public Result<IPage<RewardLogVo>> rewardLog(UserLogQuery form) {
        if (form.getId() != null && !NumberUtils.isCreatable(form.getId())) {
            User user = userService.getByCode(form.getId());
            if (user != null) {
                form.setId(user.getId().toString());
            }
        } else if (StringUtils.isNotEmpty(form.getId())) {
            User user = userService.findByAddress(form.getId());
            if (user != null) {
                form.setId(user.getId().toString());
            }
        }
        QueryWrapper<RewardLog> qw = new QueryWrapper<RewardLog>()
                .eq(StringUtils.isNotEmpty(form.getId()), "user_id", form.getId())
                .eq(StringUtils.isNotEmpty(form.getTokenId()), "symbol", form.getTokenId());

        if (form.getBeginTime() != null || form.getEndTime() != null) {
            qw.between("create_time", Utils.formatBeginTime(form.getBeginTime()), Utils.formatEndTime(form.getEndTime()));
        }
        if (StringUtils.isNotEmpty(form.getOrder())) {
            qw.orderBy(true, "ascending".equals(form.getOrder()), form.getProp());
        } else {
            qw.orderByDesc("id");
        }

        IPage<RewardLogVo> page = rewardLogService.page(new Page<>(form.getPage(), form.getSize()), qw)
                .convert(RewardLogVo::new);

        qw.select("ifnull(sum(amount), 0) as amount");
        RewardLog total = rewardLogService.getOne(qw);

        return Result.success(page).setTotal(total);
    }


    @GetMapping("/sys-config")
    public Result<SysConfig> getSysConfig() {
        SysConfig sysConfig = sysConfigService.getSysConfig();
        return Result.success(sysConfig);
    }

    @PostMapping("/sys-config")
    public Result<SysConfig> saveSysConfig(@RequestBody SysConfig sysConfig) {
        sysConfigService.updateSysConfig(sysConfig);
        return Result.success("修改成功", sysConfig);
    }

    // 获取今日是否量，查询user_log表 type in (3,4) 的amount合计
    @GetMapping("/today-amount")
    public Result<BigDecimal> todayAmount() {
        BigDecimal amount = userLogService.getTodayAmount();
        return Result.success(amount);
    }

//    @PostMapping("/set-level")
//    public Result<Boolean> setLevel(@RequestBody User form) {
//        Long id = form.getId();
//        int level = form.getLevel();
//        boolean result;
//        if (level == -1) {
//            // 重置等级
//            int _level = 0;
//            result = userService.update(new LambdaUpdateWrapper<User>()
//                    .set(User::isLockLevel, false)
//                    .set(User::getLevel, _level)
//                    .eq(User::getId, id));
//        } else {
//            result = userService.setLevel(id, level);
//        }
//        return Result.success(result);
//    }

//    @PostMapping("/stake-from-wallet-admin")
//    public Result<Boolean> stakeFromWalletAdmin(@Valid @RequestBody UserStakeForm form) {
//        User user = null;
//        if (form.getId() != null && !NumberUtils.isCreatable(form.getId())) {
//            user = userService.getByCode(form.getId());
//        } else if (StringUtils.isNotEmpty(form.getId())) {
//            user = userService.getById(form.getId());
//        }
//        BizAssert.notNull(user, "用户不存在");
//        
//        stakeUserService.stakeFromWalletAdmin(user, form.getAmount());
//        return Result.success(true);
//    }

}
