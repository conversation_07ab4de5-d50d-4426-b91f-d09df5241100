package com.aic.app.admin;

import com.aic.app.admin.form.LoginForm;
import com.aic.app.model.admin.SysUser;
import com.aic.app.service.admin.ISysUserService;
import com.aic.app.util.FileUploadUtil;
import com.aic.app.util.GoogleAuthenticator;
import com.aic.app.util.JwtUtil;
import com.aic.app.vo.Result;
import com.aic.app.vo.UploadVo;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/admin")
@AllArgsConstructor
@Slf4j
@Hidden
public class AdminController {

    ISysUserService sysUserService;
    ObjectMapper objectMapper;
    StringRedisTemplate stringRedisTemplate;

    @GetMapping("/captchaImage")
    public Object captchaImage() {
        // 注释图形验证码生成，只保留谷歌验证码
//        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(200, 90, 4, 100);
//        String code = lineCaptcha.getCode();
//        String uid = UUID.randomUUID().toString();
//        stringRedisTemplate.opsForValue().set(uid, code, 60 * 5, TimeUnit.SECONDS);
//        log.debug("验证码 = {}", code);
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);
        map.put("msg", "操作成功");
//        map.put("img", lineCaptcha.getImageBase64());
        map.put("captchaEnabled", false); // 禁用图形验证码
//        map.put("uuid", uid);
        return map;
    }

    @PostMapping("/login")
    public Object login(@RequestBody LoginForm form) throws Exception {
        // 检查用户是否被锁定
        String lockKey = "dop:login:lock:" + form.getUsername();
        Boolean isLocked = stringRedisTemplate.hasKey(lockKey);
        if (Boolean.TRUE.equals(isLocked)) {
            return Result.error("账户已被锁定，请稍后再试", null);
        }

        // 注释图形验证码验证，只保留谷歌验证码
//        String code = stringRedisTemplate.opsForValue().get(form.getUuid());
//        if (!form.getCode().equalsIgnoreCase(code)) {
//            log.error("验证码错误 code = {}, user code = {}", code, form.getCode());
//            return Result.error("验证码错误", null);
//        }

        String password = "$admin+" + form.getPassword();
        String pwd = DigestUtils.md5DigestAsHex(password.getBytes());

        LambdaUpdateWrapper<SysUser> qw = new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getAccount, form.getUsername())
                .eq(SysUser::getPassword, pwd);

        SysUser sysUser = sysUserService.getOne(qw);
        if (sysUser == null) {
            // 登录失败，增加失败次数
            String failKey = "dop:login:fail:" + form.getUsername();
            Long failCount = stringRedisTemplate.opsForValue().increment(failKey);
            stringRedisTemplate.expire(failKey, 1, TimeUnit.HOURS); // 设置1小时过期

            if (failCount != null && failCount >= 5) {
                // 超过5次失败，锁定账户15分钟
                stringRedisTemplate.opsForValue().set(lockKey, "locked", 60*24, TimeUnit.MINUTES);
                return Result.error("登录失败次数过多，账户已被锁定24小时", null);
            }
            return Result.error("用户名或密码错误", null);
        }

        // 验证谷歌验证码
        if (form.getGoogleCode() == null || form.getGoogleCode().isEmpty()) {
            return Result.error("请输入谷歌验证码", null);
        }

        // 如果用户设置了谷歌验证码，则验证
        if (sysUser.getGoogleCode() != null && !sysUser.getGoogleCode().isEmpty()) {
            boolean isValid = GoogleAuthenticator.verify(sysUser.getGoogleCode(), form.getGoogleCode());
            if (!isValid) {
                // 谷歌验证码验证失败
                String failKey = "dop:login:fail:" + form.getUsername();
                Long failCount = stringRedisTemplate.opsForValue().increment(failKey);
                stringRedisTemplate.expire(failKey, 1, TimeUnit.HOURS); // 设置1小时过期

                if (failCount != null && failCount >= 5) {
                    // 超过5次失败，锁定账户
                    stringRedisTemplate.opsForValue().set(lockKey, "locked", 60*24, TimeUnit.MINUTES);
                    return Result.error("登录失败次数过多，账户已被锁定24小时", null);
                }
                return Result.error("谷歌验证码错误", null);
            }
        } else {
            // 如果用户没有设置谷歌验证码，则返回错误
            return Result.error("请先绑定谷歌验证码", null);
        }

        // 登录成功，清除失败记录
        stringRedisTemplate.delete("dop:login:fail:" + form.getUsername());

        Map<String, Object> data = new HashMap<>();
        data.put("id", sysUser.getId());
        data.put("username", sysUser.getAccount());
        String json = objectMapper.writeValueAsString(data);
        String token = JwtUtil.createJWT(json);

        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);
        map.put("msg", "操作成功");
        map.put("token", token);
        return map;
    }

    @PostMapping("/logout")
    public Object logout() {
        return Result.success("操作成功", null);
    }

    @GetMapping("/getInfo")
    public Object getInfo(@RequestAttribute("admin") SysUser sysUser) {

        Map<String, Object> data = new HashMap<>();
        data.put("createBy", sysUser.getName());
        data.put("remark", sysUser.getName());

        data.put("createTime", "2023-04-23 16:11:38");
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);
        map.put("msg", "操作成功");
        map.put("permissions", Arrays.asList("*:*:*"));
        map.put("roles", Arrays.asList(sysUser.getRole()));
        map.put("user", data);
        return map;
    }

    @GetMapping("/getRouters")
    public Object getRouters(@RequestAttribute("admin") SysUser sysUser) throws Exception {
        if (!"ADMIN".equals(sysUser.getRole())) {
            // 返回空数组
            return Result.success(new ArrayList<>());
        }
//        {
//            "name": "SysConfig",
//                "path": "sysconfig",
//                "hidden": false,
//                "component": "system/user/sysconfig",
//                "meta": {
//            "title": "参数设置",
//                    "icon": "list",
//                    "noCache": false,
//                    "link": null
//        }
//        },
        String routes = """
                [{
                        "name": "Sys",
                        "path": "/sys",
                        "hidden": false,
                        "redirect": "noRedirect",
                        "component": "Layout",
                        "alwaysShow": true,
                        "meta": {
                            "title": "系统管理",
                            "icon": "system",
                            "noCache": false,
                            "link": null
                        },
                        "children": [
                            {
                                "name": "server",
                                "path": "server",
                                "hidden": false,
                                "component": "monitor/server/index",
                                "meta": {
                                    "title": "服务器监控",
                                    "icon": "list",
                                    "noCache": false,
                                    "link": null
                                }
                            },
                            {
                                "name": "SysUser",
                                "path": "sysUser",
                                "hidden": false,
                                "component": "system/admin/user",
                                "meta": {
                                    "title": "用户管理",
                                    "icon": "list",
                                    "noCache": false,
                                    "link": null
                                }
                            }
                        ]
                    }]
                """;
        List<Map<String, Object>> list = objectMapper.readValue(routes.trim(), new TypeReference<List<Map<String, Object>>>() {});
        
        return Result.success(list);
    }

    // /upload
    @PostMapping("/common/upload")
    @Operation(summary = "上传产品图片")
    public Result<UploadVo> upload(@RequestParam("file") MultipartFile file) {
        try {
            // 上传文件
            String filePath = FileUploadUtil.upload("upload/products", file);

            // 创建响应对象
            UploadVo uploadVo = UploadVo.success(
                    file.getOriginalFilename(),
                    filePath,
                    file.getSize()
            );

            return Result.success("上传成功", uploadVo);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Result.error("上传失败: " + e.getMessage(), null);
        }
    }

}
