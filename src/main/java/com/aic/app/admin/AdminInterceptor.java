package com.aic.app.admin;

import com.aic.app.model.admin.SysUser;
import com.aic.app.service.admin.ISysUserService;
import com.aic.app.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@AllArgsConstructor
@Slf4j
public class AdminInterceptor implements HandlerInterceptor {

    ISysUserService userService;
    ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String uri = request.getRequestURI();
        if (!uri.startsWith("/admin")) {
            return true;
        }
        String token = request.getHeader("Authorization");
        if (token != null && !token.isEmpty()) {
            token = token.replaceFirst("Bearer ", "");
            try {
                Claims claims = JwtUtil.parseJWT(token);
//                System.out.println(claims);
                SysUser user = objectMapper.readValue(claims.getSubject(), SysUser.class);
                SysUser sysUser = userService.getById(user.getId());
                if (sysUser != null) {
                    log.debug("鉴权成功");
                    request.setAttribute("admin", sysUser);

                    // 检查是否是系统用户管理相关接口
                    if (uri.startsWith("/admin/sys-user")) {
                        // 只有管理员角色才能访问系统用户管理接口
                        if (!"ADMIN".equals(sysUser.getRole())) {
                            log.debug("权限不足，需要管理员权限");
                            response.setStatus(403);
                            response.getWriter().print("{\"code\":403,\"msg\":\"权限不足，需要管理员权限\"}");
                            return false;
                        }
                    }

                    return true;
                }
            } catch (ExpiredJwtException e) {
                log.debug("token过期");
            } catch (Exception e) {
                log.error("后台鉴权失败", e);
            }
        }
        log.debug("admin登录拦截 url = {}", request.getRequestURI());
        // 鉴权失败
        response.getWriter().print("""
                {"code":401,"msg":"need login"}
                """);
        return false;
    }

}
