package com.aic.app.admin;

import com.aic.app.admin.form.ProductForm;
import com.aic.app.form.ProductQuery;
import com.aic.app.model.Product;
import com.aic.app.service.IProductService;
import com.aic.app.util.BizAssert;
import com.aic.app.util.FileUploadUtil;
import com.aic.app.vo.ProductVo;
import com.aic.app.vo.Result;
import com.aic.app.vo.UploadVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@RestController
@RequestMapping("/admin/products")
@AllArgsConstructor
@Hidden
@Slf4j
public class AdminProductController {

    IProductService productService;

    @GetMapping
    @Operation(summary = "商品列表")
    public Result<IPage<ProductVo>> products(ProductQuery query) {
        QueryWrapper<Product> qw = new QueryWrapper<Product>()
                .in(query.getTypes() != null && !query.getTypes().isEmpty(), "type", query.getTypes())
                .like(StringUtils.isNotEmpty(query.getName()), "name", query.getName());

        // 动态排序
        if (StringUtils.isNotEmpty(query.getOrder())) {
            String sortField = query.getProp();
            // 验证排序字段，防止SQL注入
            if ("createTime".equals(sortField)) {
                sortField = "create_time";
            } else if ("price".equals(sortField)) {
                sortField = "price";
            } else {
                sortField = "id"; // 默认按ID排序
            }
            qw.orderBy(true, "ascending".equals(query.getOrder()), sortField);
        } else {
            qw.orderByDesc("id"); // 默认按ID降序
        }

        IPage<ProductVo> page = productService.page(new Page<>(query.getPage(), query.getSize()), qw)
                .convert(ProductVo::new);

        return Result.success(page);
    }

    // GET /products/7
    @GetMapping("/{id}")
    @Operation(summary = "商品详情")
    public Result<ProductVo> product(@PathVariable Integer id) {
        Product product = productService.getById(id);
        BizAssert.notNull(product, "商品不存在");
        return Result.success(new ProductVo(product));
    }

    @PostMapping
    @Operation(summary = "新增产品")
    public Result<ProductVo> add(@RequestBody @Valid ProductForm form) {
        Product product = new Product();
        product.setType(form.getType());
        product.setName(form.getName());
        product.setDescription(form.getDescription());
        product.setRate(Optional.ofNullable(form.getRate()).orElse(BigDecimal.ZERO));
        product.setFee(Optional.ofNullable(form.getFee()).orElse(BigDecimal.ZERO));
        product.setDay(Optional.ofNullable(form.getDay()).orElse(0));
        product.setPrice(form.getPrice());
        product.setImage(form.getImage());
        product.setEnable(form.getEnable());
        product.setCreateTime(new Date());

        productService.save(product);
        return Result.success("新增成功", new ProductVo(product));
    }

    @PutMapping
    @Operation(summary = "更新产品")
    public Result<ProductVo> update(@RequestBody @Valid ProductForm form) {
        BizAssert.notNull(form.getId(), "产品ID不能为空");
        Product product = productService.getById(form.getId());
        BizAssert.notNull(product, "产品不存在");

        product.setType(form.getType());
        product.setName(form.getName());
        product.setDescription(form.getDescription());
        product.setRate(form.getRate());
        product.setFee(form.getFee());
        product.setDay(form.getDay());
        product.setPrice(form.getPrice());
        product.setImage(form.getImage());
        product.setEnable(form.getEnable());

        productService.updateById(product);
        return Result.success("更新成功", new ProductVo(product));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除产品")
    public Result<Void> delete(@PathVariable Long id) {
        Product product = productService.getById(id);
        BizAssert.notNull(product, "产品不存在");

        productService.removeById(product);

        return Result.success("删除成功", null);
    }



}
