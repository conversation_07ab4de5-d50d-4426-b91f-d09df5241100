package com.aic.app.job;

import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.*;
import com.aic.app.service.ISysConfigService;
import com.aic.app.service.IUserLogService;
import com.aic.app.service.IUserProductService;
import com.aic.app.service.IUserStakeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 质押结算Job - 每天早晚8点执行
 * 功能：
 * 1. T+1结算：将待确认质押转为已确认质押
 * 2. 静态收益计算：按配置收益率计算
 * 3. 动态奖励计算：分享奖励和社区奖励
 */
@Component
@Slf4j
public class Job3 {

    @Resource
    private StakeUserMapper stakeUserMapper;
    
    @Resource
    private UserRelationMapper userRelationMapper;
    
    @Resource
    private ISysConfigService sysConfigService;
    
    @Resource
    private IUserLogService userLogService;
    
    @Resource
    private IUserStakeService userStakeService;

    @Resource
    private IUserProductService userProductService;

    /**
     * 每天早晚8点执行结算
     * 0 0 8,20 * * ?
     */
//    @Scheduled(cron = "0 0 8,20 * * ?")
//    @Transactional(rollbackFor = Exception.class)
    public void run() {
        log.info("开始执行质押结算Job3");
        
        try {
            // 1. T+1结算：将待确认质押转为已确认质押
            settlePendingStake();
            
            // 2. 普通商品线性释放和静态收益计算
            calculateNormalProductReward();

            // 3. 计算静态收益
            calculateStaticReward();

            // 4. 计算动态奖励
            calculateDynamicReward();
            
            log.info("质押结算Job3执行完成");
        } catch (Exception e) {
            log.error("质押结算Job3执行失败", e);
            throw e;
        }
    }

    /**
     * T+1结算：将待确认质押转为已确认质押
     */
    private void settlePendingStake() {
        log.info("开始T+1结算");
        
        // 查询所有有待确认质押的用户
        List<StakeUser> pendingUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getPendingAmount, BigDecimal.ZERO)
        );
        
        for (StakeUser stakeUser : pendingUsers) {
            BigDecimal pendingAmount = Optional.ofNullable(stakeUser.getPendingAmount()).orElse(BigDecimal.ZERO);
            if (pendingAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal currentAmount = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
                // 将待确认转为已确认
                stakeUserMapper.update(null,
                    new LambdaUpdateWrapper<StakeUser>()
                        .eq(StakeUser::getId, stakeUser.getId())
                        .set(StakeUser::getCurrentAmount, currentAmount.add(pendingAmount))
                        .set(StakeUser::getPendingAmount, BigDecimal.ZERO)
                );
                
                log.info("用户{}待确认质押{}转为已确认", stakeUser.getUserId(), pendingAmount);
            }
        }
        
        log.info("T+1结算完成，处理{}个用户", pendingUsers.size());
    }

    /**
     * 普通商品线性释放和静态收益计算
     */
    private void calculateNormalProductReward() {
        log.info("开始计算普通商品线性释放和静态收益");

        // 获取系统配置的收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        if (rewardRate == null || rewardRate.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("收益率配置异常，跳过普通商品收益计算");
            return;
        }

        // 查询所有type=2且status=1(有效)的普通商品订单
        List<UserProduct> normalProducts = userProductService.list(
            new LambdaQueryWrapper<UserProduct>()
                .eq(UserProduct::getType, 2)  // type=2 普通商品
                .eq(UserProduct::getStatus, 1)  // status=1 有效
        );

        Date today = new Date();
        java.sql.Date sqlToday = new java.sql.Date(today.getTime());

        for (UserProduct product : normalProducts) {
            try {
                // 1. 线性释放处理
                processLinearRelease(product, sqlToday);

                // 2. 计算静态收益（基于已释放未提取金额的2倍收益）
                calculateNormalProductStaticReward(product, rewardRate);

            } catch (Exception e) {
                log.error("处理普通商品订单失败，订单号={}", product.getOrderNo(), e);
            }
        }

        log.info("普通商品线性释放和静态收益计算完成，处理{}个订单", normalProducts.size());
    }

    /**
     * 计算静态收益
     */
    private void calculateStaticReward() {
        log.info("开始计算静态收益");
        
        // 获取系统配置的收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        if (rewardRate == null || rewardRate.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("收益率配置异常，跳过静态收益计算");
            return;
        }
        
        // 查询所有有已确认质押的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );
        
        for (StakeUser stakeUser : stakeUsers) {
            BigDecimal currentAmount = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
            BigDecimal staticPool = Optional.ofNullable(stakeUser.getStaticPool()).orElse(BigDecimal.ZERO);

            // 静态复利：计算收益基数 = 已确认质押 + 静态池子
            BigDecimal rewardBase = currentAmount.add(staticPool);

            // 计算静态收益 = (已确认质押 + 静态池子) * 收益率
            BigDecimal staticReward = rewardBase.multiply(rewardRate)
                .setScale(8, RoundingMode.HALF_UP);

            if (staticReward.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal totalStatic = Optional.ofNullable(stakeUser.getTotalStatic()).orElse(BigDecimal.ZERO);

                // 更新静态池子和今日静态、累计静态（静态复利）
                stakeUserMapper.update(null,
                    new LambdaUpdateWrapper<StakeUser>()
                        .eq(StakeUser::getId, stakeUser.getId())
                        .set(StakeUser::getStaticPool, staticPool.add(staticReward))
                        .set(StakeUser::getTodayStatic, staticReward)
                        .set(StakeUser::getTotalStatic, totalStatic.add(staticReward))
                );
                
                // 记录流水
                userLogService.addLog(stakeUser.getUserId(),
                    UserLogType.StakeProfit.getValue(),
                    staticReward,
                    "质押静态收益");
                
                log.debug("用户{}静态收益：质押金额={}, 静态池={}, 收益基数={}, 收益={}",
                    stakeUser.getUserId(), currentAmount, staticPool, rewardBase, staticReward);
            }
        }
        
        log.info("静态收益计算完成，处理{}个用户", stakeUsers.size());
    }

    /**
     * 计算动态奖励
     */
    private void calculateDynamicReward() {
        log.info("开始计算动态奖励");
        
        // 计算分享奖励
        calculateShareReward();
        
        // 计算社区奖励
        calculateCommunityReward();

        // 计算平级奖励
        calculatePeerReward();

        log.info("动态奖励计算完成");
    }

    /**
     * 计算分享奖励
     */
    private void calculateShareReward() {
        log.info("开始计算分享奖励");
        
        // 查询所有有质押的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );
        
        for (StakeUser stakeUser : stakeUsers) {
            BigDecimal personalStake = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
            Long userId = stakeUser.getUserId();

            // 获取直推有效用户数量（质押100及以上）
            int validDirectUsers = getValidDirectUsers(userId);

            // 根据个人质押数量和直推有效用户数量确定奖励比例
            BigDecimal shareRewardRate = getShareRewardRate(personalStake, validDirectUsers);

            if (shareRewardRate.compareTo(BigDecimal.ZERO) > 0) {
                // 计算直推用户今日静态收益总和
                BigDecimal directStaticTotal = getDirectUsersTodayStatic(userId);

                if (directStaticTotal.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal shareReward = directStaticTotal.multiply(shareRewardRate)
                        .setScale(8, RoundingMode.HALF_UP);

                    BigDecimal dynamicPool = Optional.ofNullable(stakeUser.getDynamicPool()).orElse(BigDecimal.ZERO);
                    BigDecimal todayDynamic = Optional.ofNullable(stakeUser.getTodayDynamic()).orElse(BigDecimal.ZERO);
                    BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                    // 更新动态池子
                    stakeUserMapper.update(null,
                        new LambdaUpdateWrapper<StakeUser>()
                            .eq(StakeUser::getId, stakeUser.getId())
                            .set(StakeUser::getDynamicPool, dynamicPool.add(shareReward))
                            .set(StakeUser::getTodayDynamic, todayDynamic.add(shareReward))
                            .set(StakeUser::getTotalDynamic, totalDynamic.add(shareReward))
                    );
                    
                    // 记录流水
                    userLogService.addLog(userId,
                        UserLogType.StakeReward.getValue(),
                        shareReward,
                        "分享奖励");
                    
                    log.debug("用户{}分享奖励：直推静态={}, 奖励比例={}%, 奖励={}", 
                        userId, directStaticTotal, shareRewardRate.multiply(new BigDecimal("100")), shareReward);
                }
            }
        }
        
        log.info("分享奖励计算完成");
    }

    /**
     * 计算社区奖励
     */
    private void calculateCommunityReward() {
        log.info("开始计算社区奖励");

        // 查询所有有质押的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );

        for (StakeUser stakeUser : stakeUsers) {
            Long userId = stakeUser.getUserId();
            BigDecimal personalStake = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);

            // 计算社区业绩（团队总质押）
            BigDecimal communityPerf = getCommunityPerformance(userId);

            // 确定用户级别
            int userLevel = getUserLevel(personalStake, communityPerf, userId);

            if (userLevel > 0) {
                // 获取社区奖励比例
                BigDecimal communityRewardRate = getCommunityRewardRate(userLevel);

                if (communityRewardRate.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算团队今日静态收益总和
                    BigDecimal teamStaticTotal = getTeamTodayStatic(userId);

                    if (teamStaticTotal.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal communityReward = teamStaticTotal.multiply(communityRewardRate)
                            .setScale(8, RoundingMode.HALF_UP);

                        // 烧伤机制：检查上级是否有更高级别
                        communityReward = applyBurnMechanism(userId, userLevel, communityReward);

                        if (communityReward.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal dynamicPool = Optional.ofNullable(stakeUser.getDynamicPool()).orElse(BigDecimal.ZERO);
                            BigDecimal todayDynamic = Optional.ofNullable(stakeUser.getTodayDynamic()).orElse(BigDecimal.ZERO);
                            BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                            // 更新动态池子
                            stakeUserMapper.update(null,
                                new LambdaUpdateWrapper<StakeUser>()
                                    .eq(StakeUser::getId, stakeUser.getId())
                                    .set(StakeUser::getDynamicPool, dynamicPool.add(communityReward))
                                    .set(StakeUser::getTodayDynamic, todayDynamic.add(communityReward))
                                    .set(StakeUser::getTotalDynamic, totalDynamic.add(communityReward))
                            );

                            // 记录流水
                            userLogService.addLog(userId,
                                UserLogType.StakeReward.getValue(),
                                communityReward,
                                "社区奖励V" + userLevel);

                            log.debug("用户{}社区奖励：级别=V{}, 团队静态={}, 奖励比例={}%, 奖励={}",
                                userId, userLevel, teamStaticTotal,
                                communityRewardRate.multiply(new BigDecimal("100")), communityReward);
                        }
                    }
                }
            }
        }

        log.info("社区奖励计算完成");
    }

    /**
     * 计算平级奖励（V4-V6有5%平级奖励）
     */
    private void calculatePeerReward() {
        log.info("开始计算平级奖励");

        // 查询所有V4及以上级别的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );

        for (StakeUser stakeUser : stakeUsers) {
            Long userId = stakeUser.getUserId();
            BigDecimal personalStake = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);

            // 计算社区业绩
            BigDecimal communityPerf = getCommunityPerformance(userId);

            // 确定用户级别
            int userLevel = getUserLevel(personalStake, communityPerf, userId);

            // 只有V4-V6级别才有平级奖励
            if (userLevel >= 4 && userLevel <= 6) {
                // 计算平级奖励：同级别用户的静态收益 * 5%
                BigDecimal peerReward = calculatePeerLevelReward(userId, userLevel);

                if (peerReward.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal dynamicPool = Optional.ofNullable(stakeUser.getDynamicPool()).orElse(BigDecimal.ZERO);
                    BigDecimal todayDynamic = Optional.ofNullable(stakeUser.getTodayDynamic()).orElse(BigDecimal.ZERO);
                    BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                    // 更新动态池子
                    stakeUserMapper.update(null,
                        new LambdaUpdateWrapper<StakeUser>()
                            .eq(StakeUser::getId, stakeUser.getId())
                            .set(StakeUser::getDynamicPool, dynamicPool.add(peerReward))
                            .set(StakeUser::getTodayDynamic, todayDynamic.add(peerReward))
                            .set(StakeUser::getTotalDynamic, totalDynamic.add(peerReward))
                    );

                    // 记录流水
                    userLogService.addLog(userId,
                        UserLogType.StakeReward.getValue(),
                        peerReward,
                        "平级奖励V" + userLevel);

                    log.debug("用户{}平级奖励：级别=V{}, 奖励={}",
                        userId, userLevel, peerReward);
                }
            }
        }

        log.info("平级奖励计算完成");
    }

    /**
     * 获取直推有效用户数量（质押100及以上）
     */
    private int getValidDirectUsers(Long userId) {
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            return 0;
        }
        return stakeUserMapper.selectCount(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, directUserIds)
                .ge(StakeUser::getCurrentAmount, new BigDecimal("100"))
        ).intValue();
    }

    /**
     * 获取直推用户ID列表
     */
    private List<Long> getDirectUserIds(Long userId) {
        List<UserRelation> directUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .eq(UserRelation::getPid, userId)
        );
        return directUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
    }

    /**
     * 根据个人质押数量和直推有效用户数量获取分享奖励比例
     */
    private BigDecimal getShareRewardRate(BigDecimal personalStake, int validDirectUsers) {
        if (personalStake.compareTo(new BigDecimal("2000")) >= 0 && validDirectUsers >= 10) {
            return new BigDecimal("0.02"); // 2%
        } else if (personalStake.compareTo(new BigDecimal("1000")) >= 0 && validDirectUsers >= 6) {
            return new BigDecimal("0.05"); // 5%
        } else if (personalStake.compareTo(new BigDecimal("300")) >= 0 && validDirectUsers >= 3) {
            return new BigDecimal("0.09"); // 9%
        } else if (personalStake.compareTo(new BigDecimal("100")) >= 0 && validDirectUsers >= 1) {
            return new BigDecimal("0.12"); // 12%
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取直推用户今日静态收益总和
     */
    private BigDecimal getDirectUsersTodayStatic(Long userId) {
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<StakeUser> directStakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, directUserIds)
        );

        return directStakeUsers.stream()
            .map(user -> Optional.ofNullable(user.getTodayStatic()).orElse(BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取社区业绩（团队总质押）
     */
    private BigDecimal getCommunityPerformance(Long userId) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPath() == null) {
            return BigDecimal.ZERO;
        }

        // 查询所有下级用户的质押总和
        List<UserRelation> teamUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .like(UserRelation::getPath, userRelation.getPath() + "/" + userId + "/")
        );

        if (teamUsers.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<Long> teamUserIds = teamUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
        if (teamUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<StakeUser> teamStakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, teamUserIds)
        );

        return teamStakeUsers.stream()
            .map(user -> Optional.ofNullable(user.getCurrentAmount()).orElse(BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据个人质押、社区业绩和团队要求确定用户级别
     */
    private int getUserLevel(BigDecimal personalStake, BigDecimal communityPerf, Long userId) {
        // V6: 个人100000U + 社区300W + 3个V5
        if (personalStake.compareTo(new BigDecimal("100000")) >= 0 &&
            communityPerf.compareTo(new BigDecimal("3000000")) >= 0 &&
            getTeamLevelCount(userId, 5) >= 3) {
            return 6;
        }
        // V5: 个人50000U + 社区100W + 3个V4
        else if (personalStake.compareTo(new BigDecimal("50000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("1000000")) >= 0 &&
                 getTeamLevelCount(userId, 4) >= 3) {
            return 5;
        }
        // V4: 个人20000U + 社区30W + 3个V3
        else if (personalStake.compareTo(new BigDecimal("20000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("300000")) >= 0 &&
                 getTeamLevelCount(userId, 3) >= 3) {
            return 4;
        }
        // V3: 个人5000U + 社区10W + 2个V2
        else if (personalStake.compareTo(new BigDecimal("5000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("100000")) >= 0 &&
                 getTeamLevelCount(userId, 2) >= 2) {
            return 3;
        }
        // V2: 个人1000U + 社区3W + 2个V1
        else if (personalStake.compareTo(new BigDecimal("1000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("30000")) >= 0 &&
                 getTeamLevelCount(userId, 1) >= 2) {
            return 2;
        }
        // V1: 个人100U + 社区1W
        else if (personalStake.compareTo(new BigDecimal("100")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("10000")) >= 0) {
            return 1;
        }
        return 0;
    }

    /**
     * 获取团队中指定级别的用户数量
     */
    private int getTeamLevelCount(Long userId, int targetLevel) {
        // 获取直推用户
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (Long directUserId : directUserIds) {
            StakeUser directStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>()
                    .eq(StakeUser::getUserId, directUserId)
            );

            if (directStakeUser != null) {
                BigDecimal directPersonalStake = Optional.ofNullable(directStakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
                BigDecimal directCommunityPerf = getCommunityPerformance(directUserId);
                int directLevel = getUserLevel(directPersonalStake, directCommunityPerf, directUserId);

                if (directLevel >= targetLevel) {
                    count++;
                }
            }
        }

        return count;
    }

    /**
     * 计算平级奖励金额
     */
    private BigDecimal calculatePeerLevelReward(Long userId, int userLevel) {
        // 获取直推用户
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalPeerStatic = BigDecimal.ZERO;

        for (Long directUserId : directUserIds) {
            StakeUser directStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>()
                    .eq(StakeUser::getUserId, directUserId)
            );

            if (directStakeUser != null) {
                BigDecimal directPersonalStake = Optional.ofNullable(directStakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
                BigDecimal directCommunityPerf = getCommunityPerformance(directUserId);
                int directLevel = getUserLevel(directPersonalStake, directCommunityPerf, directUserId);

                // 只计算同级别用户的静态收益
                if (directLevel == userLevel) {
                    BigDecimal directTodayStatic = Optional.ofNullable(directStakeUser.getTodayStatic()).orElse(BigDecimal.ZERO);
                    totalPeerStatic = totalPeerStatic.add(directTodayStatic);
                }
            }
        }

        // 平级奖励 = 同级别用户静态收益总和 * 5%
        return totalPeerStatic.multiply(new BigDecimal("0.05"))
            .setScale(8, RoundingMode.HALF_UP);
    }

    /**
     * 获取社区奖励比例
     */
    private BigDecimal getCommunityRewardRate(int level) {
        switch (level) {
            case 6: return new BigDecimal("0.70"); // 70%
            case 5: return new BigDecimal("0.60"); // 60%
            case 4: return new BigDecimal("0.50"); // 50%
            case 3: return new BigDecimal("0.35"); // 35%
            case 2: return new BigDecimal("0.20"); // 20%
            case 1: return new BigDecimal("0.10"); // 10%
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 获取团队今日静态收益总和
     */
    private BigDecimal getTeamTodayStatic(Long userId) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPath() == null) {
            return BigDecimal.ZERO;
        }

        // 查询所有下级用户的今日静态收益总和
        List<UserRelation> teamUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .like(UserRelation::getPath, userRelation.getPath() + "/" + userId + "/")
        );

        if (teamUsers.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<Long> teamUserIds = teamUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
        if (teamUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<StakeUser> teamStakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, teamUserIds)
        );

        return teamStakeUsers.stream()
            .map(user -> Optional.ofNullable(user.getTodayStatic()).orElse(BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 应用烧伤机制
     * 检查上级是否有更高级别，如果有则减少奖励
     */
    private BigDecimal applyBurnMechanism(Long userId, int userLevel, BigDecimal reward) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPid() == null) {
            return reward;
        }

        // 检查上级用户级别
        Long parentId = userRelation.getPid();
        StakeUser parentStakeUser = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, parentId)
        );

        if (parentStakeUser != null) {
            BigDecimal parentPersonalStake = Optional.ofNullable(parentStakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
            BigDecimal parentCommunityPerf = getCommunityPerformance(parentId);
            int parentLevel = getUserLevel(parentPersonalStake, parentCommunityPerf, parentId);

            // 如果上级级别更高，则烧伤（减少奖励）
            if (parentLevel > userLevel) {
                // 烧伤比例可以根据级别差异调整，这里简单设为50%
                return reward.multiply(new BigDecimal("0.5"));
            }
        }

        return reward;
    }

    /**
     * 处理普通商品线性释放
     * @param product 普通商品订单
     * @param today 今天日期
     */
    private void processLinearRelease(UserProduct product, java.sql.Date today) {
        // 检查是否需要释放
        if (product.getLastReleaseDate() != null &&
            product.getLastReleaseDate().equals(today)) {
            // 今天已经释放过了，跳过
            return;
        }

        // 检查是否已经释放完成
        BigDecimal totalReleased = Optional.ofNullable(product.getTotalReleased()).orElse(BigDecimal.ZERO);
        BigDecimal orderAmount = product.getAmount();
        if (totalReleased.compareTo(orderAmount) >= 0) {
            // 已经释放完成，更新订单状态为已完成
            if (product.getStatus() != 4) {
                userProductService.update(
                    new LambdaUpdateWrapper<UserProduct>()
                        .eq(UserProduct::getId, product.getId())
                        .set(UserProduct::getStatus, 4)  // 4-释放完成
                );
                log.info("普通商品订单释放完成，订单号={}", product.getOrderNo());
            }
            return;
        }

        // 计算今日应释放金额
        BigDecimal dailyRelease = Optional.ofNullable(product.getDailyReleaseAmount()).orElse(BigDecimal.ZERO);
        if (dailyRelease.compareTo(BigDecimal.ZERO) <= 0) {
            // 如果每日释放金额为0，重新计算
            dailyRelease = orderAmount.divide(new BigDecimal("360"), 8, BigDecimal.ROUND_HALF_UP);
        }

        // 确保不超过总金额
        BigDecimal remainingAmount = orderAmount.subtract(totalReleased);
        BigDecimal actualRelease = dailyRelease.min(remainingAmount);

        if (actualRelease.compareTo(BigDecimal.ZERO) > 0) {
            // 更新释放相关字段
            userProductService.update(
                new LambdaUpdateWrapper<UserProduct>()
                    .eq(UserProduct::getId, product.getId())
                    .set(UserProduct::getTotalReleased, totalReleased.add(actualRelease))
                    .set(UserProduct::getAvailableAmount,
                        Optional.ofNullable(product.getAvailableAmount()).orElse(BigDecimal.ZERO).add(actualRelease))
                    .set(UserProduct::getLastReleaseDate, today)
                    .set(UserProduct::getDailyReleaseAmount, dailyRelease)
            );

            log.info("普通商品线性释放，订单号={}, 释放金额={}, 累计释放={}",
                product.getOrderNo(), actualRelease, totalReleased.add(actualRelease));
        }
    }

    /**
     * 计算普通商品静态收益
     * @param product 普通商品订单
     * @param rewardRate 系统收益率
     */
    private void calculateNormalProductStaticReward(UserProduct product, BigDecimal rewardRate) {
        BigDecimal availableAmount = Optional.ofNullable(product.getAvailableAmount()).orElse(BigDecimal.ZERO);

        if (availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
            // 没有可提取金额，无需计算收益
            return;
        }

        // 普通商品静态收益 = 已释放未提取金额 * 收益率 * 2
        BigDecimal normalProductReward = availableAmount.multiply(rewardRate).multiply(new BigDecimal("2"))
            .setScale(8, RoundingMode.HALF_UP);

        if (normalProductReward.compareTo(BigDecimal.ZERO) > 0) {
            // 获取或创建用户的StakeUser记录
            StakeUser stakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>()
                    .eq(StakeUser::getUserId, product.getUserId())
            );

            if (stakeUser == null) {
                // 如果用户没有StakeUser记录，创建一个
                stakeUser = new StakeUser();
                stakeUser.setUserId(product.getUserId());
                stakeUser.setTokenId("XYC"); // 默认tokenId
                stakeUser.setCurrentAmount(BigDecimal.ZERO);
                stakeUser.setPendingAmount(BigDecimal.ZERO);
                stakeUser.setStaticPool(BigDecimal.ZERO);
                stakeUser.setDynamicPool(BigDecimal.ZERO);
                stakeUser.setTodayStatic(BigDecimal.ZERO);
                stakeUser.setTotalStatic(BigDecimal.ZERO);
                stakeUser.setCreateTime(new Date());
                stakeUserMapper.insert(stakeUser);
            }

            BigDecimal currentStaticPool = Optional.ofNullable(stakeUser.getStaticPool()).orElse(BigDecimal.ZERO);
            BigDecimal currentTotalStatic = Optional.ofNullable(stakeUser.getTotalStatic()).orElse(BigDecimal.ZERO);

            // 更新用户静态池和累计静态收益
            stakeUserMapper.update(null,
                new LambdaUpdateWrapper<StakeUser>()
                    .eq(StakeUser::getId, stakeUser.getId())
                    .set(StakeUser::getStaticPool, currentStaticPool.add(normalProductReward))
                    .set(StakeUser::getTodayStatic,
                        Optional.ofNullable(stakeUser.getTodayStatic()).orElse(BigDecimal.ZERO).add(normalProductReward))
                    .set(StakeUser::getTotalStatic, currentTotalStatic.add(normalProductReward))
            );

            // 记录流水
            userLogService.addLog(product.getUserId(),
                UserLogType.StakeProfit.getValue(),
                normalProductReward,
                "普通商品静态收益-订单号:" + product.getOrderNo());

            log.debug("普通商品静态收益，订单号={}, 可提取金额={}, 收益={}",
                product.getOrderNo(), availableAmount, normalProductReward);
        }
    }
}
