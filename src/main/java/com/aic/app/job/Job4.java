package com.aic.app.job;

import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.User;
import com.aic.app.model.UserRelation;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class Job4 {

    @Resource
    private UserRelationMapper userRelationMapper;
    // cache
    final private Map<Long, String> pathCache = new HashMap<>();
    final private Map<Long, UserRelation> userCache = new HashMap<>();

    @Scheduled(cron = "*/15 * * * * ?")
    public void run() {
        int count = userRelationMapper.syncUsers();
        if (count > 0) {
            log.info("sync relation count:{}", count);
        }

        List<User> syncUsers2 = userRelationMapper.findUnSyncUsers2();
        if (syncUsers2.size() > 0) {
            log.info("sync relation change count:{}", syncUsers2.size());
        }
        for (User user : syncUsers2) {
            userRelationMapper.update(new LambdaUpdateWrapper<UserRelation>()
                    .set(UserRelation::getPid, user.getPid())
                    .set(UserRelation::getPath, null)
                    .eq(UserRelation::getId, user.getId()));
            userRelationMapper.update(new LambdaUpdateWrapper<UserRelation>()
                    .set(UserRelation::getPath, null)
                    .like(UserRelation::getPath, "/" + user.getId() + "/"));
        }

        List<UserRelation> users = userRelationMapper.findUnSyncUsers();
        if (users.isEmpty()) {
            return;
        }
        for (UserRelation user : users) {
            userCache.put(user.getId(), user);
        }
        for (UserRelation user : users) {
            log.info("sync: {}", user);
            setPath(user);
        }

        pathCache.clear();
        userCache.clear();
    }

    public void setPath(UserRelation user) {
        if (user.getPath() == null) {
            String parentPath = getParentPath(user.getPid());
            if (parentPath != null) {
                pathCache.put(user.getPid(), parentPath);
            }
            String path = parentPath + "/" + user.getId();
            user.setPath(path);
            userRelationMapper.update(new UpdateWrapper<UserRelation>()
                    .set("path", path)
                    .set("layer", path.split("/").length - 1)
                    .eq("id", user.getId()));
        }
    }

    private String getParentPath(Long parentId) {
        if (pathCache.containsKey(parentId)) {
            return pathCache.get(parentId);
        }
        StringBuilder pathBuilder = new StringBuilder();

        Set<Long> idSet = new HashSet<>();
        while (parentId != null && parentId > 0) {
            if (idSet.contains(parentId)) {
                break;
            }

            UserRelation parent = userCache.get(parentId);
            if (parent == null) {
                parent = userRelationMapper.selectById(parentId);
            }
            if (parent == null) {
                break;
            }

            idSet.add(parentId);

            if (parent.getPath() != null) {
                pathBuilder.insert(0, parent.getPath());
                break;
            } else {
                pathBuilder.insert(0, "/" + parentId);
            }
            parentId = parent.getPid();
        }
        if (idSet.isEmpty()) {
            return "";
        }
        return pathBuilder.toString();
    }

}
