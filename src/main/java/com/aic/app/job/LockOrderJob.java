package com.aic.app.job;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserLogMapper;
import com.aic.app.service.ILockOrderService;
import com.aic.app.service.IProductService;
import com.aic.app.service.ISysConfigService;
import com.aic.app.model.SysConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 锁仓订单结算Job - 每天早晚8点执行
 * 功能：
 * 1. 处理不同类型的释放（线性、阶梯、一次性）
 * 2. 计算静态收益
 * 3. 计算动态收益
 */
@Component
@Slf4j
public class LockOrderJob {
    
    @Resource
    private ILockOrderService lockOrderService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    IProductService productService;
    @Resource
    private StakeUserMapper stakeUserMapper;

    @Resource
    private LockOrderMapper lockOrderMapper;
    @Resource
    private UserLogMapper userLogMapper;
    
    @Scheduled(cron = "0 0 8 * * ?") // 每天8点执行
    public void run8() {
        log.info("开始执行锁仓订单结算Job");
        
        try {
            // 1. 处理不同类型的释放
            processAllReleaseTypes();
            
            // 2. 计算静态收益
            lockOrderService.calculateStaticRewards();
            
            // 3. 计算动态收益
            lockOrderService.calculateDynamicRewards();

            updateTotalAmount();
            
            log.info("锁仓订单结算Job执行完成");
        } catch (Exception e) {
            log.error("锁仓订单结算Job执行失败", e);
        }
    }

    @Scheduled(cron = "0 0 20 * * ?") // 每天20点执行
    public void run20() {
        log.info("开始执行锁仓订单结算Job");

        try {
            // 1. 处理不同类型的释放
            processAllReleaseTypes();

            // 2. 计算静态收益
            lockOrderService.calculateStaticRewards();

            // 3. 计算动态收益
            lockOrderService.calculateDynamicRewards();

            // 4、更新当前指数 currentIndex, 每天一次
            updateCurrentIndex();

            updateTotalAmount();

            log.info("锁仓订单结算Job执行完成");
        } catch (Exception e) {
            log.error("锁仓订单结算Job执行失败", e);
        }
    }
    
    @PostConstruct
    public void initIndex() {
        // 如果redis中当前指数为0，就设置为1，有就忽略
        log.info("开始初始化当前指数");

        try {
            String redisKey = "xyc:stats";
            String indexField = "currentIndex";

            // 从Redis获取当前指数值
            Object currentIndexObj = stringRedisTemplate.opsForHash().get(redisKey, indexField);

            if (currentIndexObj == null) {
                // Redis中没有指数值，设置为1
                stringRedisTemplate.opsForHash().put(redisKey, indexField, "1");
                log.info("Redis中没有当前指数，已初始化为1");
            } else {
                // 检查当前值是否为0
                BigDecimal currentIndex = new BigDecimal(currentIndexObj.toString());
                if (currentIndex.compareTo(BigDecimal.ZERO) == 0) {
                    // 当前指数为0，设置为1
                    stringRedisTemplate.opsForHash().put(redisKey, indexField, "1");
                    log.info("当前指数为0，已重置为1");
                } else {
                    log.info("当前指数已存在，值为: {}", currentIndex);
                }
            }
        } catch (Exception e) {
            log.error("初始化当前指数失败", e);
        }
    }

    // update 总利息 to redis
    public void updateTotalInterest() {
        log.info("开始更新总利息");

        try {
            String redisKey = "xyc:stats";
            String totalInterestField = "totalInterest";

            // 1. 获取总利息
            BigDecimal totalInterest = BigDecimal.ZERO;

            // 总利息（活期+定期，单位XYC）质押+锁仓全网静动态, 从userLog中sum统计
            totalInterest = userLogMapper.getTotalInterest();


            // 2. 更新Redis中的总利息
            stringRedisTemplate.opsForHash().put(redisKey, totalInterestField, totalInterest.toPlainString());

            log.info("总利息更新成功: {}", totalInterest.toPlainString());
        } catch (Exception e) {
            log.error("更新总利息失败", e);
        }
    }

    // 更新全网总只有量 = 每次结算完更新 = （质押本金+锁仓）* XYC价格
    public void updateTotalAmount() {
        log.info("开始更新全网总只有量");

        try {
            String redisKey = "xyc:stats";
            String totalAmountField = "totalAmount";

            // 1. 获取质押本金
            BigDecimal totalStakeAmount = stakeUserMapper.getTotalStakeAmount("XYC");
            if (totalStakeAmount == null) {
                totalStakeAmount = BigDecimal.ZERO;
            }

            // 2. 获取锁仓总额
            BigDecimal totalLockAmount = lockOrderMapper.getTotalLockAmount("XYC");
            if (totalLockAmount == null) {
                totalLockAmount = BigDecimal.ZERO;
            }

            // 3. 获取XYC价格
            BigDecimal xycPrice = productService.getPrice("XYC");
            if (xycPrice == null) {
                xycPrice = BigDecimal.ZERO;
            }

            // 4. 计算全网总只有量
            BigDecimal totalAmount = totalStakeAmount.add(totalLockAmount).multiply(xycPrice);

            // 5. 更新Redis中的全网总只有量
            stringRedisTemplate.opsForHash().put(redisKey, totalAmountField, totalAmount.toPlainString());

            log.info("全网总只有量更新成功: {}", totalAmount.toPlainString());
        } catch (Exception e) {
            log.error("更新全网总只有量失败", e);
        }
    }

    public void updateCurrentIndex() {
        // 当前指数（从开盘第一天开始，第一天是1，第二天是1.08，不用美元单位）初始值，每次结算 = 当前值 * （1+静态收益）
        log.info("开始更新当前指数");

        try {
            String redisKey = "xyc:stats";
            String indexField = "currentIndex";

            // 1. 获取当前指数
            Object currentIndexObj = stringRedisTemplate.opsForHash().get(redisKey, indexField);
            if (currentIndexObj == null) {
                log.warn("Redis中没有当前指数，跳过更新");
                return;
            }

            BigDecimal currentIndex = new BigDecimal(currentIndexObj.toString());
            log.info("当前指数值: {}", currentIndex);

            // 2. 获取系统配置的静态收益率
            SysConfig sysConfig = sysConfigService.getSysConfig();
            if (sysConfig == null || sysConfig.getRewardRate() == null) {
                log.warn("系统配置或收益率为空，跳过指数更新");
                return;
            }

            BigDecimal rewardRate = sysConfig.getRewardRate();
            if (rewardRate.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("收益率配置异常: {}，跳过指数更新", rewardRate);
                return;
            }

            log.info("静态收益率: {}", rewardRate);

            // 3. 计算新的指数值：新指数 = 当前指数 * (1 + 静态收益率)
            BigDecimal multiplier = BigDecimal.ONE.add(rewardRate);
            BigDecimal newIndex = currentIndex.multiply(multiplier)
                .setScale(8, RoundingMode.HALF_UP);

            // 4. 更新Redis中的指数值
            stringRedisTemplate.opsForHash().put(redisKey, indexField, newIndex.toPlainString());

            log.info("指数更新成功: {} -> {}, 收益率: {}",
                currentIndex.toPlainString(),
                newIndex.toPlainString(),
                rewardRate.toPlainString());

        } catch (Exception e) {
            log.error("更新当前指数失败", e);
        }
    }
    
    private void processAllReleaseTypes() {
        lockOrderService.processLinearRelease();
    }
}