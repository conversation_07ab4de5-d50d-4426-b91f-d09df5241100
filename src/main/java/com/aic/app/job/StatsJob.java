package com.aic.app.job;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.model.AssetEnum;
import com.aic.app.service.IProductService;
import com.aic.app.service.ISysConfigService;
import com.aic.app.service.RustService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Stats related scheduled tasks
 */
@Component
@Slf4j
public class StatsJob {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private IProductService productService;
    
    @Resource
    private StakeUserMapper stakeUserMapper;
    
    @Resource
    private LockOrderMapper lockOrderMapper;
    
    @Resource
    private ISysConfigService sysConfigService;

    /**
     * Save XYC stats to Redis every minute
     * key: xyc:stats
     * fields: xycPrice, burnAmount, totalSupply
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void saveXycPriceToRedis() {
        log.info("Starting to save XYC stats to Redis");
        
        try {
            // Get XYC price
            BigDecimal xycPrice = productService.getPrice(AssetEnum.XYC.getTokenId());
            
            if (xycPrice != null && xycPrice.compareTo(BigDecimal.ZERO) > 0) {
                // Save XYC price to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "xycPrice", xycPrice.toPlainString());
                log.info("Successfully saved XYC price to Redis: {}", xycPrice.toPlainString());
            } else {
                log.warn("Invalid XYC price obtained: {}", xycPrice);
            }
            
        } catch (Exception e) {
            log.error("Failed to save XYC price to Redis", e);
        }
        
        try {
            // Get XYC burn amount
            String burnAmountStr = RustService.getXycBurnAmount();
            if (burnAmountStr != null && !burnAmountStr.trim().isEmpty()) {
                // Save burn amount to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "burnAmount", burnAmountStr);
                log.info("Successfully saved XYC burn amount to Redis: {}", burnAmountStr);
            } else {
                log.warn("Invalid XYC burn amount obtained: {}", burnAmountStr);
            }
            
        } catch (Exception e) {
            log.error("Failed to save XYC burn amount to Redis", e);
        }
        
        try {
            // Get XYC total supply
            String totalSupplyStr = RustService.getXycTotalSupply();
            if (totalSupplyStr != null && !totalSupplyStr.trim().isEmpty()) {
                // Save total supply to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "totalSupply", totalSupplyStr);
                log.info("Successfully saved XYC total supply to Redis: {}", totalSupplyStr);
            } else {
                log.warn("Invalid XYC total supply obtained: {}", totalSupplyStr);
            }
            
        } catch (Exception e) {
            log.error("Failed to save XYC total supply to Redis", e);
        }
    }

    /**
     * Save XYC stake principal and APY to Redis every 5 minutes
     * Includes confirmed + pending stake amount + lock amount
     * key: xyc:stats
     * fields: stakePrincipal, apy
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void saveXycStakePrincipalToRedis() {
        log.info("Starting to save XYC stake principal and APY to Redis");
        
        try {
            String xycTokenId = AssetEnum.XYC.getTokenId();
            
            // Get total stake amount (confirmed + pending)
            BigDecimal totalStakeAmount = stakeUserMapper.getTotalStakeAmount(xycTokenId);
            if (totalStakeAmount == null) {
                totalStakeAmount = BigDecimal.ZERO;
            }
            
            // Get total lock amount
            BigDecimal totalLockAmount = lockOrderMapper.getTotalLockAmount(xycTokenId);
            if (totalLockAmount == null) {
                totalLockAmount = BigDecimal.ZERO;
            }
            
            // Calculate total principal
            BigDecimal totalPrincipal = totalStakeAmount.add(totalLockAmount);
            
            // Save stake principal to Redis Hash
            stringRedisTemplate.opsForHash().put("xyc:stats", "stakePrincipal", totalPrincipal.toPlainString());
            
            log.info("Successfully saved XYC stake principal to Redis: stake={}, lock={}, total={}", 
                    totalStakeAmount.toPlainString(), 
                    totalLockAmount.toPlainString(), 
                    totalPrincipal.toPlainString());
            
        } catch (Exception e) {
            log.error("Failed to save XYC stake principal to Redis", e);
        }
        
        try {
            // Get static reward rate from SysConfig
            var sysConfig = sysConfigService.getSysConfig();
            if (sysConfig != null && sysConfig.getRewardRate() != null) {
                BigDecimal staticRate = sysConfig.getRewardRate();
                
                // Calculate APY = (1 + staticRate)^730
                // Using Math.pow for the calculation
                double rate = staticRate.doubleValue();
                double apy = (1 + rate) * 730;
                BigDecimal apyDecimal = BigDecimal.valueOf(apy);
                
                // Save APY to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "apy", apyDecimal.toPlainString());
                
                log.info("Successfully saved APY to Redis: staticRate={}, apy={}", 
                        staticRate.toPlainString(), 
                        apyDecimal.toPlainString());
            } else {
                log.warn("SysConfig or rewardRate is null, cannot calculate APY");
            }
            
        } catch (Exception e) {
            log.error("Failed to save APY to Redis", e);
        }
    }
}