package com.aic.app.sdk;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthResult {
    int code;
    String msg;
    AuthData data;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AuthData {
        String accessToken;
        String userId;
        String openId;
        String nickName;
        String avatarUrl;
        String refreshToken;
    }
}
