package com.aic.app.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssetEnum implements IAsset {
    
    BNB("BNB", "BNB"),
    XYC("XYC", "XYC"),

    USDT("USDT", "USDT"),
    JU("JU", "JU");

    final String tokenId;
    final String name;
    
    public static IAsset nameOf(String name) {
        for (AssetEnum asset : values()) {
            if (asset.name.equals(name)) {
                return asset;
            }
        }
        return new OtherAsset(name, name);
    }

    public static IAsset of(String name) {
        try {
            return valueOf(name);   
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
