package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@NoArgsConstructor
public class BurnLog {

    @TableId(type = IdType.AUTO)
    Integer id;
    
    LocalDate day;
    
    BigDecimal amount;

    /**
     * 创建时间
     */
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;

    public BurnLog(LocalDate day, BigDecimal amount) {
        this.day = day;
        this.amount = amount;
        this.createTime = new Date();
    }
}
