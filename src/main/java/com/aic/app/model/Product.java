package com.aic.app.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 理财产品
 */
@Getter
@Setter
public class Product extends BaseEntity {

    /**
     * 类型(1-预售节点 2-普通商品 3-债券 4-城主)
     */
    int type;

    /**
     * 名称
     */
    String name;

    /**
     * 描述
     */
    String description;

    /**
     * 日收益率
     */
    BigDecimal rate;

    /**
     * 手续费
     */
    BigDecimal fee;

    /**
     * 天
     */
    Integer day;

    /**
     * 是否有效
     */
    Boolean enable;

    /**
     * 图片
     */
    String image;

    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 已售数量
     */
    private BigDecimal sold;

    private BigDecimal total;

    /**
     * 折扣率
     */
    private BigDecimal discount;
    
    /**
     * 限制时间段 (格式: "10:00-13:00", 为空表示全天可抢购)
     */
    private String limitTime;
    
}
