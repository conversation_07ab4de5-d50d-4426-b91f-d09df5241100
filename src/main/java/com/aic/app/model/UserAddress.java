package com.aic.app.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserAddress extends BaseEntity{
    private Long userId;
    @Schema(description = "联系人")
    private String contact;
    @Schema(description = "区号")
    private String area;
    @Schema(description = "电话")
    private String phone;
    @Schema(description = "地址")
    private String address;

    public UserAddress(Long userId, String contact, String area, String phone, String address) {
        this.userId = userId;
        this.contact = contact;
        this.area = area;
        this.phone = phone;
        this.address = address;
        super.setCreateTime(new Date());
    }

}
