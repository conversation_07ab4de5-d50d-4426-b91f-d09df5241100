package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 锁仓订单表
 */
@Data
@NoArgsConstructor
@TableName("lock_order")
public class LockOrder {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 锁仓订单号
     */
    private String orderNo;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 来源类型: 1-节点产品 2-普通商品 3-债券产品 4-质押 5-其他
     */
    private Integer sourceType;
    
    /**
     * 来源ID(user_product.id或其他业务ID)
     */
    private Long sourceId;
    
    /**
     * 代币ID
     */
    private String tokenId;
    
    /**
     * 锁仓总金额
     */
    private BigDecimal lockAmount;
    
    /**
     * 已释放金额
     */
    private BigDecimal releasedAmount;
    
    /**
     * 可提取金额(已释放未提取)
     */
    private BigDecimal availableAmount;
    
    /**
     * 总锁仓天数
     */
    private Integer totalDays;
    
    /**
     * 已释放天数
     */
    private Integer releasedDays;
    
    /**
     * 每日释放金额
     */
    private BigDecimal dailyReleaseAmount;
    
    /**
     * 状态: 0-待激活 1-锁仓中 2-已完成 3-已取消
     */
    private Integer status;
    
    /**
     * 锁仓配置(阶梯释放规则等)
     */
    private String lockConfig;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    // 构造方法
    public LockOrder(String orderNo, Long userId, Integer sourceType, Long sourceId, String tokenId, BigDecimal lockAmount) {
        this.orderNo = orderNo;
        this.userId = userId;
        this.sourceType = sourceType;
        this.sourceId = sourceId;
        this.tokenId = tokenId;
        this.lockAmount = lockAmount;
        this.releasedAmount = BigDecimal.ZERO;
        this.availableAmount = BigDecimal.ZERO;
        this.releasedDays = 0;
        this.status = 1; // 默认锁仓中
        this.createTime = new Date();
    }
}