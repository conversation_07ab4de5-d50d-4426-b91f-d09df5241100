package com.aic.app.model;

import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * 
 * @TableName node_product
 */
@Getter
@Setter
@Schema(description = "节点产品")
public class NodeProduct extends BaseEntity {
    /**
     * TOKEN
     */
    @Schema(description = "TOKEN")
    private String tokenId;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal price;

    /**
     * 返佣比例
     */
    @Schema(description = "返佣比例")
    private BigDecimal parentRate;

    /**
     * 合约地址
     */
    @Schema(description = "合约地址")
    private String address;
}
