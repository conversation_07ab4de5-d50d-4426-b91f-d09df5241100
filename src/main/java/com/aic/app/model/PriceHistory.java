package com.aic.app.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName price_history
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PriceHistory extends BaseEntity {
    /**
     * TOKEN
     */
    private String tokenId;

    /**
     * 
     */
    private BigDecimal price;

    /**
     * 
     */
    private Long time;
}
