package com.aic.app.model.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class SysUser {
    @TableId(type = IdType.AUTO)
    Integer id;

    /**
     * 账号
     */
    String account;


    /**
     * 密码
     */
    String password;

    /**
     * 昵称
     */
    String name;

    /**
     * 0-正常 1-禁用
     */
    int state;

    /**
     * 谷歌二次验证私钥
     */
    String googleCode;

    /**
     * 角色: ADMIN-管理员 NORMAL-普通角色
     */
    String role; // 默认为普通角色

    /**
     * 创建时间
     */
    Date createDate;

}
