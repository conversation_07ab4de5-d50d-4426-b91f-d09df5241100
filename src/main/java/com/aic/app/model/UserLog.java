package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserLog {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Long userId;
    private int type;
    private BigDecimal productAmount;
    private BigDecimal amount;
    private BigDecimal lastAmount;
    private Date createTime;
    private String remark;
    private String symbol;
    private String tokenId;
    
    private transient UserProduct userProduct;
    private transient String code;
    private transient String address;

    public UserLog(Long userId, int type, BigDecimal productAmount, BigDecimal amount, String remark) {
        this.userId = userId;
        this.type = type;
        this.productAmount = productAmount;
        this.amount = amount;
        this.remark = remark;
        this.createTime = new Date();
    }

    public UserLog(Long userId, int type, BigDecimal productAmount, BigDecimal amount, String tokenId, String symbol, String remark) {
        this.userId = userId;
        this.type = type;
        this.productAmount = productAmount;
        this.amount = amount;
        this.tokenId = tokenId;
        this.symbol = symbol;
        this.remark = remark;
        this.createTime = new Date();
    }
}
