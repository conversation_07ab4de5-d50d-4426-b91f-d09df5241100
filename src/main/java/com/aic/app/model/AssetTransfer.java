package com.aic.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AssetTransfer extends BaseEntity {

    /**
     * 手机到平台(转出)
     */
    public static int TYPE_OUT = 0;
    /**
     * 平台到手机(转入)
     */
    public static int TYPE_IN = 1;

    /**
     * 订单ID
     */
    private String orderNo;
    
    // 划转类型 0-手机到平台 1-平台到手机
    @Schema(description = "划转类型 0-转出 1-转入")
    private int type;
    private Long userId;
    private String tokenId;
    private BigDecimal quantity;
    // 状态 0-待确认 1-成功 2-失败
    @Schema(description = "状态 0-待确认 1-成功 2-失败")
    private int status;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @Schema(description = "确认时间")
    private Date confirmTime;
    
    private transient String code;

    public AssetTransfer(int type, Long userId, String tokenId, BigDecimal quantity) {
        this.type = type;
        this.userId = userId;
        this.tokenId = tokenId;
        this.quantity = quantity;
        this.status = 0;
        this.setCreateTime(new Date());
    }
}
