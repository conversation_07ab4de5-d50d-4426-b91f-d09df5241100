package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 返佣记录
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class RewardLog {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 下级用户ID
     */
    private Long childUserId;
    /**
     * 等级
     */
    private int level;
    /**
     * 理财数量
     */
    private BigDecimal productAmount;
    /**
     * 返佣数量
     */
    private BigDecimal amount;
    /**
     * 创建时间
     */
    private Date createTime;
    
    private String symbol;
    
    private Integer productId;
    private String payMethod;

    private BigDecimal price;

    private transient String code;
    private transient String address;
    
    public RewardLog(Long userId, StakeUser childUser, BigDecimal amount) {
        this.userId = userId;
//        this.childUserId = childUser.getId();
//        this.level = childUser.getLevel();
        this.amount = amount;
        this.createTime = new Date();
    }
    
    public RewardLog(Long userId, UserProduct userProduct, BigDecimal amount, String symbol, Integer productId, String payMethod) {
        this.userId = userId;
        this.childUserId = userProduct.getUserId();
        this.level = -1;
        this.productAmount = userProduct.getPower();
        this.amount = amount;
        this.createTime = new Date();
        this.symbol = symbol;
        this.productId = productId;
        this.payMethod = payMethod;
    }
}
