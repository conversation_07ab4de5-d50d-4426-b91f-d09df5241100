package com.aic.app.model;

import com.aic.app.vo.UserAssetVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MyAssetVo {

    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "邀请码")
    private String code;

    @Schema(description = "我的资产列表")
    List<UserAssetVo> list;

    public MyAssetVo(User user, List<UserAsset> list) {
        if (user != null) {
            this.userId = user.getId();
            this.code = user.getCode();
        }
        this.list = list.stream().map(UserAssetVo::new).toList();
    }
}
