package com.aic.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class User extends BaseEntity {
    int type;
    String address;
    String code;
    Long pid;
    int level;
    int preLevel;
    Date lastTime;

    /**
     * 是否锁定等级
     */
    boolean lockLevel;

    /**
     * 设置等级时间
     */
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date setLevelTime;

    /**
     * 4倍收益次数
     */
    private int quadrupleRewardTimes;

    String juid;

    public User(Long id) {
        this.id = id;
    }

    /**
     * 是否有推荐人
     * @return
     */
    public boolean hasParent() {
        return ObjectUtils.isNotEmpty(pid) && !Long.valueOf(0).equals(pid);
    }

}
