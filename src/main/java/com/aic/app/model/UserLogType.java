package com.aic.app.model;

import lombok.Getter;

/**
 * 流水类型
 */
@Getter
public enum UserLogType {

    RegularBuy("认购", 1),

    Stake("质押", 2),

    StakeProfit("质押收益", 3),

    InviteProfit("返佣奖励", 4),

    RedeemCurrent("赎回质押", 5),

    ProjectAirdrop("项目空投", 6),

    RECEIVE("提取收益", 7),

    WithdrawFee("提现手续费", 8),

    WithdrawWeek("提现周分红", 9),

    Transfer_Out("划出", 11),

    Transfer_In("划入", 12),

    Airdrop("空投", 13),
    BuyNode("购买节点", 14), 
    BuyNodeReward("购买节点返佣", 15),
    WithdrawNodeReward("提现节点返佣", 16),  
    WithdrawNodePool("提现节点池", 17), 
    NodeDividend("节点分红", 18),
    WithdrawNodeRewardFail("提现节点返佣失败", 19), 
    WeekDynamic("周分红", 20),
    StakeRelease("分期释放", 21),
    StakeReward("质押返佣", 39),
    StakeReward2("直推10%", 45),
    Burn("销毁", 46),
    
    // 锁仓释放相关
    LOCK_RELEASE_LINEAR("线性释放", 201),
    LOCK_RELEASE_STEP("阶梯释放", 202),
    LOCK_RELEASE_ONETIME("一次性释放", 203),
    
    // 锁仓收益相关
    LOCK_STATIC_REWARD("静态收益", 211),
    LOCK_DYNAMIC_REWARD("动态收益", 212),
    LOCK_COMPOUND_REWARD("复利收益", 213),
    
    // 锁仓状态变更
    LOCK_ORDER_CREATE("锁仓订单创建", 221),
    LOCK_ORDER_COMPLETE("锁仓订单完成", 222),
    LOCK_ORDER_CANCEL("锁仓订单取消", 223),
    
    // 产品购买相关
    PRODUCT_PURCHASE("购买产品", 230),
    
    // 城主商品相关
    CITY_LORD_PURCHASE("购买城主商品", 231),
    QUADRUPLE_REWARD_ADD("增加4倍收益次数", 232),
    QUADRUPLE_REWARD_USE("使用4倍收益次数", 233);
    

    final String label;
    final int value;

    UserLogType(String label, int value) {
        this.label = label;
        this.value = value;
    }
    
    public int getCode() {
        return this.value;
    }

}
