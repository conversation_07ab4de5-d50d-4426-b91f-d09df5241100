package com.aic.app.model;

import com.aic.app.util.ListStringHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@TableName(autoResultMap = true)
@Data
public class SysConfig {

    @TableId(type = IdType.AUTO)
    Integer id;

    /**
     * 收益率
     */
    private BigDecimal rewardRate;

}
