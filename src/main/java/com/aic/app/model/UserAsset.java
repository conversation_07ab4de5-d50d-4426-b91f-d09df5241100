package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserAsset extends BaseEntity {
    private Long userId;
    private String tokenId;
    private BigDecimal balance;
    @Version
    private int version;
    
    private transient String code;
    private transient String tokenAddress;
    private transient String stakeAddress;
    private transient Boolean stake;
    private transient String logo;

    public UserAsset(Long userId, String tokenId, BigDecimal balance) {
        this.userId = userId;
        this.tokenId = tokenId;
        this.balance = balance;
    }

    public boolean checkBalance(BigDecimal amount) {
        return balance.compareTo(amount) >= 0;
    }
}
