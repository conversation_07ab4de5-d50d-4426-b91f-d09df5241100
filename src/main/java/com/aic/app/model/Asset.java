package com.aic.app.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class Asset {
    private Integer id;
    private String tokenId;
    private String tokenName;
    private Boolean stake;
    private String idoAddress;
    private String stakeAddress;
    private String tokenAddress;
    private String nodeAddress;
    private String withdrawAddress;
    private BigDecimal nodePrice;
    private BigDecimal nodeReward;
    
    // 总质押
    private BigDecimal totalStaked;
    // 回购池
    private BigDecimal totalSupply;
    // 回购池BNB
    private BigDecimal totalSupplyBnb;
    // 销毁池
    private BigDecimal totalBurn;
    // dao池
    private BigDecimal totalDao;
    // 周分红池
    private BigDecimal totalWeekPool;
    // 周分红池(实发)
    private BigDecimal realWeekPool;
    // 节点池
    private BigDecimal nodePool;

    /**
     * 节点目标业绩
     */
    private BigDecimal nodeTarget;
    /**
     * 节点未达业绩拿的比例
     */
    private BigDecimal nodeRate;
    
}
