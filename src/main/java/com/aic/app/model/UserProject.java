package com.aic.app.model;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName user_project
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserProject extends BaseEntity {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 状态：0-未发币 1-已发币
     */
    private int state;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 已购买数量
     */
    private Integer quantity;
}
