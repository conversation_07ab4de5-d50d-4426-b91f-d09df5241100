package com.aic.app.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class RewardDay extends BaseEntity {
    String day;
    BigDecimal totalStatic;
    BigDecimal totalDynamic;
    BigDecimal totalReward;
    BigDecimal totalAmount;
}
