package com.aic.app.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName project
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "project", autoResultMap = true)
public class Project extends BaseEntity {
    /**
     * 
     */
    private String name;
    
    private String logo;

    /**
     * 
     */
    private String description;

    /**
     * 支付方式
     */
    private String payToken;

    /**
     * 发放Token
     */
    private String token;

    /**
     * 
     */
    private String website;

    /**
     * 
     */
    private String twitter;

    /**
     * 
     */
    private String telegram;

    /**
     * 状态：0-未开始 1-已开始 2-已结束
     */
    private Integer state;

    /**
     * 是否启用：0-否 1-是
     */
    private Boolean enable;

    /**
     * 总量
     */
    private BigDecimal total;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 已购买数量
     */
    private Integer quantity;

    /**
     * 每份多少Token
     */
    private BigDecimal perToken;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 预售时间
     */
    private long presaleTime;

    /**
     * 结束时间
     */
    private long endTime;
    
    private Boolean isSend;
    
    private Date sendTime;
    
    @TableField(typeHandler = TypeHandler.class)
    private InfoData info;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 是否已结束
     * @return
     */
    public boolean isEnd() {
        long now = System.currentTimeMillis() / 1000;
        return now > endTime;
    }
    
    public boolean isOpen() {
        long now = System.currentTimeMillis() / 1000;
        return now >= startTime && now <= endTime;
    }
    
    public boolean isPresale() {
        long now = System.currentTimeMillis() / 1000;
        return now >= presaleTime && now <= startTime;
    }
    
    public static class TypeHandler extends JacksonTypeHandler {
        public TypeHandler() {
            super(InfoData.class);
        }
    }
    
    public static class InfoData extends HashMap<String, Object> {
        public InfoData() {
            super();
        }
    }
}
