package com.aic.app.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
public class StakeUser extends BaseEntity implements Cloneable {

    private Long userId;
    
    private String tokenId;

    /**
     * 活期
     */
    private BigDecimal currentAmount;

    /**
     * 待确认数量
     */
    private BigDecimal pendingAmount;

    /**
     * 静态池子
     */
    private BigDecimal staticPool;
    /**
     * 动态池子
     */
    private BigDecimal dynamicPool;

    /**
     * 锁仓静态池
     */
    private BigDecimal lockStaticPool;

    /**
     * 锁仓动态池
     */
    private BigDecimal lockDynamicPool;

    /**
     * 今日静态
     */
    private BigDecimal todayStatic;
    /**
     * 累计静态
     */
    private BigDecimal totalStatic;

    /**
     * 可领取
     */
    private BigDecimal canReceive;
    
    /**
     * 今日动态
     */
    private BigDecimal todayDynamic;

    /**
     * 累计动态
     */
    private BigDecimal totalDynamic;

    /**
     * 节点返佣
     */
    private BigDecimal nodeReward;
    /**
     * 节点池
     */
    private BigDecimal nodePool;

    /**
     * 伞下业绩
     * 大小区都算
     */
    private BigDecimal sumAmount;

    private transient Long pid;
    private transient int level;
    /**
     * 临时静态收益
     */
    private transient BigDecimal tmpStatic;

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }


    @Override
    public StakeUser clone() {
        try {
            StakeUser clone = (StakeUser) super.clone();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }

    /**
     * 总算力 = 已确认质押 + 静态 + 动态
     * @return
     */
    public BigDecimal getTotalPower() {
        return Optional.ofNullable(currentAmount).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(staticPool).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(dynamicPool).orElse(BigDecimal.ZERO));
    }

    /**
     * 总质押数量 = 已确认 + 待确认
     * @return
     */
    public BigDecimal totalStakeAmount() {
        return Optional.ofNullable(currentAmount).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(pendingAmount).orElse(BigDecimal.ZERO));
    }

    /**
     * 有效质押1000，才能拿动态
     * @return
     */
    public boolean validNode() {
        if (currentAmount == null) {
            return false;
        }
        return currentAmount.compareTo(new BigDecimal("1000")) >= 0;
    }
}
