package com.aic.app.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class StakeUserModel extends StakeUser {
//    Long pid;
    String refCode;
    String code;
    String address;
    int layer;
    BigDecimal totalAmount;
    
}
