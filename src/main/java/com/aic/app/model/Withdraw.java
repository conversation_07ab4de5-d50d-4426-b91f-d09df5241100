package com.aic.app.model;

import java.math.BigDecimal;
import java.util.Date;

import com.aic.app.exception.BizException;
import com.aic.app.exception.Errors;
import com.aic.app.service.RustService;
import com.aic.app.util.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName withdraw
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class Withdraw extends BaseEntity {

    /**
     * 类型：0.提币、 1.释放、 2.提周分红  3.提节点推荐奖励  4.提节点分红
     */
    private Integer type;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 随机数
     */
    private Long nonce;

    /**
     * 合约
     */
    private String contract;

    /**
     * TOKEN
     */
    private String tokenId;

    /**
     * 
     */
    private String address;

    /**
     * 
     */
    private String token;

    /**
     * 
     */
    private String sign;

    /**
     * 
     */
    private BigDecimal quantity;

    /**
     * 
     */
    private BigDecimal fee;

    /**
     * 
     */
    private String txid;

    /**
     * 
     */
    private String data;

    /**
     * 状态 0-待确认 1-审核中 2-成功 3-拒绝回退 4-取消提现
     */
    private Integer state;

    /**
     * 
     */
    private String err;

    /**
     * 
     */
    private Long deadline;

    /**
     * 产品ID
     */
    private Integer productId;
    
    /**
     * 
     */
    private Date updateTime;

    public Withdraw(int type, User user, Asset asset, Integer productId, BigDecimal quantity, BigDecimal fee, RustService.WithdrawSign withdrawSignVo) {
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw Errors.REQUEST_EXCEPTION;
        }
        this.type = type;
        this.userId = user.getId();
        this.contract = asset.getWithdrawAddress();
        this.tokenId = asset.getTokenId();
        this.nonce = withdrawSignVo.getNonce();
        this.address = user.getAddress();
        this.token = withdrawSignVo.getToken();
        this.sign = withdrawSignVo.getSign();
        this.productId = productId;
        this.quantity = quantity;
        this.fee = fee;
        this.data = JsonUtils.toJson(withdrawSignVo);
        this.state = 0;
        this.deadline = withdrawSignVo.getDeadline();
    }

}
