package com.aic.app.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class StakeModel {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 父级id
     */
    private Long pid;

    /**
     * 代币id
     */

    /**
     * 业绩
     */
    private BigDecimal amount;

    /**
     * 最大团队业绩
     */
    private BigDecimal maxTeamPerf;

    /**
     * 最大节点业绩
     */
    private BigDecimal maxNodePerf;
    
    private int node;

    /**
     * 团队业绩
     */
    private BigDecimal teamPerf = BigDecimal.ZERO;    

    /**
     * 节点业绩
     */
    private BigDecimal nodePerf = BigDecimal.ZERO;

    /**
     * 节点池
     */
    private BigDecimal nodePool = BigDecimal.ZERO;

    /**
     * 活期
     */
    private BigDecimal currentAmount;

    /**
     * 待确认数量
     */
    private BigDecimal pendingAmount;
    /**
     * 伞下业绩
     */
    private BigDecimal sumAmount = BigDecimal.ZERO;

}
