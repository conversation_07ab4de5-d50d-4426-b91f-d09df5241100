package com.aic.app.service;

import com.aic.app.form.WithdrawNormalProductForm;
import com.aic.app.model.User;
import com.aic.app.model.UserProduct;
import com.aic.app.service.RustService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

public interface IUserProductService extends IService<UserProduct> {
    
    BigDecimal sum();
    
    UserProduct findUserProduct(String userId, int productId);

    UserProduct getUserProduct(String uid, int id);

    void updateProduct6Rate();

    IPage<UserProduct> pageChildOrders(Page<UserProduct> of, Wrapper<UserProduct> qw);

    /**
     * 普通商品提取到钱包
     * @param user 用户
     * @param form 提取表单
     * @return 提取签名
     */
    RustService.WithdrawSign withdrawNormalProductWallet(User user, WithdrawNormalProductForm form);
}
