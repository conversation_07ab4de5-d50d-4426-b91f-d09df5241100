package com.aic.app.service;

import com.aic.app.form.StakeForm;
import com.aic.app.form.RedeemForm;
import com.aic.app.form.ClaimForm;
import com.aic.app.form.WithdrawForm;
import com.aic.app.model.*;
import com.aic.app.sdk.ApiUser;
import com.aic.app.vo.MyProductDataVo;
import com.aic.app.vo.RankVo;
import com.aic.app.vo.UserAsset;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

public interface IStakeUserService extends IService<StakeUser> {
    /**
     * 获取用户AIC资产
     * @param uid
     * @return
     */
    UserAsset getUserAsset(Long uid);

    /**
     * 活期赎回
     *
     * @param user
     * @param form
     * @return
     */
    UserLog redeemCurrent(StakeUser user, RedeemForm form);

    User checkUser(String address);

    /**
     * 领取收益
     *
     * @param user
     * @return
     */
    boolean withdraw(StakeUser user, String tokenId, BigDecimal amount);
    
    RustService.WithdrawSign withdrawWallet(User user, String tokenId, BigDecimal amount);

    IPage<UserModel> findAllForAdminUser(Page<User> page, QueryWrapper<User> queryWrapper);
    
    IPage<StakeUserModel> findAllForAdmin(Page<StakeUser> page, QueryWrapper<StakeUser> queryWrapper);

    void updateCodeAndPid(String id, String code, String pid);

    StakeUser getStakeUser(Long userId, String tokenId);

    MyProductDataVo getMyProduct(User user, String projectId);

    void stake(User user, StakeForm form);

    void claimStatic(User user, ClaimForm form);

    void claimDynamic(User user, ClaimForm form);

    void unStake(User user, StakeForm form);

    RustService.WithdrawSign claimStaticWallet(User user, ClaimForm form);

    RustService.WithdrawSign claimWeekWallet(User user, WithdrawForm form);

    /**
     * 提取质押静态池
     */
    BigDecimal withdrawStakeStaticPool(Long userId, String tokenId, BigDecimal amount);

    /**
     * 提取质押动态池
     */
    BigDecimal withdrawStakeDynamicPool(Long userId, String tokenId, BigDecimal amount);

    void claimWeek(User user, ClaimForm form);

    void stakeFromWallet(Events events);

    void stakeFromWalletAdmin(User user, BigDecimal amount);

    void unStakeFromWallet(Events events);

    void withdrawFromWallet(Events events);

    void updateSuccess(RustService.CheckResult checkResult);

    void updateFails(RustService.CheckResult checkResult);

    void updateTeamPerf(Long userId, String tokenId, BigDecimal amount);

    List<RankVo> listTeamPerfTop(String tokenId, int size);

    @Transactional(rollbackFor = Exception.class)
    void setMaxTeamPerf(String tokenId);
    
    /**
     * 购买节点
     * @param user 用户
     * @param asset 资产
     */
    void buyNode(User user, Asset asset);
    
    /**
     * 从钱包购买节点
     * @param events 事件
     */
    void buyNodeFromWallet(Events events);
    
    /**
     * 提现节点返佣
     * @param user 用户
     * @param asset 资产
     * @param amount 金额
     */
    void withdrawNodeReward(User user, Asset asset, BigDecimal amount);

    void withdrawNodePool(User user, Asset asset, BigDecimal amount);

    RustService.WithdrawSign withdrawNodeWallet(User user, String tokenId, BigDecimal amount);

    RustService.WithdrawSign receiveNodeWallet(User user, WithdrawForm form);
    
    List<StakeModel> listStakeUsers(String tokenId);
    void setMaxNodePerf(String tokenId);

    void addNodePool(StakeUser stakeUser, BigDecimal amount);

//    void burnFromWallet(Events events);

    StakeUser sumChilds(Long id);
}
