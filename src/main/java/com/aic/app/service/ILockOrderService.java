package com.aic.app.service;

import com.aic.app.model.LockOrder;
import com.aic.app.model.Product;
import com.aic.app.model.UserProduct;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 锁仓订单服务接口
 */
public interface ILockOrderService extends IService<LockOrder> {
    
    /**
     * 根据产品类型创建锁仓订单
     */
    LockOrder createLockOrderByProduct(UserProduct userProduct, Product product, BigDecimal amount);
    
    /**
     * 处理线性释放
     */
    void processLinearRelease();
    
    /**
     * 计算静态收益
     */
    void calculateStaticRewards();
    
    /**
     * 计算动态收益
     */
    void calculateDynamicRewards();
    
    /**
     * 获取用户锁仓总额
     */
    BigDecimal getUserTotalLockAmount(Long userId, String tokenId);
    
    /**
     * 获取用户可提取金额
     */
    BigDecimal getUserAvailableAmount(Long userId, String tokenId);
    
    /**
     * 生成锁仓订单号
     */
    String generateLockOrderNo();
    
    /**
     * 记录锁仓相关日志
     */
    void recordLockOrderLog(Long userId, Integer logType, BigDecimal amount,
                           String operation, Long lockOrderId, Integer sourceType);

    /**
     * 提取锁仓释放金额
     */
    BigDecimal withdrawLockOrderRelease(Long userId, Long lockOrderId, BigDecimal amount);

    /**
     * 获取用户锁仓静态池总额
     */
    BigDecimal getUserLockStaticPool(Long userId, String tokenId);

    /**
     * 获取用户锁仓动态池总额
     */
    BigDecimal getUserLockDynamicPool(Long userId, String tokenId);

    /**
     * 提取锁仓静态池
     */
    BigDecimal withdrawLockStaticPool(Long userId, String tokenId, BigDecimal amount);

    /**
     * 提取锁仓动态池
     */
    BigDecimal withdrawLockDynamicPool(Long userId, String tokenId, BigDecimal amount);
}