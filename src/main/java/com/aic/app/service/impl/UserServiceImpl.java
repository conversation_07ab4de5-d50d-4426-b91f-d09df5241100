package com.aic.app.service.impl;

import com.aic.app.exception.Errors;
import com.aic.app.mapper.UserMapper;
import com.aic.app.model.User;
import com.aic.app.service.IUserLogService;
import com.aic.app.service.IUserService;
import com.aic.app.util.BizAssert;
import com.aic.app.util.StringUtils;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;

@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    
    @Resource
    StringRedisTemplate stringRedisTemplate;
    @Resource
    IUserLogService userLogService;
    
//    @PostConstruct
    public void init() {
        log.info("init user id");
        if (maxUserId() == 0) {
            stringRedisTemplate.opsForValue().set("fist:user:seq", "100000");
        }
    }
    
    private long maxUserId() {
        return this.baseMapper.maxUserId();
    }

    public Long genUserId() {
        // 随机生成一个6位数的数字ID，第一位不能是0，然后检查是否存在，如果存在则重新生成一个，知道不存在为止
        String userId = String.valueOf((long) (Math.random() * 900000 + 100000));
        while (this.getById(userId) != null) {
            userId = String.valueOf((long) (Math.random() * 900000 + 100000));
        }
        return Long.parseLong(userId);
    }

    private Long getUserSeq(Long seq) {
        // 定义Lua脚本
        String luaScript = "local key = KEYS[1] " +
                "local seq = tonumber(ARGV[1]) " +
                "local current_value = redis.call('GET', key) " +
                "if not current_value or tonumber(current_value) < 100000 then " +
                "    redis.call('SET', key, 100000) " +
                "end " +
                "return redis.call('INCRBY', key, seq)";

        // 创建RedisScript对象
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);

        // 执行Lua脚本
        return stringRedisTemplate.execute(redisScript, Collections.singletonList("fist:user:seq"), seq.toString());
    }
    
    @Override
    public void add(User user) {
        if (user.getId() == null) {
            user.setId(genUserId());
        }
        this.save(user);
    }

    @Override
    public User getByCode(String code) {
        return getOne(new LambdaQueryWrapper<User>().eq(User::getCode, code));
    }

    @Override
    public User findByAddress(String address) {
        return getOne(new LambdaQueryWrapper<User>().eq(User::getAddress, address));
    }

    @Override
    public boolean setLevel(Long id, int level) {
        User user = this.getById(id);
        BizAssert.isTrue(level >= 0 && level <= 9, "只能设置0-9等级");
        // 等级上限
        log.info("后台设置等级 UID = {}, level = {}", user.getId(), level);
        return update(new LambdaUpdateWrapper<User>()
                .set(user.getSetLevelTime() == null, User::getSetLevelTime, new Date())
                .set(User::isLockLevel, true)
                .set(User::getLevel, level)
                .eq(User::getId, id));
    }

    @Override
    public int sumChildren(Long id) {
        return baseMapper.sumChildren(id);
    }

    @Override
    public BigDecimal sumParentReward(Long userId) {
        return baseMapper.sumParentReward(userId);
    }

    @Override
    public void bindJuid(Long userId, String juid) {
        update(new LambdaUpdateWrapper<User>()
                .set(User::getJuid, juid)
                .eq(User::getId, userId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindRefCode(User user, String code) {
        BizAssert.isEmpty(user.getPid(), () -> Errors.INVITE_CODE_EXCEPTION2);
        BizAssert.isNotEmpty(code, () -> Errors.INVITE_CODE_EXCEPTION);
        code = code.trim();
        // 没有邀请人的，可以填邀请人
        User inviteUser = getByCode(code.trim());
        BizAssert.notNull(inviteUser, () -> Errors.INVITE_CODE_EXCEPTION);
        BizAssert.isTrue(!user.getId().equals(inviteUser.getId()), () -> Errors.INVITE_CODE_EXCEPTION);
        user.setPid(inviteUser.getId());
        String selfCode = null;
        if (StringUtils.isEmpty(user.getCode())) {
            selfCode = Utils.generateInviteCode();
            log.info("首次绑定邀请人生成邀请码 UID = {}, code = {}", user.getId(), selfCode);
        }
        log.info("绑定邀请人 UID = {}, code = {}, PID = {}", user.getId(), code, user.getPid());
        update(new LambdaUpdateWrapper<User>()
                .set(User::getPid, user.getPid())
                .set(selfCode != null, User::getCode, selfCode)
                .eq(User::getId, user.getId()));
    }
}
