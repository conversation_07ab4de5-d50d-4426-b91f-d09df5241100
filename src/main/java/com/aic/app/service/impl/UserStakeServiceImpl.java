package com.aic.app.service.impl;

import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.mapper.UserStakeMapper;
import com.aic.app.model.UserRelation;
import com.aic.app.model.UserStake;
import com.aic.app.service.IUserStakeService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
* <AUTHOR>
* @description 针对表【user_stake(用户理财产品表)】的数据库操作Service实现
* @createDate 2025-03-13 22:20:50
*/
@Service
public class UserStakeServiceImpl extends ServiceImpl<UserStakeMapper, UserStake>
    implements IUserStakeService {

    @Resource
    UserRelationMapper userRelationMapper;

    @Override
    public <E extends IPage<UserStake>> E page(E page, Wrapper<UserStake> queryWrapper) {
        return getBaseMapper().page(page, queryWrapper);
    }

    @Override
    public BigDecimal getStakeSum(Long userId, Date beginTime, Date endTime) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null) {
            return BigDecimal.ZERO;
        }
        // 开始时间如果不为空，去掉时分秒，结束时间如果不为空，去掉时分秒，纳秒清零
        if (beginTime != null) {
            beginTime = DateUtils.setHours(beginTime, 0);
            beginTime = DateUtils.setMinutes(beginTime, 0);
            beginTime = DateUtils.setSeconds(beginTime, 0);
        }
        if (endTime != null) {
            endTime = DateUtils.setHours(endTime, 23);
            endTime = DateUtils.setMinutes(endTime, 59);
            endTime = DateUtils.setSeconds(endTime, 59);
        }
        String path = Optional.ofNullable(userRelation.getPath()).orElse("/" + userId);
        return this.baseMapper.getStakeSum(path + "/%", beginTime, endTime);
    }

}




