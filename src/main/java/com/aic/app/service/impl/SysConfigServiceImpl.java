package com.aic.app.service.impl;

import com.aic.app.mapper.SysConfigMapper;
import com.aic.app.model.SysConfig;
import com.aic.app.service.ISysConfigService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SysConfigServiceImpl implements ISysConfigService {
    
    private SysConfigMapper sysConfigMapper;
    
    @Override
    public SysConfig getSysConfig() {
        return sysConfigMapper.selectById(1);
    }
    
    @Override
    public void updateSysConfig(SysConfig sysConfig) {
        sysConfigMapper.updateById(sysConfig);
    }
    
}
