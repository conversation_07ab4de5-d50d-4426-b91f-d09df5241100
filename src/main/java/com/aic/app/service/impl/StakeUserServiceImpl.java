package com.aic.app.service.impl;

import com.aic.app.exception.BizException;
import com.aic.app.exception.Errors;
import com.aic.app.form.ClaimForm;
import com.aic.app.form.RedeemForm;
import com.aic.app.form.StakeForm;
import com.aic.app.form.WithdrawForm;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserMapper;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.*;
import com.aic.app.sdk.ApiUser;
import com.aic.app.service.*;
import com.aic.app.util.BizAssert;
import com.aic.app.util.Utils;
import com.aic.app.vo.MyProductDataVo;
import com.aic.app.vo.RankVo;
import com.aic.app.vo.UserAsset;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;

@Service
@Slf4j
public class StakeUserServiceImpl extends ServiceImpl<StakeUserMapper, StakeUser> implements IStakeUserService {
    @Resource
    IProductService productService;
    @Resource
    IUserProductService userProductService;
    @Resource
    IUserLogService userLogService;
    @Resource
    IUserService userService;
    @Resource
    UserMapper userMapper;
    @Resource
    IUserStakeService userStakeService;
    @Resource
    IWithdrawService withdrawService;
    @Resource
    UserRelationMapper userRelationMapper;
    @Resource
    BizLogService bizLogService;

    @Override
    public com.aic.app.vo.UserAsset getUserAsset(Long uid) {
        com.aic.app.vo.UserAsset userAsset = new com.aic.app.vo.UserAsset();
//        List<com.aic.app.model.UserAsset> phoneAssets = userAssetService.listPhoneAsset(uid);
//        for (com.aic.app.model.UserAsset phoneAsset : phoneAssets) {
//            userAsset.setAsset(phoneAsset.getTokenId(), phoneAsset.getBalance());
//        }
        return userAsset;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserLog redeemCurrent(StakeUser user, RedeemForm form) {

        return null;
    }

//    public User initUser(String openId, String code, Long pid) {
//        User user = new User();
//        user.setType(0);
//        user.setCode(Utils.generateInviteCode());
//        user.setPid(pid);
//        user.setOpenId(openId);
//
//        if (Utils.isDev() && NumberUtils.isCreatable(openId)) {
//            // 测试环境ID，code一致，方便测试
//            user.setCode(openId);
//            user.setId(Long.parseLong(openId));
//        }
//
//        userService.add(user);
//        userAssetService.initAsset(user.getId(), this::saveBatch);
//        return user;
//    }

    public User initUser(String address) {
        User user = new User();
        user.setType(1);
        user.setCode(Utils.generateInviteCode());
        user.setAddress(address);
        user.setQuadrupleRewardTimes(5); // 设置初始4倍收益次数为5
        userService.add(user);
//        userAssetService.initAsset(user.getId(), this::saveBatch);
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User checkUser(String address) {
        User user = userService.findByAddress(address);
        if (user == null) {
            user = initUser(address);
            checkStakeUser(user.getId(), List.of(AssetEnum.XYC));
        } else {
            final Long userId = user.getId();
            checkStakeUser(userId, List.of(AssetEnum.XYC));
        }
        return user;
    }


    @Transactional(rollbackFor = Exception.class)
    public void checkStakeUser(Long userId, List<AssetEnum> list) {
        List<StakeUser> userStakes = this.list(new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, userId));
        List<StakeUser> newUserStakes = new ArrayList<>();
        for (AssetEnum asset : list) {
            // 如果不存在userStakes中，就创建一个
            StakeUser stakeUser = userStakes.stream().filter(row -> row.getTokenId().equals(asset.getTokenId())).findFirst().orElse(null);
            if (stakeUser == null) {
                stakeUser = new StakeUser();
                stakeUser.setUserId(userId);
                stakeUser.setTokenId(asset.getTokenId());
                newUserStakes.add(stakeUser);
            }
        }
        // 如果有新的，就批量保存
        if (!newUserStakes.isEmpty()) {
            this.saveBatch(newUserStakes);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean withdraw(StakeUser user, String tokenId, BigDecimal amount) {
//        BigDecimal canReceive = user.getCanReceive();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
//        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
//        com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getUserId(), tokenId);
//        log.info("领取收益 UID = {}, amount = {}", user.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("can_receive = can_receive - {0}", amount)
//                .apply("can_receive - {0} >= 0", amount)
//                .eq("id", user.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//        return userAssetService.plus(user.getUserId(), asset.getTokenId(), amount, UserLogType.RECEIVE.getValue(), UserLogType.RECEIVE.getLabel(), asset.getTokenId());
        return false;
    }

    @Override
    public RustService.WithdrawSign withdrawWallet(User user, String tokenId, BigDecimal amount) {
//        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
//        BigDecimal canReceive = stakeUser.getCanReceive();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
//        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
//        Asset asset = assetService.getByTokenId(tokenId);
//        log.info("领取收益 UID = {}, amount = {}", stakeUser.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("can_receive = can_receive - {0}", amount)
//                .apply("can_receive - {0} >= 0", amount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//        BigDecimal _amount = amount.multiply(new BigDecimal("1e18")).setScale(0, RoundingMode.HALF_UP);
//
//        RustService.WithdrawSign withdrawSignVo = rustService.getWithdrawSignVo(asset.getStakeAddress(), asset.getTokenAddress(), user.getAddress(), _amount.toPlainString(), "0");
//        withdrawService.save(new Withdraw(0, user, asset, null, amount, BigDecimal.ZERO, withdrawSignVo));
//        userLogService.save(new UserLog(user.getId(), UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount.negate(), tokenId, tokenId, UserLogType.RECEIVE.getLabel()));
//
//        return withdrawSignVo;
        return null;
    }

    @Override
    public IPage<UserModel> findAllForAdminUser(Page<User> page, QueryWrapper<User> queryWrapper) {
        return baseMapper.findAllForAdminUser(page, queryWrapper);
    }

    @Override
    public IPage<StakeUserModel> findAllForAdmin(Page<StakeUser> page, QueryWrapper<StakeUser> queryWrapper) {
        return baseMapper.findAllForAdmin(page, queryWrapper);
    }

    @Override
    public void updateCodeAndPid(String id, String code, String pid) {
//        log.info("[user] sync user id = {}, code = {}, pid = {}", id, code, pid);
//        this.update(new LambdaUpdateWrapper<StakeUser>()
//                .set(code != null, StakeUser::getCode, code)
//                .set(pid != null, StakeUser::getPid, pid)
//                .eq(StakeUser::getId, id));
    }

    @Override
    public StakeUser getStakeUser(Long userId, String tokenId) {
        return this.getOne(new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, userId)
                .eq(StakeUser::getTokenId, tokenId));
    }

    @Override
    public MyProductDataVo getMyProduct(User user, String tokenId) {
//        StakeUser stakeUser = null;
//        if (user != null) {
//            stakeUser = getStakeUser(user.getId(), tokenId);
//        }
//        Asset asset = assetService.getByTokenId(tokenId);
//        MyProductDataVo data = new MyProductDataVo(user, stakeUser, asset);
//        if (user != null) {
//            if (ObjectUtils.isNotEmpty(user.getPid())
//                    && !Long.valueOf(0).equals(user.getPid())) {
//
//                User pUser = userMapper.selectById(user.getPid());
//                if (pUser != null) {
//                    data.setRefCode(pUser.getCode());
//                }
//            }
//            if (!user.isWalletUser()) {
//                com.aic.app.model.UserAsset userAsset = userAssetService.getAsset(user.getId(), tokenId);
//                com.aic.app.model.UserAsset bnbAsset = userAssetService.getAsset(user.getId(), AssetEnum.BNB.getTokenId());
//                com.aic.app.model.UserAsset juAsset = userAssetService.getAsset(user.getId(), AssetEnum.JU.getTokenId());
//                data.setBalance(userAsset.getBalance());
//                data.setBnbBalance(bnbAsset.getBalance());
//                data.setJuBalance(juAsset.getBalance());
//            }
//            long invitation = userRelationMapper.countChild(user.getId());
//            long teamCount = userRelationMapper.countTeam(user.getId());
//            data.setInvitation(invitation);
//            data.setTeamCount(teamCount);
//        }
//
//        LocalDate yesterday = LocalDate.of(2025, 05, 10);
//        // 2% 解除质押手续费，TOKEN单位
//        BigDecimal unStakeFee = userLogService.getFeeAmount(yesterday);
//        // 直接销毁的数量
//        BigDecimal burnAmount = userLogService.getBurnAmount(yesterday);
//
//        data.setUnStakeFee(unStakeFee);
//        data.setBurnAmount(burnAmount);
//        return data;
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stake(User user, StakeForm form) {
//        BigDecimal amount = form.getAmount();
//        String token = form.getTokenId();
//
//        BizAssert.notNull(user.getPid(), () -> Errors.NO_INVITE_EXCEPTION);
//
//        log.info("质押 uid = {}, token = {}, amount = {}", user.getId(), token, amount);
//        com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getId(), token);
//        BizAssert.notNull(asset, () -> Errors.BALANCE_EXCEPTION);
//        BizAssert.isTrue(asset.checkBalance(amount), () -> Errors.BALANCE_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//
//        bizLogService.recordLog(user.getId(), null, "质押", form, "质押【" + token + "】");
//
//        // 扣款
//        userAssetService.pay(user.getId(), asset.getTokenId(), asset.getTokenId(), amount, UserLogType.Stake.getValue(), UserLogType.Stake.getLabel() + "【" + token + "】");
//
//        int updateRow = baseMapper.updateUserPendingAmount(stakeUser.getId(), amount);
//        BizAssert.isTrue(updateRow == 1, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
//
//        if (!Boolean.TRUE.equals(stakeUser.getStakeFirst())) {
//            // 首次质押增加4倍额度
//            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
//            updateRow = baseMapper.updateUserLimitAmount(stakeUser.getId(), limit);
//            log.info("[stake] 首次质押，增加4倍额度，UID = {}, 额度 = {}", stakeUser.getId(), limit);
//            BizAssert.isTrue(updateRow == 1, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
//        }
//
//        UserStake userStake = new UserStake(0, user.getId(), asset.getTokenId(), amount);
//        userStakeService.save(userStake);
//
//        // this.updateTeamPerf(user.getId(), asset.getTokenId(), amount);
//
//        assetService.updateTotal(asset.getTokenId(), amount, null, null, null, null, null, null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stakeFromWallet(Events events) {
        Events.Data data = events.getData();

        // 验证必要字段
        if (data.getAddress() == null || data.getStakeAmount() == null) {
            log.error("[stakeFromWallet] 事件数据不完整，address = {}, stakeAmount = {}",
                    data.getAddress(), data.getStakeAmount());
            return;
        }

        // 根据地址查找或创建用户
        User user = checkUser(data.getAddress());
        if (user == null) {
            log.error("[stakeFromWallet] 用户创建失败，address = {}", data.getAddress());
            return;
        }

        String tokenId = AssetEnum.XYC.getTokenId();
        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);

        try {
            BigDecimal amount = new BigDecimal(data.getStakeAmount());

            // 记录业务日志
            bizLogService.recordLog(user.getId(), null, "质押", data,
                    "质押【" + tokenId + "】金额: " + amount + ", 交易号: " + events.getTxid());

            log.info("[stakeFromWallet] 处理质押事件，userId = {}, tokenId = {}, amount = {}, txid = {}",
                    user.getId(), tokenId, amount, events.getTxid());

            // 更新用户待确认质押金额
            int updateRow = baseMapper.updateUserPendingAmount(stakeUser.getId(), amount);
            if (updateRow != 1) {
                log.error("[stakeFromWallet] 更新待确认质押金额失败，stakeUserId = {}, amount = {}", stakeUser.getId(), amount);
                BizAssert.isTrue(false, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
            }

            // 记录用户日志
            userLogService.addLog(user.getId(), UserLogType.Stake.getValue(), amount,
                    UserLogType.Stake.getLabel() + "【" + tokenId + "】", tokenId, tokenId);

            // 创建质押记录
            UserStake userStake = new UserStake(0, user.getId(), tokenId, amount);
            // data.getTokenType() 0：ju，1：usdt，2：xyc
            if (data.getTokenType() == 0) {
                userStake.setPayToken(AssetEnum.JU.getTokenId());
            } else if (data.getTokenType() == 1) {
                userStake.setPayToken(AssetEnum.USDT.getTokenId());
            } else if (data.getTokenType() == 2) {
                userStake.setPayToken(AssetEnum.XYC.getTokenId());
            }
            userStake.setOriAmount(new BigDecimal(data.getOriAmount()));
            userStake.setStatus(0); // 待确认状态，等待T+1结算
            userStake.setTxid(events.getTxid());
            userStakeService.save(userStake);

            log.info("[stakeFromWallet] 质押事件处理成功，userId = {}, amount = {}, txid = {}",
                    user.getId(), amount, events.getTxid());

        } catch (NumberFormatException e) {
            log.error("[stakeFromWallet] 质押金额格式错误，stakeAmount = {}", data.getStakeAmount(), e);
        } catch (Exception e) {
            log.error("[stakeFromWallet] 质押事件处理失败，userId = {}, txid = {}",
                    user.getId(), events.getTxid(), e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stakeFromWalletAdmin(User user, BigDecimal amount) {
//        Asset asset = assetService.getByTokenId(AssetEnum.FTST.getTokenId());
//        String tokenId = asset.getTokenId();
//        StakeUser stakeUser = getStakeUser(user.getId(), asset.getTokenId());
//        if (stakeUser == null) {
//            log.error("找不到质押用户 {}", user.getId());
//            return;
//        }
//        bizLogService.recordLog(user.getId(), null, "质押", amount, "质押【" + tokenId + "】后台");
//
//        int updateRow = baseMapper.updateUserPendingAmount(stakeUser.getId(), amount);
//        BizAssert.isTrue(updateRow == 1, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
//
//        userLogService.save(new UserLog(user.getId(), UserLogType.Stake.getValue(), BigDecimal.ZERO, amount.negate(), tokenId, tokenId, UserLogType.Stake.getLabel() + "【" + tokenId + "】后台"));
//
//        if (!Boolean.TRUE.equals(stakeUser.getStakeFirst())) {
//            // 首次质押增加4倍额度
//            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
//            updateRow = baseMapper.updateUserLimitAmount(stakeUser.getId(), limit);
//            log.info("[stake] 首次质押，增加4倍额度，UID = {}, 额度 = {}", stakeUser.getId(), limit);
//            BizAssert.isTrue(updateRow == 1, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
//        }
//
//        UserStake userStake = new UserStake(0, user.getId(), asset.getTokenId(), amount);
//        userStake.setStatus(1);
//        userStake.setTxid("后台质押");
//        userStakeService.save(userStake);
//
//        assetService.updateTotal(asset.getTokenId(), amount, null, null, null, null, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unStakeFromWallet(Events events) {
//        Events.Data data = events.getData();
//        Asset asset = assetService.getAssetByStakeAddress(events.getContract());
//        if (asset == null) {
//            log.error("找不到资产 {}", events.getContract());
//            return;
//        }
//        User user = userService.findByAddress(data.getAddress());
//        if (user == null) {
//            log.error("找不到用户 {}", data.getAddress());
//            return;
//        }
//        StakeUser stakeUser = getStakeUser(user.getId(), asset.getTokenId());
//        if (stakeUser == null) {
//            log.error("找不到质押用户 {}", user.getId());
//            return;
//        }
//        String tokenId = asset.getTokenId();
//        BigDecimal amount = new BigDecimal(data.getStakeAmount());
//
//        BigDecimal totalStake = stakeUser.getPendingAmount().add(stakeUser.getCurrentAmount());
//
//        BizAssert.isTrue(totalStake.compareTo(amount) >= 0, () -> Errors.AMOUNT_EXCEPTION);
//        BigDecimal currentAmount = BigDecimal.ZERO;
//        BigDecimal pendingAmount = BigDecimal.ZERO;
//
//        if (stakeUser.getPendingAmount().compareTo(BigDecimal.ZERO) > 0) {
//            if (stakeUser.getPendingAmount().compareTo(amount) >= 0) {
//                pendingAmount = amount;
//            } else {
//                pendingAmount = stakeUser.getPendingAmount();
//                currentAmount = amount.subtract(pendingAmount);
//            }
//        } else {
//            currentAmount = amount;
//        }
//
//        bizLogService.recordLog(user.getId(), null, "赎回质押", data, "赎回质押【" + tokenId + "】");
//
//        log.info("[stake] 赎回质押 资产 = {}, UID = {}, amount = {}, 扣减待确认 = {}, 扣减已确认 = {}", tokenId, user.getId(), amount, pendingAmount, currentAmount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("current_amount = current_amount - {0}, pending_amount = pending_amount - {1}", currentAmount, pendingAmount)
//                .apply("current_amount - {0} >= 0 and pending_amount >= pending_amount - {1}", currentAmount, pendingAmount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//
//        userLogService.save(new UserLog(user.getId(), UserLogType.RedeemCurrent.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, UserLogType.RedeemCurrent.getLabel() + "【" + tokenId + "】"));
//
//        if (stakeUser.getStakeLimit().compareTo(BigDecimal.ZERO) > 0) {
//            // 解除质押扣除4倍额度
//            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
//            if (limit.compareTo(stakeUser.getStakeLimit()) > 0) {
//                limit = stakeUser.getStakeLimit();
//            }
//            int updateRow = baseMapper.updateUserLimitAmount2(stakeUser.getId(), limit);
//            BizAssert.isTrue(updateRow == 1, () -> Errors.SERVER_EXCEPTION);
//            log.info("[stake] 解除质押扣除4倍额度，UID = {}, 额度 = {}", stakeUser.getId(), limit);
//        }
//
//        UserStake userStake = new UserStake(1, user.getId(), asset.getTokenId(), amount);
//        userStake.setStatus(1);
//        userStake.setTxid(events.getTxid());
//        userStakeService.save(userStake);
//
//        // this.updateTeamPerf(user.getId(), asset.getTokenId(), amount.negate());
//
//        assetService.updateTotal(asset.getTokenId(), amount.negate(), null, null, null, null, null, null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawFromWallet(Events events) {
        // type: 0 提币、 1 释放、 2 提周分红
//        Events.Data data = events.getData();
//        Withdraw withdraw = withdrawService.findByAddress(data.getAddress(), Integer.valueOf(Optional.ofNullable(data.getType()).orElse("0")), data.getNonce());
//        if (withdraw == null) {
//            log.info("[stake] 提币提现记录不存在 address = {}, amount = {}", data.getAddress(), data.getAmount());
//            return;
//        }
//        if (withdraw.getState().equals(2) || withdraw.getState().equals(3)) {
//            log.info("[stake] 提币提现记录已处理 address = {}, amount = {}", data.getAddress(), data.getAmount());
//            return;
//        }
//        log.info("[event] 处理事件 = {}", events);
//        // uint256 _status // 说明 1-审核中 2-成功 3-拒绝回退
//        int status;
//        if (Integer.valueOf(1).equals(data.getStatus())) {
//            status = 1;
//        } else if (Integer.valueOf(2).equals(data.getStatus())) {
//            status = 2;
//        }  else if (Integer.valueOf(3).equals(data.getStatus())) {
//            status = 3;
//        } else {
//            throw new BizException(-1, "未知状态");
//        }
//
//        if (status == 3) {
//            // 失败
//            updateFails(new RustService.CheckResult(withdraw.getId()));
//        } else {
//            // 其他状态
//            if ("0".equals(data.getType())) {
//                log.info("[stake] 提币 address = {}, amount = {}", data.getAddress(), data.getAmount());
//            } else if ("1".equals(data.getType())) {
//                log.info("[stake] 释放 address = {}, amount = {}", data.getAddress(), data.getAmount());
//                claimStaticFromWallet(events, withdraw);
//            } else if ("2".equals(data.getType())) {
//                log.info("[stake] 提周分红 address = {}, amount = {}", data.getAddress(), data.getAmount());
//            } else if ("3".equals(data.getType())) {
//                log.info("[stake] 提节点奖励 address = {}, amount = {}", data.getAddress(), data.getAmount());
//            } else if ("4".equals(data.getType())) {
//                log.info("[stake] 提节点分红 address = {}, amount = {}", data.getAddress(), data.getAmount());
//            }
//        }
//
//
//        withdrawService.update(new LambdaUpdateWrapper<Withdraw>()
//                .set(Withdraw::getState, status)
//                .set(Withdraw::getTxid, events.getTxid())
//                .eq(BaseEntity::getId, withdraw.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unStake(User user, StakeForm form) {
//        BigDecimal amount = form.getAmount();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.AMOUNT_EXCEPTION);
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//        com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getId(), form.getTokenId());
//
//        BigDecimal totalStake = stakeUser.getPendingAmount().add(stakeUser.getCurrentAmount());
//        BizAssert.isTrue(totalStake.compareTo(amount) >= 0, () -> Errors.AMOUNT_EXCEPTION);
//        BigDecimal currentAmount = BigDecimal.ZERO;
//        BigDecimal pendingAmount = BigDecimal.ZERO;
//
//        if (stakeUser.getPendingAmount().compareTo(BigDecimal.ZERO) > 0) {
//            if (stakeUser.getPendingAmount().compareTo(amount) >= 0) {
//                pendingAmount = amount;
//            } else {
//                pendingAmount = stakeUser.getPendingAmount();
//                currentAmount = amount.subtract(pendingAmount);
//            }
//        } else {
//            currentAmount = amount;
//        }
//
//        bizLogService.recordLog(user.getId(), null, "赎回质押", form, "赎回质押【" + form.getTokenId() + "】");
//
//        log.info("[stake] 赎回质押 资产 = {}, UID = {}, amount = {}, 扣减待确认 = {}, 扣减已确认 = {}", form.getTokenId(), user.getId(), amount, pendingAmount, currentAmount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("current_amount = current_amount - {0}, pending_amount = pending_amount - {1}", currentAmount, pendingAmount)
//                .apply("current_amount - {0} >= 0 and pending_amount >= pending_amount - {1}", currentAmount, pendingAmount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//
//        // 解除质押 扣除2%手续费
//        BigDecimal realAmount = amount.multiply(new BigDecimal("0.98"));
//
//        userAssetService.plus(user.getId(), asset.getTokenId(), realAmount, UserLogType.RedeemCurrent.getValue(), UserLogType.RedeemCurrent.getLabel(), asset.getTokenId());
//
//        if (stakeUser.getStakeLimit().compareTo(BigDecimal.ZERO) > 0) {
//            // 解除质押扣除4倍额度
//            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
//            if (limit.compareTo(stakeUser.getStakeLimit()) > 0) {
//                limit = stakeUser.getStakeLimit();
//            }
//            int updateRow = baseMapper.updateUserLimitAmount2(stakeUser.getId(), limit);
//            BizAssert.isTrue(updateRow == 1, () -> Errors.SERVER_EXCEPTION);
//            log.info("[stake] 解除质押扣除4倍额度，UID = {}, 额度 = {}", stakeUser.getId(), limit);
//        }
//
//        UserStake userStake = new UserStake(1, user.getId(), asset.getTokenId(), amount);
//        userStake.setStatus(1);
//        userStakeService.save(userStake);
//
//        // this.updateTeamPerf(user.getId(), asset.getTokenId(), amount.negate());
//
//        assetService.updateTotal(asset.getTokenId(), amount.negate(), null, null, null, null, null, null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RustService.WithdrawSign claimStaticWallet(User user, ClaimForm form) {
//        Product product = productService.getById(form.getProductId());
//        BizAssert.notNull(product, "产品不存在");
//
//        BigDecimal amount = form.getAmount();
//        if (!(amount.compareTo(BigDecimal.ZERO) > 0)) {
//            log.info("[stake] 提取数量不正确 = {}", form);
//        }
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//
//        BizAssert.isTrue(stakeUser.getStaticPool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount);
//        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        BigDecimal tokenPrice = productService.getPrice(form.getTokenId());
//        BigDecimal bnbPrice = productService.getPrice(AssetEnum.BNB.getTokenId());
//
//        if (!(tokenPrice.compareTo(BigDecimal.ZERO) > 0)) {
//            log.error("[stake] 获取不到token价格");
//        }
//        BizAssert.isTrue(tokenPrice.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//        if (!(bnbPrice.compareTo(BigDecimal.ZERO) > 0)) {
//            log.error("[stake] 获取不到bnb价格");
//        }
//        BizAssert.isTrue(bnbPrice.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        // 手续费 30% * Token价格 / bnb价格
//        BigDecimal fee = amount.multiply(product.getFee()).multiply(tokenPrice).divide(bnbPrice, 8, RoundingMode.HALF_UP);
//
//        if (product.getFee().compareTo(BigDecimal.ZERO) > 0) {
//            if (!(fee.compareTo(BigDecimal.ZERO) > 0)) {
//                log.info("[stake] 手续费不能为0 = {} - {}", product, fee);
//            }
//            BizAssert.isTrue(fee.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//        }
//
//        Asset asset = assetService.getByTokenId(form.getTokenId());
//
//        BigDecimal feeWei = fee.multiply(new BigDecimal("1e18")).setScale(0, RoundingMode.HALF_UP);
//
//        log.info("领取静态池 UID = {}, amount = {}, 手续费 = {}", user.getId(), amount, fee);
//        RustService.WithdrawSign withdrawSignVo = rustService.getWithdrawSignVo(asset.getStakeAddress(), asset.getTokenAddress(), user.getAddress(), feeWei.toPlainString(), "1");
//
//        withdrawService.save(new Withdraw(1, user, asset, product.getId(), amount, fee, withdrawSignVo));
//
//        return withdrawSignVo;
        return null;

    }

    @Override
    public RustService.WithdrawSign claimWeekWallet(User user, WithdrawForm form) {
//        BigDecimal amount = form.getAmount();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//
//        BizAssert.isTrue(stakeUser.getWeekDynamic().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        Asset asset = assetService.getByTokenId(form.getTokenId());
//
//        log.info("领取周分红 UID = {}, amount = {}", user.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("week_dynamic = week_dynamic - {0}", amount)
//                .apply("week_dynamic - {0} >= 0", amount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//
//        BigDecimal amountWei = amount.multiply(new BigDecimal("1e18")).setScale(0, RoundingMode.HALF_UP);
//
//        RustService.WithdrawSign withdrawSignVo = rustService.getWithdrawSignVo(asset.getStakeAddress(), asset.getTokenAddress(), user.getAddress(), amountWei.toPlainString(), "2");
//
//        withdrawService.save(new Withdraw(2, user, asset, null, amount, BigDecimal.ZERO, withdrawSignVo));
//
//        userLogService.save(new UserLog(user.getId(), UserLogType.WithdrawWeek.getValue(), BigDecimal.ZERO, amount.negate(), form.getTokenId(), form.getTokenId(), UserLogType.WithdrawWeek.getLabel()));
//
//        return withdrawSignVo;
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimWeek(User user, ClaimForm form) {
//        BigDecimal amount = form.getAmount();
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//        BigDecimal canReceive = stakeUser.getWeekDynamic();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
//        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
//        com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getId(), form.getTokenId());
//        log.info("领取周分红 UID = {}, amount = {}", user.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("week_dynamic = week_dynamic - {0}", amount)
//                .apply("week_dynamic - {0} >= 0", amount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//        userAssetService.plus(user.getId(), asset.getTokenId(), amount, UserLogType.WithdrawWeek.getValue(), UserLogType.WithdrawWeek.getLabel(), asset.getTokenId());
    }

    private void claimStaticFromWallet(Events events, Withdraw withdraw) {
        // Events.Data data = events.getData();
        // User user = userService.findByAddress(data.getAddress());
        // BigDecimal amount = withdraw.getQuantity();
        // Product product = productService.getById(withdraw.getProductId());
        // BigDecimal fee = withdraw.getFee();
        // String tokenId = withdraw.getTokenId();

        // BigDecimal bnbPrice = productService.getPrice(AssetEnum.BNB.getTokenId());
        // BigDecimal tokenPrice = productService.getPrice(tokenId);
        // BigDecimal feeToken = fee.multiply(bnbPrice).divide(tokenPrice, 8, RoundingMode.HALF_UP);

        // UserProduct userProduct = new UserProduct(user.getId(), Utils.genOrderNo(), product, amount);
        // userProduct.setAmount(amount);
        // userProduct.setFee(fee);
        // userProduct.setType(0);
        // userProduct.setSource(user.getType());
        // userProduct.setTokenId(tokenId);
        // userProduct.setPrice1(bnbPrice);
        // userProduct.setPrice2(tokenPrice);
        // userProduct.setFeeToken(feeToken);
        // userProduct.setFeeTokenId(AssetEnum.BNB.getTokenId());
        // userProductService.save(userProduct);

        // claimStaticFee(userProduct);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimStatic(User user, ClaimForm form) {
//        Product product = productService.getById(form.getProductId());
//        BizAssert.notNull(product, "产品不存在");
//
//        BigDecimal amount = form.getAmount();
//
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        String tokenId = form.getTokenId();
//        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
//
//        BizAssert.isTrue(stakeUser.getStaticPool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        BigDecimal juPrice = productService.getPrice(AssetEnum.JU.getTokenId());
//        BigDecimal bnbPrice = productService.getPrice(AssetEnum.BNB.getTokenId());
//        BigDecimal tokenPrice = productService.getPrice(tokenId);
//        // 手续费 30%
//        BigDecimal feeToken = amount.multiply(product.getFee()).setScale(8, RoundingMode.HALF_UP);
//        // 扣除的 ju = token 数量 * token价格 / ju价格
//        BigDecimal fee = feeToken.multiply(tokenPrice).divide(juPrice, 8, RoundingMode.HALF_UP);
//
//        // 等价的BNB = token 数量 * token价格 / bnb价格 
//        BigDecimal bnbFee = feeToken.multiply(tokenPrice).divide(bnbPrice, 8, RoundingMode.HALF_UP);
//
//        final BigDecimal power = amount;
//
//        if (fee.compareTo(BigDecimal.ZERO) > 0) {
//            com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getId(), AssetEnum.JU.getTokenId());
//            BizAssert.isTrue(asset.getBalance().compareTo(fee) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION2);
//            String remark = String.format("提取静态收益，产品：%s，数量：%s，手续费：%s, JU单价：%s, %s单价: %s，bnbPrice：%s", product.getName(), amount, fee, juPrice, tokenId, tokenPrice, bnbPrice);
//            userAssetService.pay(user.getId(), tokenId, asset.getTokenId(), fee, UserLogType.WithdrawFee.getValue(), remark);
//        }
//
//        log.info("[stake] 提取静态 token = {} => {}, amount = {}, fee = {}, power = {}", tokenId, product.getName(), amount, fee, power);
//
//        int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount);
//        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        UserProduct userProduct = new UserProduct(user.getId(), Utils.genOrderNo(), product, power);
//        userProduct.setAmount(amount);
//        userProduct.setFee(fee);
//        userProduct.setType(0);
//        userProduct.setSource(user.getType());
//        userProduct.setTokenId(tokenId);
//        userProduct.setPrice1(juPrice);
//        userProduct.setPrice2(tokenPrice);
//        userProduct.setFeeToken(feeToken);
//        userProduct.setFeeTokenId(AssetEnum.JU.getTokenId());
//        userProduct.setBnbFee(bnbFee);
//        userProductService.save(userProduct);
//
//        claimStaticFee(userProduct);
    }

    public void claimStaticFee(UserProduct userProduct) {
//        BigDecimal fee = userProduct.getFeeToken();
//        if (fee.compareTo(BigDecimal.ZERO) <= 0) {
//            log.info("[stake] 没有静态手续费，无需销毁 = {}", userProduct);
//            return;
//        }
//        // 回购资金池 70%
//        BigDecimal supply = fee.multiply(new BigDecimal("0.7")).setScale(8, RoundingMode.HALF_UP);
//        // 社区奖励 10%
//        BigDecimal totalWeekPool = fee.multiply(new BigDecimal("0.1")).setScale(8, RoundingMode.HALF_UP);
//        // 社区共建者 20%
//        BigDecimal nodePool = fee.multiply(new BigDecimal("0.2")).setScale(8, RoundingMode.HALF_UP);
//        // totalSupplyBnb
//        BigDecimal totalSupplyBnb;
//        if (AssetEnum.BNB.getTokenId().equals(userProduct.getFeeTokenId())) {
//            totalSupplyBnb = userProduct.getFee();
//        } else {
//            totalSupplyBnb = userProduct.getBnbFee();
//        }
//        // 打印log
//        log.info("[stake] 静态手续费 = {}, supply = {}, totalWeekPool = {}, nodePool = {}", fee, supply, totalWeekPool, nodePool);
//        assetService.updateTotal(userProduct.getTokenId(), null, supply, null, null, totalWeekPool, nodePool, totalSupplyBnb);
    }

    /**
     * 动态手续费
     * @param userProduct
     */
    private void claimDynamicFee(UserProduct userProduct) {
//        BigDecimal fee = userProduct.getFee();
//        if (fee.compareTo(BigDecimal.ZERO) <= 0) {
//            log.info("[stake] 没有动态手续费，无需销毁 = {}", userProduct);
//            return;
//        }
//        // 质押DAO池 50%
//        BigDecimal dao = fee.multiply(new BigDecimal("0.5"));
//        // 销毁 10%
//        BigDecimal burn = fee.multiply(new BigDecimal("0.2"));
//        // 社区奖励10%
//        BigDecimal community = fee.multiply(new BigDecimal("0.1"));
//        // 社区共建者 20%
//        BigDecimal nodePool = fee.multiply(new BigDecimal("0.2"));
//        log.info("[stake] 动态手续费 = {}, dao = {}, burn = {}, community = {}, nodePool = {}", fee, dao, burn, community, nodePool);
//
//        assetService.updateTotal(userProduct.getTokenId(), null, null, burn, dao, community, nodePool, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void claimDynamic(User user, ClaimForm form) {
//        Product product = productService.getById(form.getProductId());
//        BizAssert.notNull(product, "产品不存在");
//
//        BigDecimal amount = form.getAmount();
//
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//
//        BizAssert.isTrue(stakeUser.getDynamicPool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        BigDecimal fee = amount.multiply(product.getFee()).setScale(8, RoundingMode.HALF_UP);
//        BigDecimal power = amount.subtract(fee);
//
//        log.info("[stake] 提取动态 token = {} => {}, amount = {}, fee = {}, power = {}", form.getTokenId(), product.getName(), amount, fee, power);
//
//        int updateRow = baseMapper.updateDynamicPool(stakeUser.getId(), amount);
//        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        String remark = String.format("提取动态收益，产品：%s，数量：%s，手续费：%s, 实到：%s", product.getName(), amount, fee, power);
//        userLogService.save(new UserLog(user.getId(), UserLogType.WithdrawFee.getValue(), amount, fee.negate(), form.getTokenId(), form.getTokenId(), remark));
//
//        UserProduct userProduct = new UserProduct(user.getId(), Utils.genOrderNo(), product, power);
//        userProduct.setAmount(amount);
//        userProduct.setFee(fee);
//        userProduct.setType(1);
//        userProduct.setSource(user.getType());
//        userProduct.setTokenId(form.getTokenId());
//        userProduct.setFeeTokenId(form.getTokenId());
//        userProductService.save(userProduct);
//
//        claimDynamicFee(userProduct);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSuccess(RustService.CheckResult checkResult) {
        Withdraw withdraw = withdrawService.getById(checkResult.getId());
        if (!withdraw.getState().equals(0)) {
            log.warn("[withdraw] 提现状态更被更新 id = {}, state = {}", withdraw.getId(), withdraw.getState());
            return;
        }
        boolean success = withdrawService.update(new LambdaUpdateWrapper<Withdraw>()
                .set(Withdraw::getState, 1)
                .set(Withdraw::getTxid, checkResult.getTxid())
                .eq(Withdraw::getId, withdraw.getId())
                .eq(Withdraw::getState, 0));
        BizAssert.isTrue(success, () -> Errors.SERVER_EXCEPTION);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFails(RustService.CheckResult checkResult) {
        Withdraw withdraw = withdrawService.getById(checkResult.getId());
        if (!withdraw.getState().equals(0) && !withdraw.getState().equals(1)) {
            log.warn("[withdraw] 提现状态更被更新 id = {}, state = {}", withdraw.getId(), withdraw.getState());
            return;
        }
        boolean success = withdrawService.update(new LambdaUpdateWrapper<Withdraw>()
                .set(Withdraw::getState, 4)
                .eq(Withdraw::getId, withdraw.getId())
                .eq(Withdraw::getState, withdraw.getState()));
        BizAssert.isTrue(success, () -> Errors.SERVER_EXCEPTION);

        Long userId = withdraw.getUserId();
        String tokenId = withdraw.getTokenId();
        BigDecimal amount = withdraw.getQuantity();

        StakeUser stakeUser = this.getStakeUser(userId, tokenId);

        if (withdraw.getType().equals(0)) {
            // 返回提币金额
            log.info("[withdraw] 超时 返还提现金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            boolean result = this.update(new UpdateWrapper<StakeUser>()
                    .setSql("can_receive = can_receive + {0}", amount)
                    .apply("can_receive + {0} >= 0", amount)
                    .eq("id", stakeUser.getId()));
            BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
            userLogService.save(new UserLog(userId, UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, UserLogType.RECEIVE.getLabel() + "【资金退回】"));
        } else if (withdraw.getType().equals(1)) {
            // 返回提取静态金额
            log.info("[withdraw] 超时 返回提取静态金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount.negate());
            BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
        } else if (withdraw.getType().equals(2)) {
            log.info("[withdraw] 超时 返回提取周分红金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            boolean result = this.update(new UpdateWrapper<StakeUser>()
                    .setSql("week_dynamic = week_dynamic + {0}", amount)
                    .apply("week_dynamic + {0} >= 0", amount)
                    .eq("id", stakeUser.getId()));
            BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
            userLogService.save(new UserLog(userId, UserLogType.WithdrawWeek.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, UserLogType.WithdrawWeek.getLabel() + "【资金退回】"));
        } else if (withdraw.getType().equals(3)) {
            log.info("[withdraw] 超时 返回提取节点返佣 UID = {}, amount = {}", stakeUser.getId(), amount);
            withdrawNodeRewardFromWalletRollback(withdraw);
        } else if (withdraw.getType().equals(4)) {
            log.info("[withdraw] 超时 返回提取节点分红 UID = {}, amount = {}", stakeUser.getId(), amount);
            withdrawNodePoolFromWalletRollback(withdraw);
        } else if (withdraw.getType().equals(5)) {
            log.info("[withdraw] 超时 返回普通商品提取金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            withdrawNormalProductFromWalletRollback(withdraw);
        }
    }

    private void withdrawNodeRewardFromWalletRollback(Withdraw withdraw) {
        // 驳回
        StakeUser stakeUser = getStakeUser(withdraw.getUserId(), withdraw.getTokenId());
        BigDecimal amount = withdraw.getQuantity();
        boolean result = update(new UpdateWrapper<StakeUser>()
                .setSql("node_reward = node_reward + {0}", amount)
                .apply("node_reward + {0} >= 0", amount)
                .eq("id", stakeUser.getId()));
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
        userLogService.addLog(withdraw.getUserId(), UserLogType.WithdrawNodeReward.getValue(), amount, UserLogType.WithdrawNodeReward.getLabel() + "【资金退回】", AssetEnum.BNB.getTokenId(), withdraw.getTokenId());
    }

    private void withdrawNodePoolFromWalletRollback(Withdraw withdraw) {
        // 驳回
        StakeUser stakeUser = getStakeUser(withdraw.getUserId(), withdraw.getTokenId());
        BigDecimal amount = withdraw.getQuantity();
        boolean result = update(new UpdateWrapper<StakeUser>()
                .setSql("node_pool = node_pool + {0}", amount)
                .apply("node_pool + {0} >= 0", amount)
                .eq("id", stakeUser.getId()));
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
        userLogService.addLog(withdraw.getUserId(), UserLogType.WithdrawNodePool.getValue(), amount, UserLogType.WithdrawNodePool.getLabel() + "【资金退回】", withdraw.getTokenId(), withdraw.getTokenId());
    }

    /**
     * 普通商品提取回退
     */
    private void withdrawNormalProductFromWalletRollback(Withdraw withdraw) {
        // 回退普通商品可提取金额
        BigDecimal amount = withdraw.getQuantity();
        Integer productId = withdraw.getProductId();

        if (productId == null) {
            log.error("[withdraw] 普通商品提取回退失败，productId为空，withdrawId = {}", withdraw.getId());
            return;
        }

        boolean result = userProductService.update(
            new LambdaUpdateWrapper<UserProduct>()
                .eq(UserProduct::getId, productId)
                .setSql("available_amount = available_amount + {0}", amount)
        );

        if (result) {
            log.info("[withdraw] 普通商品提取回退成功，订单ID = {}, 回退金额 = {}", productId, amount);
            userLogService.addLog(withdraw.getUserId(), UserLogType.RECEIVE.getValue(), amount,
                "普通商品提取超时回退-订单ID:" + productId, withdraw.getTokenId(), withdraw.getTokenId());
        } else {
            log.error("[withdraw] 普通商品提取回退失败，订单ID = {}, 回退金额 = {}", productId, amount);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTeamPerf(Long userId, String tokenId, BigDecimal amount) {
        // Set<Long> userIds = new HashSet<>();
        // userIds.add(userId);

        // UserRelation userRelation = userRelationMapper.selectById(userId);
        // if (userRelation != null && StringUtils.isNotEmpty(userRelation.getPath())) {
        //     String[] ids = userRelation.getPath().substring(1).split("/");
        //     for (String id : ids) {
        //         userIds.add(Long.parseLong(id));
        //     }
        // }
        // log.info("[stake] 更新团队业绩 userId = {}, tokenId = {}, amount = {}, ids = {}", userId, tokenId, amount, userIds);
        // this.update(new UpdateWrapper<StakeUser>()
        //         .setSql("team_perf = team_perf + {0}", amount)
        //         .eq("token_id", tokenId)
        //         .in("user_id", userIds));
    }

    @Override
    public List<RankVo> listTeamPerfTop(String tokenId, int size) {
        return this.baseMapper.listTeamPerfTop(tokenId, size);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setMaxTeamPerf(String tokenId) {
        this.update(new UpdateWrapper<StakeUser>()
                .setSql("max_team_perf = GREATEST(team_perf, max_team_perf)")
                .eq("token_id", tokenId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buyNode(User user, Asset asset) {
//        String tokenId = asset.getTokenId();
//        Long userId = user.getId();
//        String payToken = AssetEnum.BNB.getTokenId();
//        com.aic.app.model.UserAsset userAsset = userAssetService.getAsset(userId, payToken);
//        BizAssert.isTrue(userAsset.checkBalance(asset.getNodePrice()), () -> Errors.BALANCE_EXCEPTION);
//        log.info("购买节点 UID = {}, PID = {}, tokenId = {}", userId, user.getPid(), tokenId);
//        userAssetService.pay(userId, asset.getTokenId(), payToken, asset.getNodePrice(), UserLogType.BuyNode.getValue(), UserLogType.BuyNode.getLabel() + "【" + tokenId + "】");
//
//        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
//        this.update(new LambdaUpdateWrapper<StakeUser>()
//                .setSql("node = node + 1")
//                .eq(BaseEntity::getId, stakeUser.getId()));

        // 返佣, 4.28去掉
        // if (user.hasParent()) {
        //     User pUser = userService.getById(user.getPid());
        //     StakeUser pStakeUser = getStakeUser(pUser.getId(), tokenId);
        //     BizAssert.notNull(pStakeUser, () -> Errors.NO_INVITE_EXCEPTION);
        //     BizAssert.notNull(pStakeUser.getId(), () -> Errors.NO_INVITE_EXCEPTION);
        //     if (pStakeUser.isBuyNode()) {
        //         BigDecimal amount = asset.getNodePrice().multiply(asset.getNodeReward()).setScale(8, RoundingMode.DOWN);
        //         log.info("购买节点返佣 UID = {}, PID = {}, tokenId = {}, amount = {}", user.getPid(), user.getId(), tokenId, amount);
        //         this.update(new LambdaUpdateWrapper<StakeUser>()
        //                 .setSql("node_reward = node_reward + {0}", amount)
        //                 .eq(BaseEntity::getId, pStakeUser.getId()));
        //         userLogService.addLog(pUser.getId(), UserLogType.BuyNodeReward.getValue(), amount, UserLogType.BuyNodeReward.getLabel(), payToken, tokenId);    
        //     } else {
        //         log.info("购买节点返佣 UID = {}, PID = {}, tokenId = {}, 没有购买节点，没有返佣", user.getPid(), user.getId(), tokenId);
        //     }
        // }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buyNodeFromWallet(Events events) {
//        Asset asset = assetService.findByNodeAddress(events.getContract());
//        Events.Data data = events.getData();
//        String tokenId = asset.getTokenId();
//        User user = userService.findByAddress(data.getUser());
//        if (user == null) {
//            log.error("[stake] 购买节点失败， 找不到用户地址 = {}", data.getUser());
//            return;
//        }
//        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
//        if (stakeUser.isBuyNode()) {
//            log.warn("[stake] 购买节点失败， 用户已经购买节点， UID = {}, tokenId = {}", user.getId(), tokenId);
//            return;
//        }
//
//        String payToken = AssetEnum.BNB.getTokenId();
//        // log 购买节点
//        userLogService.addLog(user.getId(), UserLogType.BuyNode.getValue(), asset.getNodePrice().negate(), UserLogType.BuyNode.getLabel() + "【" + tokenId + "】", payToken, tokenId);
//
//        this.update(new LambdaUpdateWrapper<StakeUser>()
//                .setSql("node = node + 1")
//                .eq(BaseEntity::getId, stakeUser.getId()));

        // 返佣，4.28去掉
        // if (user.hasParent()) {
        //     User pUser = userService.getById(user.getPid());
        //     StakeUser pStakeUser = getStakeUser(pUser.getId(), tokenId);
        //     BizAssert.notNull(pStakeUser, () -> Errors.NO_INVITE_EXCEPTION);
        //     BizAssert.notNull(pStakeUser.getId(), () -> Errors.NO_INVITE_EXCEPTION);
        //     if (pStakeUser.isBuyNode()) {
        //         BigDecimal amount = asset.getNodePrice().multiply(asset.getNodeReward()).setScale(8, RoundingMode.DOWN);
        //         log.info("购买节点返佣 UID = {}, PID = {}, tokenId = {}, amount = {}", user.getPid(), user.getId(), tokenId, amount);
        //         this.update(new LambdaUpdateWrapper<StakeUser>()
        //                 .setSql("node_reward = node_reward + {0}", amount)
        //                 .eq(BaseEntity::getId, pStakeUser.getId()));
        //         userLogService.addLog(pUser.getId(), UserLogType.BuyNodeReward.getValue(), amount, UserLogType.BuyNodeReward.getLabel(), payToken, tokenId);
        //     } else {
        //         log.info("购买节点返佣 UID = {}, PID = {}, tokenId = {}, 没有购买节点，没有返佣", user.getPid(), user.getId(), tokenId);
        //     }
        // }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawNodeReward(User user, Asset asset, BigDecimal amount) {
//        // 检查余额
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), asset.getTokenId());
//        BizAssert.isTrue(stakeUser.getNodeReward().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//        log.info("提现节点返佣 UID = {}, amount = {}", user.getId(), amount);
//        // 更新余额
//        this.update(new LambdaUpdateWrapper<StakeUser>()
//                .setSql("node_reward = node_reward - {0}", amount)
//                .apply("node_reward - {0} >= 0", amount)
//                .eq(BaseEntity::getId, stakeUser.getId()));
//        // 提现
//        userLogService.addLog(user.getId(), UserLogType.BuyNodeReward.getValue(), amount.negate(), UserLogType.BuyNodeReward.getLabel(), AssetEnum.BNB.getTokenId(), asset.getTokenId());
//        userAssetService.plus(user.getId(), AssetEnum.BNB.getTokenId(), amount, UserLogType.WithdrawNodeReward.getValue(), UserLogType.WithdrawNodeReward.getLabel(), asset.getTokenId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawNodePool(User user, Asset asset, BigDecimal amount) {
//        // 提取节点池
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), asset.getTokenId());
//        BizAssert.isTrue(stakeUser.getNodePool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//        log.info("提现节点池 UID = {}, amount = {}", user.getId(), amount);
//        // 更新余额
//        this.update(new LambdaUpdateWrapper<StakeUser>()
//                .setSql("node_pool = node_pool - {0}", amount)
//                .apply("node_pool - {0} >= 0", amount)
//                .eq(BaseEntity::getId, stakeUser.getId()));
//        // 提现
//        userAssetService.plus(user.getId(), asset.getTokenId(), amount, UserLogType.WithdrawNodePool.getValue(), UserLogType.WithdrawNodePool.getLabel(), asset.getTokenId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RustService.WithdrawSign withdrawNodeWallet(User user, String tokenId, BigDecimal amount) {
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
//        BigDecimal canReceive = stakeUser.getNodeReward();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
//        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
//        Asset asset = assetService.getByTokenId(AssetEnum.BNB.getTokenId());
//        log.info("领取收益 UID = {}, amount = {}", stakeUser.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("node_reward = node_reward - {0}", amount)
//                .apply("node_reward - {0} >= 0", amount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//        BigDecimal _amount = amount.multiply(new BigDecimal("1e18")).setScale(0, RoundingMode.HALF_UP);
//
//        RustService.WithdrawSign withdrawSignVo = rustService.getWithdrawSignVo(asset.getWithdrawAddress(), asset.getTokenAddress(), user.getAddress(), _amount.toPlainString(), "3");
//        withdrawService.save(new Withdraw(3, user, asset, null, amount, BigDecimal.ZERO, withdrawSignVo).setTokenId(tokenId));
//
//        userLogService.addLog(user.getId(), UserLogType.WithdrawNodeReward.getValue(), amount.negate(), UserLogType.WithdrawNodeReward.getLabel(), AssetEnum.BNB.getTokenId(), tokenId);
//
//        return withdrawSignVo;
        return null;
    }

    @Override
    public RustService.WithdrawSign receiveNodeWallet(User user, WithdrawForm form) {
//        BigDecimal amount = form.getAmount();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//
//        BizAssert.isTrue(stakeUser.getNodePool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
//
//        Asset asset = assetService.getByTokenId(form.getTokenId());
//
//        log.info("领取周分红 UID = {}, amount = {}", user.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("node_pool = node_pool - {0}", amount)
//                .apply("node_pool - {0} >= 0", amount)
//                .eq("id", stakeUser.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//
//        BigDecimal amountWei = amount.multiply(new BigDecimal("1e18")).setScale(0, RoundingMode.HALF_UP);
//
//        RustService.WithdrawSign withdrawSignVo = rustService.getWithdrawSignVo(asset.getWithdrawAddress(), asset.getTokenAddress(), user.getAddress(), amountWei.toPlainString(), "4");
//
//        withdrawService.save(new Withdraw(4, user, asset, null, amount, BigDecimal.ZERO, withdrawSignVo));
//
//        userLogService.addLog(user.getId(), UserLogType.WithdrawNodePool.getValue(), amount.negate(), UserLogType.WithdrawNodePool.getLabel(), asset.getTokenId(), asset.getTokenId());
//
//        return withdrawSignVo;
        return null;
    }

    @Override
    public List<StakeModel> listStakeUsers(String tokenId) {
        return this.baseMapper.listStakeUsers(tokenId);
    }

    @Override
    public void setMaxNodePerf(String tokenId) {
        this.update(new UpdateWrapper<StakeUser>()
                .setSql("max_node_perf = GREATEST(max_node_perf, node_perf)")
                .eq("token_id", tokenId));
    }

    @Override
    public void addNodePool(StakeUser stakeUser, BigDecimal amount) {
        boolean result = this.update(new UpdateWrapper<StakeUser>()
                .setSql("node_pool = node_pool + {0}", amount)
                .eq("user_id", stakeUser.getUserId())
                .eq("token_id", stakeUser.getTokenId()));

        userLogService.addLog(stakeUser.getUserId(), UserLogType.NodeDividend.getValue(), amount, UserLogType.NodeDividend.getLabel(), stakeUser.getTokenId(), stakeUser.getTokenId());
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);

    }


    @Override
    public StakeUser sumChilds(Long id) {
        UserRelation userRelation = userRelationMapper.selectById(id);
        if (userRelation == null) {
            return new StakeUser()
                    .setTotalStatic(BigDecimal.ZERO)
                    .setTotalDynamic(BigDecimal.ZERO);
        }
        String path = Optional.ofNullable(userRelation.getPath()).orElse("/" + id);
        return getBaseMapper().sumChilds(path + "/%");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawStakeStaticPool(Long userId, String tokenId, BigDecimal amount) {
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, "提取金额必须大于0");

        StakeUser stakeUser = getStakeUser(userId, tokenId);
        BizAssert.notNull(stakeUser, "用户质押记录不存在");

        BigDecimal staticPool = stakeUser.getStaticPool() != null ? stakeUser.getStaticPool() : BigDecimal.ZERO;
        BizAssert.isTrue(staticPool.compareTo(amount) >= 0, "静态池余额不足");

        // 扣减静态池金额
        int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, "扣减静态池金额失败");

        // 记录用户日志
        userLogService.addLog(userId, UserLogType.RECEIVE.getValue(), amount,
            "提取质押静态池", tokenId, tokenId);

        log.info("用户 {} 提取质押静态池成功，金额：{}", userId, amount);
        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawStakeDynamicPool(Long userId, String tokenId, BigDecimal amount) {
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, "提取金额必须大于0");

        StakeUser stakeUser = getStakeUser(userId, tokenId);
        BizAssert.notNull(stakeUser, "用户质押记录不存在");

        BigDecimal dynamicPool = stakeUser.getDynamicPool() != null ? stakeUser.getDynamicPool() : BigDecimal.ZERO;
        BizAssert.isTrue(dynamicPool.compareTo(amount) >= 0, "动态池余额不足");

        // 扣减动态池金额
        int updateRow = baseMapper.updateDynamicPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, "扣减动态池金额失败");

        // 记录用户日志
        userLogService.addLog(userId, UserLogType.RECEIVE.getValue(), amount,
            "提取质押动态池", tokenId, tokenId);

        log.info("用户 {} 提取质押动态池成功，金额：{}", userId, amount);
        return amount;
    }
}
