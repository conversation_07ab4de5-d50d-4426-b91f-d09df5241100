package com.aic.app.service.impl;

import com.aic.app.mapper.WithdrawMapper;
import com.aic.app.model.Withdraw;
import com.aic.app.service.IWithdrawService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【withdraw】的数据库操作Service实现
* @createDate 2025-03-15 13:35:02
*/
@Service
public class WithdrawServiceImpl extends ServiceImpl<WithdrawMapper, Withdraw>
    implements IWithdrawService {
    
    @Override
    public Withdraw findByAddress(String address, Integer type, Long nonce) {
        return getOne(new LambdaQueryWrapper<Withdraw>()
                .eq(Withdraw::getAddress, address)
                .eq(Withdraw::getType, type)
                .eq(Withdraw::getNonce, nonce));
    }

   
}




