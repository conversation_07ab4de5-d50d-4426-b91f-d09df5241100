package com.aic.app.service.impl;

import com.aic.app.mapper.RewardLogMapper;
import com.aic.app.model.AssetEnum;
import com.aic.app.model.RewardLog;
import com.aic.app.service.IRewardLogService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class IRewardLogServiceImpl extends ServiceImpl<RewardLogMapper, RewardLog> implements IRewardLogService {
    @Override
    public BigDecimal sumYesterdayAmount() {
        return BigDecimal.ZERO;
    }
    
    @Override
    public BigDecimal sumParentAmount(Long userId) {
        return baseMapper.sumParentAmount(userId);
    }

    @Override
    public <E extends IPage<RewardLog>> E page(E page, Wrapper<RewardLog> queryWrapper) {
        return getBaseMapper().page(page, queryWrapper);
    }
}
