package com.aic.app.service.impl;

import com.aic.app.mapper.UserLogMapper;
import com.aic.app.model.*;
import com.aic.app.service.IUserLogService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.date.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Service
public class UserLogServiceImpl extends ServiceImpl<UserLogMapper, UserLog> implements IUserLogService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserLog addLog(User user, UserLogType userLogType, BigDecimal amount, String... remark) {
        UserLog userLog = new UserLog(user.getId(), userLogType.getValue(), BigDecimal.ZERO, amount, remark.length > 0 ? remark[0] : null);
        userLog.setSymbol(remark.length > 1 ? remark[1] : "");
        this.save(userLog);
        return userLog;
    }

    public UserLog addLog(Long userId, int userLogType, BigDecimal amount, String... remark) {
        UserLog userLog = new UserLog(userId, userLogType, BigDecimal.ZERO, amount, remark.length > 0 ? remark[0] : null);
        userLog.setSymbol(remark.length > 1 ? remark[1] : "");
        userLog.setTokenId(remark.length > 2 ? remark[2] : null);
        this.save(userLog);
        return userLog;
    }

    @Override
    public <E extends IPage<UserLog>> E page(E page, Wrapper<UserLog> queryWrapper) {
        return getBaseMapper().page(page, queryWrapper);
    }

    @Override
    public BigDecimal getTodayAmount() {
        return getBaseMapper().getTodayAmount(DateUtil.beginOfDay(DateUtil.date()));
    }

    @Override
    public BigDecimal getFeeAmount(LocalDate date) {
        // 2% 解除质押手续费，TOKEN单位，流水记录的是98%实际到账的值，这里先算回去原金额，再算手续费
        BigDecimal unStakeAmount = getBaseMapper().getAmount(date, UserLogType.RedeemCurrent.getValue());
        unStakeAmount = unStakeAmount.divide(new BigDecimal("0.98"), 8, RoundingMode.HALF_UP);
        return unStakeAmount.multiply(new BigDecimal("0.02"));
    }

    @Override
    public BigDecimal getBurnAmount(LocalDate date) {
        // 销毁的数额
        return getBaseMapper().getAmount(date, UserLogType.Burn.getValue());
    }

    @Override
    public IPage<RewardData> pageRewardData(IPage<RewardData> page) {
        return getBaseMapper().pageRewardData(page);
    }

    @Override
    public BigDecimal getTotalInterest() {
        return getBaseMapper().getTotalInterest();
    }
}
