package com.aic.app.service.impl;

import com.aic.app.exception.Errors;
import com.aic.app.util.BizAssert;
import com.aic.app.form.WithdrawNormalProductForm;
import com.aic.app.mapper.UserProductMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.Utils;
import com.aic.app.vo.BondPurchaseRecordVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Service
@Slf4j
public class IUserProductServiceImpl extends ServiceImpl<UserProductMapper, UserProduct> implements IUserProductService {

    @Resource
    private IWithdrawService withdrawService;


    @Override
    public <E extends IPage<UserProduct>> E page(E page, Wrapper<UserProduct> queryWrapper) {
        return getBaseMapper().page(page, queryWrapper);
    }

    @Override
    public BigDecimal sum() {
        UserProduct userProduct = this.getOne(new QueryWrapper<UserProduct>().select("IFNULL(SUM(power), 0) as power"));
        return userProduct.getPower();
    }

    @Override
    public UserProduct findUserProduct(String userId, int productId) {
        LambdaQueryWrapper<UserProduct> qw = new LambdaQueryWrapper<UserProduct>()
                .eq(UserProduct::getUserId, userId)
                .eq(UserProduct::getProductId, productId)
                .last("limit 1");
        return getOne(qw);
    }

    @Override
    public UserProduct getUserProduct(String uid, int id) {
        return getOne(new LambdaQueryWrapper<UserProduct>().eq(UserProduct::getUserId, uid).eq(UserProduct::getId, id));
    }

    @Override
    public void updateProduct6Rate() {
    }

    @Override
    public IPage<UserProduct> pageChildOrders(Page<UserProduct> page, Wrapper<UserProduct> qw) {
        return baseMapper.pageChildOrders(page, qw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RustService.WithdrawSign withdrawNormalProductWallet(User user, WithdrawNormalProductForm form) {
        BizAssert.isTrue(user.getType() == 1, () -> Errors.ACCOUNT_EXCEPTION);
        BizAssert.isTrue(form.getAmount() != null && form.getAmount().compareTo(BigDecimal.ZERO) > 0, "无效提取数量");

        // 1. 验证订单存在且属于当前用户
        UserProduct userProduct = this.getById(form.getUserProductId());
        BizAssert.notNull(userProduct, "订单不存在");
        BizAssert.isTrue(userProduct.getUserId().equals(user.getId()), "无权限操作此订单");
        BizAssert.isTrue(userProduct.getType() == 2, "只能提取普通商品订单");
        BizAssert.isTrue(userProduct.getStatus() == 1 || userProduct.getStatus() == 3, "订单状态异常");

        // 2. 验证可提取金额
        BigDecimal availableAmount = userProduct.getAvailableAmount() != null ? userProduct.getAvailableAmount() : BigDecimal.ZERO;
        BizAssert.isTrue(availableAmount.compareTo(form.getAmount()) >= 0, "可提取金额不足");

        // 3. 创建XYC资产信息（简化处理，都是XYC）
        Asset asset = new Asset();
        asset.setTokenId("XYC");
        asset.setTokenAddress("0x8a6556FaA0846d329D470Ce1342236ca2c6609d0"); // XYC token地址
        asset.setWithdrawAddress("0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca"); // 提取合约地址

        log.info("普通商品提取到钱包 UID = {}, 订单号 = {}, amount = {}",
            user.getId(), userProduct.getOrderNo(), form.getAmount());

        // 4. 生成提取签名（转换为Wei单位）
        BigDecimal amountWei = form.getAmount().multiply(new BigDecimal("1e18")).setScale(0, RoundingMode.HALF_UP);
        RustService.WithdrawSign withdrawSign = RustService.getWithdrawSignVo(
            asset.getWithdrawAddress(),
            asset.getTokenAddress(),
            user.getAddress(),
            amountWei.toPlainString(),
            "5" // type=5 表示普通商品提取
        );

        // 5. 扣减可提取金额
        boolean updateResult = this.update(
            new LambdaUpdateWrapper<UserProduct>()
                .eq(UserProduct::getId, userProduct.getId())
                .set(UserProduct::getAvailableAmount, availableAmount.subtract(form.getAmount()))
                .apply("available_amount - {0} >= 0", form.getAmount())
        );
        BizAssert.isTrue(updateResult, "扣减可提取金额失败");

        // 6. 创建提取记录
        Withdraw withdraw = new Withdraw(5, user, asset, userProduct.getId().intValue(),
            form.getAmount(), BigDecimal.ZERO, withdrawSign);
        withdrawService.save(withdraw);

        log.info("普通商品提取记录创建成功，提取ID = {}, 订单号 = {}",
            withdraw.getId(), userProduct.getOrderNo());

        return withdrawSign;
    }

    @Override
    public IPage<BondPurchaseRecordVo> pageBondPurchaseRecords(Page<BondPurchaseRecordVo> page, Wrapper<UserProduct> queryWrapper) {
        return getBaseMapper().pageBondPurchaseRecords(page, queryWrapper);
    }
}
