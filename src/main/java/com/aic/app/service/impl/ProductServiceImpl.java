package com.aic.app.service.impl;

import com.aic.app.mapper.ProductMapper;
import com.aic.app.model.AssetEnum;
import com.aic.app.model.Product;
import com.aic.app.service.IProductService;
import com.aic.app.service.RustService;
import com.aic.app.util.Utils;
import com.aic.app.vo.JuCoinPriceVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;

@Service
@Slf4j
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

    private final HttpClient httpClient = newHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Value("${jc.host}")
    private String jcHost;

    public HttpClient newHttpClient() {
        HttpClient.Builder builder = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(5));
        if (Utils.isDev()) {
            builder = builder.proxy(java.net.ProxySelector.of(new java.net.InetSocketAddress("127.0.0.1", 1087)));
        }
        return builder
                .build();
    }

    @Override
    public BigDecimal getPrice(String tokenId) {
        // 优先从缓存里面取
        String key = "fist:cache:price:" + tokenId;
        Object cachePrice = stringRedisTemplate.opsForValue().get(key);
        if (cachePrice != null) {
//            log.info("[price] get price {} from cache = {}", tokenId, cachePrice);
            return new BigDecimal(cachePrice.toString());
        }
        BigDecimal price = null;
        if (AssetEnum.JU.getTokenId().equals(tokenId)) {
            price = getJuCoinPrice("ju_usdt");
        } else if (AssetEnum.BNB.getTokenId().equals(tokenId)) {
            price = getJuCoinPrice("bnb_usdt");
        } else if (AssetEnum.XYC.getTokenId().equals(tokenId)) {
            // String tokenAddress = assetService.getByTokenId(tokenId).getTokenAddress();
            String tokenAddress = "0xC70a64922B5968aE39f442E8CD296279378a5708";
            price = new BigDecimal(RustService.getTokenPrice(tokenAddress));
        }
        if (price == null) {
            price = BigDecimal.ZERO;
        }
        if (price.compareTo(BigDecimal.ZERO) > 0) {
            log.info("[price] set price {} from cache = {}", tokenId, price);
            stringRedisTemplate.opsForValue().set(key, price.toPlainString(), Duration.ofSeconds(60));
        }
        return price;

    }

    @Override
    public BigDecimal getJuCoinPrice(String symbol) {
        try {
            String body = httpClient.send(HttpRequest.newBuilder().GET().uri(URI.create(jcHost + "/v1/spot/public/ticker/price?symbol=" + symbol)).build(),
                    HttpResponse.BodyHandlers.ofString()).body();
            JuCoinPriceVo vo = objectMapper.readValue(body, JuCoinPriceVo.class);
            if (vo != null && vo.getPrice(symbol).compareTo(BigDecimal.ZERO) > 0) {
                // 每次取完之后都放到缓存，缓存时间一分钟
                return vo.getPrice(symbol);
            } else {
                log.error("获取价格失败 symbol = {} data = {}", symbol, vo);
            }
        } catch (Exception e) {
            log.error("获取价格失败", e);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public List<Product> listProducts() {
        return this.list(new LambdaQueryWrapper<Product>()
                .eq(Product::getEnable, true));
    }

    @Override
    public BigDecimal getSoldSumByType(Integer type) {
        // 使用SQL SUM函数统计指定类型产品的已售总数
        BigDecimal result = baseMapper.selectSoldSumByType(type);
        // 确保结果最小为0
        return result != null ? result : BigDecimal.ZERO;
    }


}
