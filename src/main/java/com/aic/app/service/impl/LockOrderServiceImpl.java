package com.aic.app.service.impl;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.model.*;
import com.aic.app.service.BizLogService;
import com.aic.app.service.ILockOrderService;
import com.aic.app.service.IUserLogService;
import com.aic.app.service.ISysConfigService;
import com.aic.app.util.BizAssert;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 锁仓订单服务实现
 */
@Service
@Slf4j
public class LockOrderServiceImpl extends ServiceImpl<LockOrderMapper, LockOrder> implements ILockOrderService {
    
    @Resource
    private IUserLogService userLogService;
    
    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private BizLogService bizLogService;
    
    @Override
    public LockOrder createLockOrderByProduct(UserProduct userProduct, Product product, BigDecimal amount) {
        LockOrder lockOrder = new LockOrder(
            generateLockOrderNo(),
            userProduct.getUserId(),
            product.getType(),
            userProduct.getId(),
            AssetEnum.XYC.getTokenId(),
            amount
        );
        
        // 根据产品类型设置锁仓规则
        switch (product.getType()) {
            case 1: // 节点产品
                setupNodeLockOrder(lockOrder, product);
                break;
            case 2: // 普通商品
                setupNormalProductLockOrder(lockOrder, product);
                break;
            case 3: // 债券产品
                setupBondLockOrder(lockOrder, product);
                break;
            default:
                throw new IllegalArgumentException("不支持的产品类型: " + product.getType());
        }
        
        // 保存锁仓订单
        this.save(lockOrder);
        
        // 记录创建日志
        recordLockOrderLog(lockOrder.getUserId(), UserLogType.LOCK_ORDER_CREATE.getCode(),
            lockOrder.getLockAmount(), "创建锁仓订单", lockOrder.getId(), lockOrder.getSourceType());
        
        log.info("创建锁仓订单成功，订单号: {}, 用户ID: {}, 金额: {}", 
            lockOrder.getOrderNo(), lockOrder.getUserId(), lockOrder.getLockAmount());
        
        return lockOrder;
    }
    
    private void setupNodeLockOrder(LockOrder lockOrder, Product product) {
        lockOrder.setTotalDays(360);
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(lockOrder.getTotalDays()), 8, RoundingMode.HALF_UP)
        );
    }
    
    private void setupNormalProductLockOrder(LockOrder lockOrder, Product product) {
        lockOrder.setTotalDays(360); // 固定360天
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(lockOrder.getTotalDays()), 8, RoundingMode.HALF_UP)
        );
    }
    
    private void setupBondLockOrder(LockOrder lockOrder, Product product) {
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(product.getDay()), 8, RoundingMode.HALF_UP)
        );

        lockOrder.setTotalDays(product.getDay());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processLinearRelease() {
        List<LockOrder> lockOrders = baseMapper.getLinearReleaseOrders();
        
        for (LockOrder lockOrder : lockOrders) {
            processLinearLockOrderRelease(lockOrder);
        }
        
        log.info("线性释放处理完成，处理订单数: {}", lockOrders.size());
    }
    
    private void processLinearLockOrderRelease(LockOrder lockOrder) {
        BigDecimal dailyAmount = lockOrder.getDailyReleaseAmount();
        if (dailyAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 检查是否已达到总天数
        if (lockOrder.getReleasedDays() >= lockOrder.getTotalDays()) {
            if (lockOrder.getStatus() != 2) {
                // 只更新状态字段
                this.update(new LambdaUpdateWrapper<LockOrder>()
                    .set(LockOrder::getStatus, 2)
                    .eq(LockOrder::getId, lockOrder.getId()));
            }
            return;
        }

        BigDecimal newAvailable = lockOrder.getAvailableAmount().add(dailyAmount);
        BigDecimal newReleased = lockOrder.getReleasedAmount().add(dailyAmount);
        int newReleasedDays = lockOrder.getReleasedDays() + 1;

        // 检查是否释放完成
        if (newReleasedDays >= lockOrder.getTotalDays()) {
            // 最后一次释放，确保释放完全部金额
            BigDecimal remainingAmount = lockOrder.getLockAmount().subtract(lockOrder.getReleasedAmount());
            newAvailable = lockOrder.getAvailableAmount().add(remainingAmount);
            newReleased = lockOrder.getLockAmount();

            // 最后一次释放，更新所有相关字段并标记为已完成
            this.update(new LambdaUpdateWrapper<LockOrder>()
                .set(LockOrder::getAvailableAmount, newAvailable)
                .set(LockOrder::getReleasedAmount, newReleased)
                .set(LockOrder::getReleasedDays, newReleasedDays)
                .set(LockOrder::getStatus, 2)
                .eq(LockOrder::getId, lockOrder.getId()));
        } else {
            // 正常释放，只更新释放相关字段
            this.update(new LambdaUpdateWrapper<LockOrder>()
                .set(LockOrder::getAvailableAmount, newAvailable)
                .set(LockOrder::getReleasedAmount, newReleased)
                .set(LockOrder::getReleasedDays, newReleasedDays)
                .eq(LockOrder::getId, lockOrder.getId()));
        }

        // 记录释放日志
        recordLockOrderLog(lockOrder.getUserId(), UserLogType.LOCK_RELEASE_LINEAR.getCode(),
            dailyAmount, String.format("锁仓订单线性释放，第%d天", newReleasedDays),
            lockOrder.getId(), lockOrder.getSourceType());
    }
    
    @Override
    public void calculateStaticRewards() {
        List<LockOrder> lockOrders = baseMapper.getStaticRewardOrders();
        SysConfig sysConfig = sysConfigService.getSysConfig();

        for (LockOrder lockOrder : lockOrders) {
            // 静态收益
            BigDecimal baseAmount = lockOrder.getAvailableAmount();
            BigDecimal staticReward = baseAmount.multiply(sysConfig.getRewardRate());

            if (staticReward.compareTo(BigDecimal.ZERO) > 0) {
                // 记录静态收益日志
                recordLockOrderLog(lockOrder.getUserId(), UserLogType.LOCK_STATIC_REWARD.getCode(),
                    staticReward, "锁仓静态收益", lockOrder.getId(), lockOrder.getSourceType());
            }
        }
        
        log.info("静态收益计算完成，处理订单数: {}", lockOrders.size());
    }
    
    @Override
    public void calculateDynamicRewards() {
        List<LockOrder> lockOrders = baseMapper.getDynamicRewardOrders();
        
        for (LockOrder lockOrder : lockOrders) {
            BigDecimal dynamicReward = calculateLockOrderDynamicReward(lockOrder);
            if (dynamicReward.compareTo(BigDecimal.ZERO) > 0) {
                // 记录动态收益日志
                recordLockOrderLog(lockOrder.getUserId(), UserLogType.LOCK_DYNAMIC_REWARD.getCode(),
                    dynamicReward, "锁仓动态收益", lockOrder.getId(), lockOrder.getSourceType());
            }
        }
        
        log.info("动态收益计算完成，处理订单数: {}", lockOrders.size());
    }
    
    @Override
    public BigDecimal getUserTotalLockAmount(Long userId, String tokenId) {
        return baseMapper.getUserTotalLockAmount(userId, tokenId);
    }
    
    @Override
    public BigDecimal getUserAvailableAmount(Long userId, String tokenId) {
        return baseMapper.getUserAvailableAmount(userId, tokenId);
    }
    
    @Override
    public String generateLockOrderNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        long random = (long) (Math.random() * 10000);
        return "LOCK" + timestamp + String.format("%04d", random);
    }
    
    @Override
    public void recordLockOrderLog(Long userId, Integer logType, BigDecimal amount, 
                                  String operation, Long lockOrderId, Integer sourceType) {
        String remark = String.format("%s - 锁仓订单ID: %d, 产品类型: %d", 
                                     operation, lockOrderId, sourceType);
        bizLogService.recordLog(userId, lockOrderId, operation, amount, remark);
        // 释放记录 userLog
        userLogService.addLog(userId, UserLogType.LOCK_RELEASE_LINEAR.getValue(), amount, remark);
    }
    
    // 辅助方法
    private BigDecimal getConfigStaticRate() {
        SysConfig sysConfig = sysConfigService.getSysConfig();
        return sysConfig.getRewardRate();
    }
    
    private boolean isBondLinearRelease(Product product) {
        return product.getName().contains("线性") || 
               (product.getDescription() != null && product.getDescription().contains("LINEAR"));
    }

    private String generateStepConfig(Product product) {
        // 生成阶梯释放配置，这里可以根据产品属性生成
        return "{\"steps\": [{\"day\": 30, \"percent\": 50}, {\"day\": 60, \"percent\": 50}]}";
    }
    
    private void processStepReleaseByConfig(LockOrder lockOrder, String lockConfig) {
        // TODO: 实现阶梯释放逻辑，需要解析JSON配置
        log.info("处理阶梯释放，订单ID: {}, 配置: {}", lockOrder.getId(), lockConfig);
    }
    

    private BigDecimal calculateLockOrderDynamicReward(LockOrder lockOrder) {
        // TODO: 实现动态收益计算逻辑
        return BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawLockOrderRelease(Long userId, Long lockOrderId, BigDecimal amount) {
        // 1. 验证锁仓订单存在且属于当前用户
        LockOrder lockOrder = this.getById(lockOrderId);
        BizAssert.notNull(lockOrder, "锁仓订单不存在");
        BizAssert.isTrue(lockOrder.getUserId().equals(userId), "无权限操作此锁仓订单");
        BizAssert.isTrue(lockOrder.getStatus() == 1, "锁仓订单状态异常");

        // 2. 验证可提取金额
        BigDecimal availableAmount = lockOrder.getAvailableAmount() != null ?
            lockOrder.getAvailableAmount() : BigDecimal.ZERO;
        BizAssert.isTrue(availableAmount.compareTo(amount) >= 0, "可提取金额不足");

        // 3. 扣减可提取金额
        boolean updateResult = this.update(
            new LambdaUpdateWrapper<LockOrder>()
                .eq(LockOrder::getId, lockOrderId)
                .set(LockOrder::getAvailableAmount, availableAmount.subtract(amount))
                .apply("available_amount - {0} >= 0", amount)
        );
        BizAssert.isTrue(updateResult, "扣减可提取金额失败");

        // 4. 记录日志
        recordLockOrderLog(userId, UserLogType.LOCK_RELEASE_LINEAR.getCode(),
            amount, "提取锁仓释放", lockOrderId, lockOrder.getSourceType());

        log.info("用户 {} 提取锁仓释放成功，订单ID：{}, 金额：{}", userId, lockOrderId, amount);
        return amount;
    }

    @Override
    public BigDecimal getUserLockStaticPool(Long userId, String tokenId) {
        // 暂时返回0，后续根据业务需求实现锁仓静态池逻辑
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getUserLockDynamicPool(Long userId, String tokenId) {
        // 暂时返回0，后续根据业务需求实现锁仓动态池逻辑
        return BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawLockStaticPool(Long userId, String tokenId, BigDecimal amount) {
        // 暂时返回0，后续根据业务需求实现锁仓静态池提取逻辑
        BizAssert.isTrue(false, "锁仓静态池提取功能暂未开放");
        return BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawLockDynamicPool(Long userId, String tokenId, BigDecimal amount) {
        // 暂时返回0，后续根据业务需求实现锁仓动态池提取逻辑
        BizAssert.isTrue(false, "锁仓动态池提取功能暂未开放");
        return BigDecimal.ZERO;
    }
}