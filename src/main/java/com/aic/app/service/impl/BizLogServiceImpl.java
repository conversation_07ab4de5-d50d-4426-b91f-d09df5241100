package com.aic.app.service.impl;

import com.aic.app.model.BizLog;
import com.aic.app.mapper.BizLogMapper;
import com.aic.app.service.BizLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class BizLogServiceImpl extends ServiceImpl<BizLogMapper, BizLog> implements BizLogService {
    
    ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordLog(Long userId, Long bizId, String operation, Object args, String... remark) {
        BizLog bizLog = new BizLog();
        bizLog.setUserId(userId);
        bizLog.setBizId(bizId);
        bizLog.setOperation(operation);
        if (args != null) {
            if (args instanceof String x) {
                bizLog.setArgs(x);
            } else {
                try {
                    bizLog.setArgs(objectMapper.writeValueAsString(args));   
                } catch (Exception e) {
                    log.error("[biz] 用户 = {}, 操作 = {}, 参数保存异常 = {}", userId, operation, args);
                }
            }
        }
        bizLog.setRemark(remark.length > 0 ? remark[0] : null);
        save(bizLog);
    }
} 
