package com.aic.app.service.impl;

import com.aic.app.mapper.UserAddressMapper;
import com.aic.app.model.BaseEntity;
import com.aic.app.model.UserAddress;
import com.aic.app.service.IUserAddressService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressMapper, UserAddress> implements IUserAddressService {

    @Override
    public UserAddress findUserAddress(Long userId, int id) {
        return getOne(new LambdaQueryWrapper<UserAddress>()
                .eq(UserAddress::getUserId, userId)
                .eq(BaseEntity::getId, id));
    }
}
