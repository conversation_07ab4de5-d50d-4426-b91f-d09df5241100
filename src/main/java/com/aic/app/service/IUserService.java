package com.aic.app.service;

import com.aic.app.model.User;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

public interface IUserService extends IService<User> {

    void add(User user);

    User getByCode(String code);

    /**
     * 绑定邀请人
     * @param user
     * @param code
     */
    void bindRefCode(User user, String code);

    User findByAddress(String address);

    boolean setLevel(Long id, int level);

    int sumChildren(Long id);

    BigDecimal sumParentReward(Long id);

    void bindJuid(Long userId, String juid);
}
