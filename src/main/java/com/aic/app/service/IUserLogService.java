package com.aic.app.service;

import com.aic.app.model.RewardData;
import com.aic.app.model.User;
import com.aic.app.model.UserLog;
import com.aic.app.model.UserLogType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface IUserLogService extends IService<UserLog> {
    UserLog addLog(User user, UserLogType userLogType, BigDecimal amount, String... remark);

    UserLog addLog(Long userId, int userLogType, BigDecimal amount, String... remark);

    BigDecimal getTodayAmount();

    BigDecimal getFeeAmount(LocalDate yesterday);

    BigDecimal getBurnAmount(LocalDate yesterday);

    IPage<RewardData> pageRewardData(IPage<RewardData> page);

    BigDecimal getTotalInterest();
}
