package com.aic.app.service;

import com.aic.app.model.Product;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

public interface IProductService extends IService<Product> {

    /**
     * 获取价格
     * @param tokenId
     * @return
     */
    BigDecimal getPrice(String tokenId);


    BigDecimal getJuCoinPrice(String symbol);

    List<Product> listProducts();

    /**
     * 统计指定类型产品的已售总数
     * @param type 产品类型
     * @return 已售总数，最小为0
     */
    BigDecimal getSoldSumByType(Integer type);
}
