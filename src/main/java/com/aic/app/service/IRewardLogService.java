package com.aic.app.service;

import com.aic.app.model.RewardLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

public interface IRewardLogService extends IService<RewardLog> {

    /**
     * 合计昨日奖励金额
     * @return
     */
    BigDecimal sumYesterdayAmount();

    /**
     * 获取累计直推收益
     * @param userId
     * @return
     */
    BigDecimal sumParentAmount(Long userId);
}
