package com.aic.app.exception;

public interface Errors {

    BizException LOGIN_EXCEPTION = new BizException(201, "登录失败，请稍后再试");

    BizException AUTH_EXCEPTION = new BizException(401, "need login");
    
    BizException REQUEST_EXCEPTION = new BizException(110, "请求参数错误");
    
    BizException INVITE_CODE_EXCEPTION = new BizException(100, "邀请码错误");
    
    BizException LIMIT_AMOUNT_EXCEPTION = new BizException(101, "购买失败，额度不足");
    
    BizException BALANCE_EXCEPTION = new BizException(102, "购买失败，余额不足");

    BizException INVITE_CODE_EXCEPTION2 = new BizException(103, "邀请码不能重复绑定");
    
    BizException AMOUNT_EXCEPTION = new BizException(104, "赎回失败，可赎回数量不足");
    
    BizException RECEIVE_EXCEPTION = new BizException(105, "领取失败，可领取数量不足");
    
    BizException NO_INVITE_EXCEPTION = new BizException(106, "请先绑定邀请人");
    
    BizException SERVER_EXCEPTION = new BizException(500, "网络超时，请稍后再试");
    BizException ACCOUNT_EXCEPTION = new BizException(230, "账户类型与接口不匹配");
    
    BizException NOT_OPEN_EXCEPTION = new BizException(203, "暂未开放");
    BizException WHITELIST_EXCEPTION = new BizException(204, "非白名单");
    BizException LIMIT_REQUEST_EXCEPTION = new BizException(205, "请求过快，请稍后再试");
    BizException BUY_EXCEPTION = new BizException(206, "单个项目只能认购一次");
    BizException TOTAL_EXCEPTION = new BizException(207, "认购失败，项目额度不足");
    BizException NOT_BUY_EXCEPTION = new BizException(208, "领取失败，未购买");
    BizException NOT_BUY_EXCEPTION2 = new BizException(209, "领取失败，未结算");
    BizException RECEIVED_EXCEPTION = new BizException(210, "领取失败，已领取");
    BizException WITHDRAW_BALANCE_EXCEPTION = new BizException(211, "提取失败，余额不足");
    BizException WITHDRAW_BALANCE_EXCEPTION2 = new BizException(212, "提取失败，BNB余额不足");
    BizException BUY_NODE_EXCEPTION = new BizException(213, "购买节点失败，只能购买一次");
    BizException BURN_EXCEPTION = new BizException(214, "销毁失败，余额不足");
    BizException TRANSFER_EXCEPTION = new BizException(215, "划转失败，次数过多");
    
    
}
