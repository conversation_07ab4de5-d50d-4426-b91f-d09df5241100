package com.aic.app.api;

import com.aic.app.exception.Errors;
import com.aic.app.form.AddressLoginForm;
import com.aic.app.form.ProductQuery;
import com.aic.app.model.AssetEnum;
import com.aic.app.model.Product;
import com.aic.app.model.User;
import com.aic.app.model.StakeUser;
import com.aic.app.service.*;
import com.aic.app.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
@Slf4j
public class IndexController {

    IStakeUserService stakeUserService;
    StringRedisTemplate stringRedisTemplate;
    IUserService userService;
    IProductService productService;

    @GetMapping
    public Object index() {
        return "it work!!";
    }

    // /api/address/login?address=&sign=
    @Operation(summary = "地址登录")
    @PostMapping("/api/address/login")
    public Result<LoginResult> addressLogin(@RequestBody AddressLoginForm form) {
        String address = form.getAddress();
        String sign = form.getSign();
        Boolean ok = RustService.checkSignMsg(address, sign);
        if (!Boolean.TRUE.equals(ok)) {
            throw Errors.LOGIN_EXCEPTION;
        }
        User phoneUser = stakeUserService.checkUser(address);
        String token = UUID.randomUUID().toString();
        stringRedisTemplate.opsForValue().set("xyc:user:token:" + token, phoneUser.getId().toString(), Duration.ofHours(2));
        stringRedisTemplate.opsForHash().putAll("xyc:user:" + phoneUser.getId(), 
                Map.of("token", token));
        stringRedisTemplate.expire("xyc:user:" + phoneUser.getId(), Duration.ofDays(30));
        log.info("用户登录成功 uid = {}", phoneUser.getId());
        return Result.success(new LoginResult(phoneUser, token));
    }

    @Operation(summary = "获取签名消息")
    @GetMapping("/api/sign-msg")
    public Result<String> signMsg(@RequestParam("address") String address) {
        String msg = RustService.getSignMsg(address);
        return Result.success(msg);
    }

//    @Operation(summary = "检查地址是否存在")
//    @GetMapping("/api/check-address")
//    public Result<Boolean> checkAddress(@RequestParam("address") String address) {
//        long count = userService.count(new LambdaQueryWrapper<User>().eq(User::getAddress, address));
//        return Result.success(count > 0);
//    }

    @GetMapping("/api/userinfo")
    @Operation(summary = "用户信息")
    public Result<UserInfoVo> userinfo(@RequestAttribute(value = "user", required = false) User user) {
        UserInfoVo vo = new UserInfoVo(user);
        return Result.success(vo);
    }
    

    @GetMapping("/api/product")
    @Operation(summary = "产品列表")
    public Result<IPage<ProductVo>> product(@ParameterObject ProductQuery query) {
        QueryWrapper<Product> qw = new QueryWrapper<Product>()
                .eq("enable", true)
                .in(query.getTypes() != null && !query.getTypes().isEmpty(), "type", query.getTypes())
                .like(StringUtils.isNotEmpty(query.getName()), "name", query.getName());

        // 动态排序
        if (StringUtils.isNotEmpty(query.getOrder())) {
            String sortField = query.getProp();
            // 验证排序字段，防止SQL注入
            if ("createTime".equals(sortField)) {
                sortField = "create_time";
            } else if ("price".equals(sortField)) {
                sortField = "price";
            } else {
                sortField = "id"; // 默认按ID排序
            }
            qw.orderBy(true, "ascending".equals(query.getOrder()), sortField);
        } else {
            qw.orderByDesc("id"); // 默认按ID降序
        }

        IPage<ProductVo> page = productService.page(new Page<>(query.getPage(), query.getSize()), qw)
                .convert(ProductVo::new);

        return Result.success(page);
    }

    @GetMapping("/api/stake")
    @Operation(summary = "我的质押接口")
    public Result<HomeDataVo> stake(@RequestAttribute(value = "user", required = false) User user) {
        // 1. 从Redis获取全网数据
        BigDecimal totalStakeAmount = getRedisHashValue("xyc:stats", "totalStakeAmount");
        BigDecimal currentIndex = getRedisHashValue("xyc:stats", "currentIndex");
        BigDecimal stakePrincipal = getRedisHashValue("xyc:stats", "stakePrincipal");
        BigDecimal totalInterest = getRedisHashValue("xyc:stats", "totalInterest");
        BigDecimal apy = getRedisHashValue("xyc:stats", "apy");

        // 2. 获取用户个人数据
        BigDecimal myStakeAmount = BigDecimal.ZERO;
        BigDecimal pendingAmount = BigDecimal.ZERO;
        BigDecimal stakeStaticPool = BigDecimal.ZERO;
        BigDecimal stakeDynamicPool = BigDecimal.ZERO;
        BigDecimal lockStaticPool = BigDecimal.ZERO;
        BigDecimal lockDynamicPool = BigDecimal.ZERO;

        if (user != null) {
            // 获取用户质押数据
            StakeUser stakeUser = stakeUserService.getStakeUser(user.getId(), AssetEnum.XYC.getTokenId());
            if (stakeUser != null) {
                myStakeAmount = stakeUser.getCurrentAmount() != null ? stakeUser.getCurrentAmount() : BigDecimal.ZERO;
                pendingAmount = stakeUser.getPendingAmount() != null ? stakeUser.getPendingAmount() : BigDecimal.ZERO;
                stakeStaticPool = stakeUser.getStaticPool() != null ? stakeUser.getStaticPool() : BigDecimal.ZERO;
                stakeDynamicPool = stakeUser.getDynamicPool() != null ? stakeUser.getDynamicPool() : BigDecimal.ZERO;
                lockStaticPool = stakeUser.getLockStaticPool() != null ? stakeUser.getLockStaticPool() : BigDecimal.ZERO;
                lockDynamicPool = stakeUser.getLockDynamicPool() != null ? stakeUser.getLockDynamicPool() : BigDecimal.ZERO;
            }
        }

        // 3. 构造返回数据
        HomeDataVo stakeData = new HomeDataVo(
            totalStakeAmount, currentIndex, stakePrincipal, totalInterest,
            myStakeAmount, pendingAmount, stakeStaticPool, stakeDynamicPool,
            lockStaticPool, lockDynamicPool, apy
        );

        return Result.success(stakeData);
    }

    /**
     * 从Redis Hash中获取BigDecimal值
     */
    private BigDecimal getRedisHashValue(String key, String field) {
        try {
            Object value = stringRedisTemplate.opsForHash().get(key, field);
            if (value != null) {
                return new BigDecimal(value.toString());
            }
        } catch (Exception e) {
            log.warn("从Redis获取数据失败，key: {}, field: {}, error: {}", key, field, e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    @GetMapping("/api/dashboard")
    @Operation(summary = "Dashboard数据")
    public Result<DashboardVo> dashboard(@RequestAttribute(value = "user", required = false) User user) {
        // 允许未登录用户访问，返回结构一致的空数据
        if (user == null) {
            log.info("未登录用户查询Dashboard，返回空数据");
            return Result.success(DashboardVo.createDefault());
        }

        // 暂时返回0值数据，结构保持一致
        DashboardVo dashboardData = DashboardVo.createDefault();
        return Result.success(dashboardData);
    }

    @GetMapping("/api/bonds")
    @Operation(summary = "债券接口")
    public Result<BondsResponseVo> bonds() {
        BondsResponseVo response = new BondsResponseVo();

        // 获取债券列表 (type=3)
        List<Product> bonds = productService.list(new LambdaQueryWrapper<Product>()
                .eq(Product::getType, 3)
                .eq(Product::getEnable, true)
                .orderByDesc(Product::getId));

        // 转换为ProductVo列表
        List<ProductVo> bondList = bonds.stream()
                .map(ProductVo::new)
                .collect(Collectors.toList());

        // 使用SQL SUM函数统计已售债券总数，不判断产品状态，直接统计，最小为0
        BigDecimal soldBonds = productService.getSoldSumByType(3);

        // 获取XYC价格
        BigDecimal xycPrice = productService.getPrice(AssetEnum.XYC.getTokenId());

        response.setSoldBonds(soldBonds);
        response.setXycPrice(xycPrice);
        response.setBondList(bondList);

        return Result.success(response);
    }

}
