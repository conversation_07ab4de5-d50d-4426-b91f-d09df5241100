package com.aic.app.api;

import com.aic.app.form.PageQuery;
import com.aic.app.form.AddressForm;
import com.aic.app.model.BaseEntity;
import com.aic.app.model.User;
import com.aic.app.model.UserAddress;
import com.aic.app.service.IUserAddressService;
import com.aic.app.util.BizAssert;
import com.aic.app.vo.Result;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/user/address")
@Tag(name = "用户地址", description = "用户地址相关接口")
@AllArgsConstructor
public class UserAddressController {

    IUserAddressService UserAddressService;

    @Operation(summary = "我的地址")
    @GetMapping
    public Result<Page<UserAddress>> list(@RequestAttribute("user") User user, PageQuery query) {
        LambdaQueryWrapper<UserAddress> qw = new LambdaQueryWrapper<>();
        qw.eq(UserAddress::getUserId, user.getId());
        qw.orderByDesc(BaseEntity::getId);
        Page<UserAddress> page = UserAddressService.page(new Page<>(query.getPage(), query.getSize()), qw);
        return Result.success(page);
    }

    @Operation(summary = "新增地址")
    @PostMapping
    public Result<Boolean> add(@RequestAttribute("user") User user, @RequestBody AddressForm form) {
        UserAddress UserAddress = new UserAddress(user.getId(), form.getContact(), form.getArea(), form.getPhone(), form.getAddress());
        UserAddressService.save(UserAddress);
        return Result.success(true);
    }

    @Operation(summary = "更新地址")
    @PutMapping
    public Result<Boolean> update(@RequestAttribute("user") User user, @RequestBody AddressForm form) {
        UserAddress userAddress = UserAddressService.findUserAddress(user.getId(), form.getId());
        BizAssert.notNull(userAddress, "地址不存在");
        userAddress.setContact(form.getContact());
        userAddress.setArea(form.getArea());
        userAddress.setPhone(form.getPhone());
        userAddress.setAddress(form.getAddress());
        UserAddressService.updateById(userAddress);
        return Result.success(true);
    }

    @Operation(summary = "更新地址")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@RequestAttribute("user") User user, @PathVariable int id) {
        UserAddress userAddress = UserAddressService.findUserAddress(user.getId(), id);
        BizAssert.notNull(userAddress, "地址不存在");
        UserAddressService.removeById(userAddress);
        return Result.success(true);
    }
}
