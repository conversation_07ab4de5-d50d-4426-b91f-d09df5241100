package com.aic.app.api;

import com.aic.app.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController
@Slf4j
public class JobController {

    StringRedisTemplate stringRedisTemplate;
    private static final String LOCK_KEY_PREFIX = "xyc:job_lock:";

    private String checkAndLock(String lockKey, Runnable runnable) {
        String value = stringRedisTemplate.opsForValue().get(lockKey);
        log.info("获取锁结果 {}", value);
        if (StringUtils.isEmpty(value)) {
            runnable.run();
            return "任务执行成功";
        } else {
            return "任务正在其他实例上执行，请稍后再试";
        }
    }

}
