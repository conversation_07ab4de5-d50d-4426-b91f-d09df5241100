package com.aic.app.api;

import com.aic.app.model.User;
import com.aic.app.service.IUserService;
import com.aic.app.util.Utils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@Slf4j
public class LoginInterceptor implements HandlerInterceptor {

    @Resource
    IUserService userService;
    @Resource
    StringRedisTemplate stringRedisTemplate;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("request.getRequestURI() = {}", request.getRequestURI());
        if (!request.getRequestURI().startsWith("/api")) {
            return true;
        }
        // 请求拦截，防止结算的时候用户提取造成事务冲突
        if ("POST".equals(request.getMethod()) && Utils.isCloseTime()) {
            response.setHeader("Content-Type", "application/json; charset=utf-8");
            response.getWriter().print("""
                {"code":500,"msg":"结算中"}
                """);
            return false;
        }

        String token = request.getHeader("Authorization");
        if (token != null && !token.isEmpty()) {
            // 本地不保存session，以接口为主
            String cookie = token.replaceFirst("Bearer ", "");
            String key = "xyc:user:token:" + cookie;
            String userId = stringRedisTemplate.opsForValue().get(key);
            // log.debug("[login] get redis key {} result = {}", key, userId);

            if (userId != null) {
                User user = userService.getById(userId);
                if (user != null) {
                    // log.debug("鉴权成功");
                    request.setAttribute("user", user);
                    return true;
                }
            }

            // 如果传了token，但是找不到，返回401
            response.setStatus(401);
            response.getWriter().print("""
                {"code":401,"msg":"need login"}
                """);
            return false;
        }
        // 如果是GET请求，则不进行鉴权
        if ("GET".equals(request.getMethod())) {
            return true;
        }
        // log.debug("登录拦截 URL = {}", request.getRequestURL());
        // 鉴权失败
        response.setStatus(401);
        response.getWriter().print("""
                {"code":401,"msg":"need login"}
                """);
        return false;
    }
}
