package com.aic.app.api;

import com.aic.app.form.*;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.StringUtils;
import com.aic.app.util.Utils;
import com.aic.app.vo.*;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/user")
@AllArgsConstructor
@Slf4j
@Tag(name = "用户接口", description = "用户相关接口")
public class UserController {

    IUserService userService;
    IUserProductService userProductService;
    IProductService productService;
    IProductPurchaseService productPurchaseService;
    IUserStakeService userStakeService;
    UserRelationMapper userRelationMapper;
    IUserLogService userLogService;
    ILockOrderService lockOrderService;
    IStakeUserService stakeUserService;
    IWithdrawService withdrawService;
    StringRedisTemplate stringRedisTemplate;

    @GetMapping("/purchase-records")
    @Operation(summary = "获取购买记录")
    public Result<IPage<UserProductVo>> getPurchaseRecords(
            @RequestAttribute(value = "user", required = false) User user,
            @ParameterObject UserProductQuery query) {

        // 如果用户未登录，返回空的分页数据，但结构一致
        if (user == null) {
            log.info("未登录用户查询购买记录，返回空数据");
            Page<UserProductVo> emptyPage = new Page<>(query.getPage(), query.getSize());
            emptyPage.setTotal(0);
            emptyPage.setRecords(java.util.Collections.emptyList());
            return Result.success(emptyPage);
        }

        log.info("用户 {} 查询购买记录，参数：{}", user.getId(), query);

        // 构建查询条件
        LambdaQueryWrapper<UserProduct> qw = new LambdaQueryWrapper<UserProduct>()
                .eq(UserProduct::getUserId, user.getId()) // 只查询当前用户的记录
                .in(query.getTypes() != null && !query.getTypes().isEmpty(),
                    UserProduct::getType, query.getTypes())
                .in(query.getStatus() != null && !query.getStatus().isEmpty(),
                    UserProduct::getStatus, query.getStatus())
                .in(query.getProductId() != null && !query.getProductId().isEmpty(),
                    UserProduct::getProductId, query.getProductId())
                .orderByDesc(UserProduct::getCreateTime); // 按创建时间倒序

        // 添加时间范围查询
        if (query.getStartDate() != null || query.getEndDate() != null) {
            qw.between(UserProduct::getCreateTime,
                      Utils.formatBeginTime(query.getStartDate()),
                      Utils.formatEndTime(query.getEndDate()));
        }

        // 分页查询
        IPage<UserProductVo> page = userProductService.page(
                new Page<>(query.getPage(), query.getSize()), qw)
                .convert(UserProductVo::new);

        // 获取所有产品ID，然后查出来，再把vo的产品给值
        List<Long> productIds = page.getRecords().stream().map(UserProductVo::getProductId).collect(Collectors.toList());
        List<Product> products = productService.listByIds(productIds);
        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        page.getRecords().forEach(vo -> {
            Product product = productMap.get(vo.getProductId());
            vo.setProductName(product.getName());
            vo.setImage(product.getImage());
        });

        log.info("用户 {} 购买记录查询完成，共 {} 条记录", user.getId(), page.getTotal());

        return Result.success(page);
    }

    @GetMapping("/invite-records")
    @Operation(summary = "邀请记录")
    public Result<InviteData> inviteRecords(@RequestAttribute(value = "user", required = false) User user, @ParameterObject PageQuery query) {
        // 未登录时返回结构一致的空数据
        if (user == null) {
            Page<InviteRecord> emptyPage = new Page<>(
                    Optional.ofNullable(query.getPage()).orElse(1),
                    Optional.ofNullable(query.getSize()).orElse(10)
            );
            emptyPage.setTotal(0);
            emptyPage.setRecords(java.util.Collections.emptyList());
            InviteData emptyData = new InviteData(null, null, 0, 0, null, 0, 0, emptyPage);
            return Result.success(emptyData);
        }
        int inviteCount = userService.sumChildren(user.getId());
        long teamCount = userRelationMapper.countTeam(user.getId());
        // BigDecimal inviteAmount = userService.sumParentReward(user.getId());

        LambdaQueryWrapper<User> qw = new LambdaQueryWrapper<User>().eq(User::getPid, user.getId());
        IPage<User> page = userService.page(Page.of(Optional.ofNullable(query.getPage()).orElse(1),
                Optional.ofNullable(query.getSize()).orElse(10)), qw);
        IPage<InviteRecord> data = page.convert(InviteRecord::new);

        if (!data.getRecords().isEmpty()) {
            // 查出所有下级，累计直推业绩
            UserRelation userRelation = userRelationMapper.selectById(user.getId());
            String path = Optional.ofNullable(userRelation.getPath()).orElse("/" + user.getId());
            List<UserRelationModel> userRelations = userRelationMapper.listChildren2(new LambdaQueryWrapper<UserRelation>()
                    .likeRight(UserRelation::getPath, path)
                    .gt(UserRelation::getLayer, userRelation.getLayer()));
            data.getRecords().forEach(row -> {
                for (UserRelationModel r : userRelations) {
                    if (r.getPid().equals(row.getUserId())) {
                        // 直推业绩
                        row.setInvitation(row.getInvitation() + 1);
                    }
                    if (r.getId().equals(row.getUserId())) {
                        row.setAmount(row.getAmount().add(r.getCurrentAmount()));
                    }
                    if (r.getPath().contains("/" + row.getUserId() + "/")) {
                        // 团队业绩
                        row.setTotalAmount(row.getTotalAmount().add(r.getCurrentAmount()));
                    }
                }
            });
        }

        return Result.success(new InviteData(user.getId(), user.getAddress(), user.getLevel(), user.getPreLevel(), user.getJuid(), inviteCount, teamCount, data));
    }

    @PostMapping("/bind-juid")
    @Operation(summary = "绑定交易所ID")
    public Result<?> bindJuid(@RequestAttribute(value = "user") User user, @Valid @RequestBody BindJuidForm form) {
        userService.bindJuid(user.getId(), form.getJuid());
        return Result.success(null);
    }

//    @PostMapping("/withdraw-normal-product")
//    @Operation(summary = "普通商品提取到钱包")
//    public Result<RustService.WithdrawSign> withdrawNormalProduct(
//            @RequestAttribute(value = "user") User user,
//            @Valid @RequestBody WithdrawNormalProductForm form) {
//
//        log.info("用户 {} 请求提取普通商品，订单ID：{}, 金额：{}",
//            user.getId(), form.getUserProductId(), form.getAmount());
//
//        RustService.WithdrawSign withdrawSign = userProductService.withdrawNormalProductWallet(user, form);
//        return Result.success(withdrawSign);
//    }

    @PostMapping("/withdraw")
    @Operation(summary = "统一提取接口")
    public Result<RustService.WithdrawSign> withdraw(
            @RequestAttribute(value = "user") User user,
            @Valid @RequestBody WithdrawUnifiedForm form) {

        log.info("用户 {} 请求统一提取，类型：{}, 金额：{}, 锁仓ID：{}",
            user.getId(), form.getType(), form.getAmount(), form.getId());

        try {
            BigDecimal withdrawnAmount;
            String operation;
            int withdrawType;

            // 根据type调用不同的提取逻辑
            switch (form.getType()) {
                case 0: // 提取锁仓释放
                    if (form.getId() == null) {
                        return Result.error("锁仓记录ID不能为空", null);
                    }
                    withdrawnAmount = lockOrderService.withdrawLockOrderRelease(
                        user.getId(), form.getId(), form.getAmount());
                    operation = "提取锁仓释放";
                    withdrawType = 6; // 新增的提取类型
                    break;

                case 1: // 提取质押静态池
                    withdrawnAmount = stakeUserService.withdrawStakeStaticPool(
                        user.getId(), form.getTokenId(), form.getAmount());
                    operation = "提取质押静态池";
                    withdrawType = 7; // 新增的提取类型
                    break;

                case 2: // 提取质押动态池
                    withdrawnAmount = stakeUserService.withdrawStakeDynamicPool(
                        user.getId(), form.getTokenId(), form.getAmount());
                    operation = "提取质押动态池";
                    withdrawType = 8; // 新增的提取类型
                    break;

                case 3: // 提取锁仓静态池
                    withdrawnAmount = lockOrderService.withdrawLockStaticPool(
                        user.getId(), form.getTokenId(), form.getAmount());
                    operation = "提取锁仓静态池";
                    withdrawType = 9; // 新增的提取类型
                    break;

                case 4: // 提取锁仓动态池
                    withdrawnAmount = lockOrderService.withdrawLockDynamicPool(
                        user.getId(), form.getTokenId(), form.getAmount());
                    operation = "提取锁仓动态池";
                    withdrawType = 10; // 新增的提取类型
                    break;

                default:
                    return Result.error("不支持的提取类型", null);
            }

            // 创建XYC资产信息
            Asset asset = new Asset();
            asset.setTokenId("XYC");
            asset.setTokenAddress("0x8a6556FaA0846d329D470Ce1342236ca2c6609d0");
            asset.setWithdrawAddress("0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca");

            // 生成提取签名（转换为Wei单位）
            BigDecimal amountWei = withdrawnAmount.multiply(new BigDecimal("1e18"))
                .setScale(0, RoundingMode.HALF_UP);
            RustService.WithdrawSign withdrawSign = RustService.getWithdrawSignVo(
                asset.getWithdrawAddress(),
                asset.getTokenAddress(),
                user.getAddress(),
                amountWei.toPlainString(),
                String.valueOf(withdrawType)
            );

            // 创建提取记录
            Withdraw withdraw = new Withdraw(withdrawType, user, asset,
                form.getId() != null ? form.getId().intValue() : 0,
                withdrawnAmount, BigDecimal.ZERO, withdrawSign);
            withdrawService.save(withdraw);

            log.info("用户 {} {} 成功，提取ID：{}, 金额：{}",
                user.getId(), operation, withdraw.getId(), withdrawnAmount);

            return Result.success(withdrawSign);

        } catch (Exception e) {
            log.error("用户 {} 提取失败，类型：{}, 金额：{}", user.getId(), form.getType(), form.getAmount(), e);
            return Result.error(e.getMessage(), null);
        }
    }

    @GetMapping("/withdraw-records")
    @Operation(summary = "提取记录")
    public Result<IPage<WithdrawVo>> withdrawRecords(@RequestAttribute(value = "user", required = false) User user, @ParameterObject WithdrawQuery query) {
        if (user == null) {
            return Result.success(new Page<>());
        }
        LambdaQueryWrapper<Withdraw> qw = new LambdaQueryWrapper<Withdraw>()
                .eq(Withdraw::getUserId, user.getId())
                .eq(query.getType() != null, Withdraw::getType, user.getType())
                .eq(Withdraw::getTokenId, AssetEnum.XYC.getTokenId());
        IPage<Withdraw> page = withdrawService.page(Page.of(Optional.ofNullable(query.getPage()).orElse(1),
                Optional.ofNullable(query.getSize()).orElse(10)), qw);
        IPage<WithdrawVo> data = page.convert(WithdrawVo::new);

        return Result.success(data);
    }

    @GetMapping("/user-log")
    @Operation(summary = "收益记录")
    public Result<IPage<UserLogVo>> userLog(@RequestAttribute(value = "user", required = false) User user, @ParameterObject UserLogQuery query) {
        if (user == null) {
            return Result.success(new Page<>());
        }
        LambdaQueryWrapper<UserLog> qw = new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, user.getId())
                .eq(StringUtils.isNotEmpty(query.symbol()), UserLog::getSymbol, query.symbol())
                .eq(StringUtils.isNotEmpty(query.tokenId()), UserLog::getTokenId, query.tokenId())
                .orderByDesc(UserLog::getId);
        if (query.type() != null && query.type().length > 0) {
            qw.in(UserLog::getType, Arrays.asList(query.type()));
        }
        if (query.startDate() != null || query.endDate() != null) {
            qw.between(UserLog::getCreateTime, Utils.formatBeginTime(query.startDate()), Utils.formatEndTime(query.endDate()));
        }
        IPage<UserLogVo> data = userLogService.page(Page.of(Optional.ofNullable(query.page()).orElse(1),
                        Optional.ofNullable(query.size()).orElse(10)), qw)
                .convert(UserLogVo::new);
        return Result.success(data);
    }

    @GetMapping("/stake/records")
    @Operation(summary = "质押记录")
    public Result<IPage<UserStake>> stakeRecords(@RequestAttribute(value = "user", required = false) User user, @ParameterObject PageQuery query) {
        if (user == null) {
            return Result.success(new Page<>());
        }
        LambdaQueryWrapper<UserStake> qw = new LambdaQueryWrapper<UserStake>()
                .eq(UserStake::getUserId, user.getId())
                .eq(UserStake::getTokenId, AssetEnum.XYC.getTokenId())
                .orderByDesc(UserStake::getCreateTime);
        IPage<UserStake> page = userStakeService.page(Page.of(Optional.ofNullable(query.getPage()).orElse(1),
                Optional.ofNullable(query.getSize()).orElse(10)), qw);

        return Result.success(page);
    }

    @PostMapping("/buy-city-lord")
    @Operation(summary = "抢城主接口")
    public Result<BuyCityLordResponse> buyCityLord(
            @RequestAttribute("user") User user,
            @Valid @RequestBody BuyCityLordRequest request) {
        
        log.info("用户 {} 请求抢购城主，产品ID：{}", user.getId(), request.getId());
        
        // 1. 验证产品存在且为城主类型
        Product product = productService.getById(request.getId());
        if (product == null) {
            return Result.error("城主产品不存在", null);
        }
        
        if (product.getType() != 4) {
            return Result.error("产品类型错误，必须为城主产品", null);
        }
        
        if (!Boolean.TRUE.equals(product.getEnable())) {
            return Result.error("城主产品暂未开放", null);
        }
        
        // 2. 检查时间段限制
        if (com.aic.app.util.StringUtils.isNotEmpty(product.getLimitTime())) {
            if (!isWithinTimeLimit(product.getLimitTime())) {
                return Result.error("当前不在抢购时间段内，抢购时间：" + product.getLimitTime(), null);
            }
        }
        
        // 3. 检查Redis日限制
        String dailyKey = String.format("xyc:city_lord_daily:%d", user.getId());
        if (stringRedisTemplate.hasKey(dailyKey)) {
            return Result.error("今日已抢购过城主，请明天再来", null);
        }
        
        try {
            // 4. 调用购买服务
            UserProduct userProduct = productPurchaseService.purchaseProduct(
                user, product, BigDecimal.ZERO, 1);
            
            // 5. 设置Redis标记到今天23:59:59
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59);
            long secondsUntilEndOfDay = Duration.between(now, endOfDay).getSeconds();
            stringRedisTemplate.opsForValue().set(dailyKey, "1", secondsUntilEndOfDay, TimeUnit.SECONDS);
            
            // 6. 返回结果
            BuyCityLordResponse response = new BuyCityLordResponse();
            response.setOrderId(userProduct.getId());
            response.setProductName(product.getName());
            response.setAddedQuadrupleRewardTimes(2);
            
            log.info("用户 {} 抢购城主成功，订单ID：{}", user.getId(), userProduct.getId());
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("用户 {} 抢购城主失败：{}", user.getId(), e.getMessage(), e);
            return Result.error("抢购失败：" + e.getMessage(), null);
        }
    }

    @GetMapping("/lock-order")
    @Operation(summary = "获取用户锁仓订单列表")
    public Result<IPage<LockOrderVo>> getUserLockOrders(
            @RequestAttribute("user") User user,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer sourceType) {

        LambdaQueryWrapper<LockOrder> wrapper = new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getUserId, user.getId())
                .eq(status != null, LockOrder::getStatus, status)
                .eq(sourceType != null, LockOrder::getSourceType, sourceType)
                .orderByDesc(LockOrder::getCreateTime);

        IPage<LockOrder> lockOrderPage = lockOrderService.page(new Page<>(page, size), wrapper);
        
        // 转换为VO
        IPage<LockOrderVo> result = lockOrderPage.convert(LockOrderVo::new);
        
        return Result.success(result);
    }

    @GetMapping("/bond-purchase-records")
    @Operation(summary = "获取债券购买记录")
    public Result<IPage<BondPurchaseRecordVo>> getBondPurchaseRecords(
            @RequestAttribute(value = "user", required = false) User user,
            @ParameterObject BondPurchaseQuery query) {

        // 如果用户未登录，返回空的分页数据，但结构一致
        if (user == null) {
            log.info("未登录用户查询债券购买记录，返回空数据");
            Page<BondPurchaseRecordVo> emptyPage = new Page<>(query.getPage(), query.getSize());
            emptyPage.setTotal(0);
            emptyPage.setRecords(java.util.Collections.emptyList());
            return Result.success(emptyPage);
        }

        log.info("用户 {} 查询债券购买记录，参数：{}", user.getId(), query);

        // 构建查询条件
        LambdaQueryWrapper<UserProduct> qw = new LambdaQueryWrapper<UserProduct>()
                .eq(UserProduct::getUserId, user.getId()) // 只查询当前用户的记录
                .eq(UserProduct::getType, 3) // 只查询债券产品
                .in(query.getProductId() != null && !query.getProductId().isEmpty(),
                    UserProduct::getProductId, query.getProductId())
                .orderByDesc(UserProduct::getCreateTime); // 按创建时间倒序

        // 添加时间范围查询
        if (query.getStartDate() != null || query.getEndDate() != null) {
            qw.between(UserProduct::getCreateTime,
                      Utils.formatBeginTime(query.getStartDate()),
                      Utils.formatEndTime(query.getEndDate()));
        }

        // 添加锁仓状态过滤（通过SQL条件）
        if (query.getStatus() != null && !query.getStatus().isEmpty()) {
            StringBuilder statusCondition = new StringBuilder("(lo.status IS NULL OR lo.status IN (");
            for (int i = 0; i < query.getStatus().size(); i++) {
                if (i > 0) statusCondition.append(",");
                statusCondition.append(query.getStatus().get(i));
            }
            statusCondition.append("))");
            qw.apply(statusCondition.toString());
        }

        // 分页查询债券购买记录
        IPage<BondPurchaseRecordVo> page = userProductService.pageBondPurchaseRecords(
                new Page<>(query.getPage(), query.getSize()), qw);

        log.info("用户 {} 债券购买记录查询完成，共 {} 条记录", user.getId(), page.getTotal());

        return Result.success(page);
    }

    /**
     * 检查当前时间是否在限制时间段内
     */
    private boolean isWithinTimeLimit(String limitTime) {
        try {
            String[] times = limitTime.split("-");
            if (times.length != 2) {
                return false;
            }
            
            LocalTime startTime = LocalTime.parse(times[0]);
            LocalTime endTime = LocalTime.parse(times[1]);
            LocalTime now = LocalTime.now();
            
            return !now.isBefore(startTime) && !now.isAfter(endTime);
        } catch (Exception e) {
            log.error("解析时间段限制失败：{}", limitTime, e);
            return false;
        }
    }
    
    /**
     * 抢城主请求参数
     */
    public static class BuyCityLordRequest {
        private Long id;
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
    }
    
    /**
     * 抢城主响应
     */
    public static class BuyCityLordResponse {
        private Long orderId;
        private String productName;
        private Integer addedQuadrupleRewardTimes;
        
        public Long getOrderId() { return orderId; }
        public void setOrderId(Long orderId) { this.orderId = orderId; }
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        public Integer getAddedQuadrupleRewardTimes() { return addedQuadrupleRewardTimes; }
        public void setAddedQuadrupleRewardTimes(Integer addedQuadrupleRewardTimes) { this.addedQuadrupleRewardTimes = addedQuadrupleRewardTimes; }
    }

}
