package com.aic.app.api;

import com.aic.app.exception.BizException;
import com.aic.app.exception.Errors;
import com.aic.app.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.resource.NoResourceFoundException;

@Slf4j
@ControllerAdvice
public class CustomExceptionHandler {

    @ExceptionHandler(BizException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleBizException(BizException ex) {
        return Result.error(ex.getCode(), ex.getMsg(), null);
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleException(Exception ex) {
        if (ex instanceof NoResourceFoundException) {
            return Result.error(Errors.SERVER_EXCEPTION.getCode(), Errors.SERVER_EXCEPTION.getMsg(), null);
        }
        if (ex instanceof MethodArgumentNotValidException e) {
            String msg = null;
            if (e.getBindingResult().getFieldError() != null) {
                msg = e.getBindingResult().getFieldError().getDefaultMessage();
            }
            return Result.error(Errors.REQUEST_EXCEPTION.getCode(), msg, null);
        }
        log.error("异常", ex);
        return Result.error(Errors.SERVER_EXCEPTION.getCode(), Errors.SERVER_EXCEPTION.getMsg(), null);
    }

}
