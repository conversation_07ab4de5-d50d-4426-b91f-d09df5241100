//package com.aic.app.api;
//
//import com.aic.app.model.LockOrder;
//import com.aic.app.model.User;
//import com.aic.app.service.ILockOrderService;
//import com.aic.app.vo.Result;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
//import java.math.BigDecimal;
//import java.util.List;
//
///**
// * 锁仓订单控制器
// */
//@RestController
//@RequestMapping("/api/lock-order")
//@AllArgsConstructor
//@Tag(name = "锁仓订单管理")
//public class LockOrderController {
//
//    private final ILockOrderService lockOrderService;
//
//    @GetMapping("/list")
//    @Operation(summary = "获取用户锁仓订单列表")
//    public Result<IPage<LockOrder>> getUserLockOrders(
//            @RequestAttribute("user") User user,
//            @RequestParam(defaultValue = "1") Integer page,
//            @RequestParam(defaultValue = "10") Integer size,
//            @RequestParam(required = false) Integer status,
//            @RequestParam(required = false) Integer sourceType) {
//
//        LambdaQueryWrapper<LockOrder> wrapper = new LambdaQueryWrapper<LockOrder>()
//                .eq(LockOrder::getUserId, user.getId())
//                .eq(status != null, LockOrder::getStatus, status)
//                .eq(sourceType != null, LockOrder::getSourceType, sourceType)
//                .orderByDesc(LockOrder::getCreateTime);
//
//        IPage<LockOrder> result = lockOrderService.page(new Page<>(page, size), wrapper);
//        return Result.success(result);
//    }
//
//    @GetMapping("/detail/{id}")
//    @Operation(summary = "获取锁仓订单详情")
//    public Result<LockOrder> getLockOrderDetail(
//            @RequestAttribute("user") User user,
//            @PathVariable Long id) {
//
//        LambdaQueryWrapper<LockOrder> wrapper = new LambdaQueryWrapper<LockOrder>()
//                .eq(LockOrder::getId, id)
//                .eq(LockOrder::getUserId, user.getId());
//
//        LockOrder lockOrder = lockOrderService.getOne(wrapper);
//
//        if (lockOrder == null) {
//            return Result.error("锁仓订单不存在", null);
//        }
//
//        return Result.success(lockOrder);
//    }
//
//    @GetMapping("/summary")
//    @Operation(summary = "获取用户锁仓汇总信息")
//    public Result<LockOrderSummary> getUserLockSummary(@RequestAttribute("user") User user) {
//        BigDecimal totalLockAmount = lockOrderService.getUserTotalLockAmount(user.getId(), "XYC");
//        BigDecimal availableAmount = lockOrderService.getUserAvailableAmount(user.getId(), "XYC");
//
//        // 获取各状态订单数量
//        List<LockOrder> allOrders = lockOrderService.list(
//                new LambdaQueryWrapper<LockOrder>()
//                        .eq(LockOrder::getUserId, user.getId())
//        );
//
//        long lockingCount = allOrders.stream().filter(o -> o.getStatus() == 1).count();
//        long completedCount = allOrders.stream().filter(o -> o.getStatus() == 2).count();
//
//        LockOrderSummary summary = new LockOrderSummary();
//        summary.setTotalLockAmount(totalLockAmount);
//        summary.setAvailableAmount(availableAmount);
//        summary.setLockingCount(lockingCount);
//        summary.setCompletedCount(completedCount);
//
//        return Result.success(summary);
//    }
//
//    /**
//     * 锁仓订单汇总信息
//     */
//    public static class LockOrderSummary {
//        private BigDecimal totalLockAmount;    // 总锁仓金额
//        private BigDecimal availableAmount;    // 可提取金额
//        private Long lockingCount;             // 锁仓中订单数
//        private Long completedCount;           // 已完成订单数
//
//        // getter/setter
//        public BigDecimal getTotalLockAmount() { return totalLockAmount; }
//        public void setTotalLockAmount(BigDecimal totalLockAmount) { this.totalLockAmount = totalLockAmount; }
//        public BigDecimal getAvailableAmount() { return availableAmount; }
//        public void setAvailableAmount(BigDecimal availableAmount) { this.availableAmount = availableAmount; }
//        public Long getLockingCount() { return lockingCount; }
//        public void setLockingCount(Long lockingCount) { this.lockingCount = lockingCount; }
//        public Long getCompletedCount() { return completedCount; }
//        public void setCompletedCount(Long completedCount) { this.completedCount = completedCount; }
//    }
//}