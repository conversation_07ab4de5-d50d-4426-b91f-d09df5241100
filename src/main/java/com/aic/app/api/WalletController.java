//package com.aic.app.api;
//
//import com.aic.app.exception.Errors;
//import com.aic.app.form.ClaimForm;
//import com.aic.app.form.WithdrawForm;
//import com.aic.app.model.Asset;
//import com.aic.app.model.Project;
//import com.aic.app.model.User;
//import com.aic.app.service.*;
//import com.aic.app.util.BizAssert;
//import com.aic.app.vo.Result;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.validation.Valid;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.web.bind.annotation.*;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//
//@RestController
//@RequestMapping("/api/user/wallet")
//@Tag(name = "钱包用户", description = "钱包用户相关接口")
//public class WalletController {
//
//    @Resource
//    IUserService userService;
//    @Resource
//    IStakeUserService stakeUserService;
//    @Resource
//    IProductService productService;
//    @Resource
//    IUserProductService userProductService;
//    @Resource
//    IUserLogService userLogService;
//    @Resource
//    IRewardLogService rewardLogService;
//    @Resource
//    IUserAssetService userAssetService;
//    @Resource
//    IProjectService projectService;
//    @Resource
//    StringRedisTemplate stringRedisTemplate;
//    @Resource
//    IUserStakeService userStakeService;
//    @Resource
//    IUserProjectService userProjectService;
//    @Resource
//    IWhitelistService whitelistService;
//    @Resource
//    RustService rustService;
//    @Resource
//    IAssetService assetService;
//
//    @Operation(summary = "提取静态池")
//    @PostMapping("/claim-static")
//    public Result<RustService.WithdrawSign> claimStatic(@RequestAttribute("user") User user, @Valid @RequestBody ClaimForm form) {
//        BizAssert.isTrue(user.getType() == 1, () -> Errors.ACCOUNT_EXCEPTION);
//        BizAssert.isTrue(form.getAmount() != null && form.getAmount().compareTo(BigDecimal.ZERO) > 0, "无效提取数量");
//
//        RustService.WithdrawSign data = stakeUserService.claimStaticWallet(user, form);
//        return Result.success(data);
//    }
//
//    @PostMapping("/withdraw")
//    @Operation(summary = "提取到钱包")
//    public Result<RustService.WithdrawSign> withdraw(@RequestAttribute("user") User user, @Valid @RequestBody WithdrawForm form) {
//        BizAssert.isTrue(user.getType() == 1, () -> Errors.ACCOUNT_EXCEPTION);
//        BizAssert.isTrue(form.getAmount() != null && form.getAmount().compareTo(BigDecimal.ZERO) > 0, "无效提取数量");
//
//        RustService.WithdrawSign data = stakeUserService.withdrawWallet(user, form.getTokenId(), form.getAmount());
//        return Result.success(data);
//    }
//    
//}
