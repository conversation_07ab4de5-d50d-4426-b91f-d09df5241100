package com.aic.app.config;

import java.math.BigDecimal;

/**
 * 常量定义
 */
public interface Constant {

	/**
	 * 不限制
	 */
	BigDecimal noLimit = BigDecimal.valueOf(-1);

	/**
	 * 最大限制
	 */
	BigDecimal maxLimit = BigDecimal.valueOf(1000);

	/**
	 * 订单号前缀
	 */
	String ORDER_PREFIX = "FISTAPP";

	/**
	 * 周分红人数
	 */
	int weekSize = 20;

	/**
	 * TODO 自身是有效用户且需要直推5个有效用户才能拿团队极差奖励
	 */
	long MIN_CHILD_COUNT = 5;

	/**
	 * 每天转账次数
	 */
	int transferCount = 10;

}
