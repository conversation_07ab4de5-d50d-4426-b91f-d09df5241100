package com.aic.app.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态资源配置
 */
@Configuration
public class ResourceConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置上传文件的静态资源访问
        registry.addResourceHandler("/upload/**")
                .addResourceLocations("file:upload/");
        
        // 配置profile路径的静态资源访问（兼容现有系统）
        registry.addResourceHandler("/profile/**")
                .addResourceLocations("file:profile/");
    }
}
