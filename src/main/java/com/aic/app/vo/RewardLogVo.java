package com.aic.app.vo;

import com.aic.app.model.RewardLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RewardLogVo {

    private Integer id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * code
     */
    private String code;
    /**
     * 上级用户ID
     */
    private Long parentId;
    /**
     * 等级
     */
    private int level;
    /**
     * 理财数量
     */
    private BigDecimal productAmount;
    /**
     * 返佣数量
     */
    private BigDecimal amount;
    /**
     * 创建时间
     */
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;

    private String address;
    private String symbol;
    private BigDecimal price;
    
    /**
     * 获取等级字符串
     * @return
     */
    public String getLevelStr() {
        if (level == -1 || level == -2) {
            return "直推";
        }
        return "V" + level;
    }
    
    public RewardLogVo(RewardLog rewardLog) {
        BeanUtils.copyProperties(rewardLog, this);
        this.parentId = rewardLog.getUserId();
        this.userId = rewardLog.getChildUserId();
    }
}
