package com.aic.app.vo;

import com.aic.app.model.UserAsset;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class UserAssetModel extends UserAsset {
    /**
     * 节点数量
     */
    private Integer node;
    
    /**
     * 本周业绩
     */
    private BigDecimal teamPerf;
    
    /**
     * 周分红
     */
    private BigDecimal weekDynamic;
    
    /**
     * 节点业绩
     */
    private BigDecimal nodePerf;
    
    /**
     * 节点返佣
     */
    private BigDecimal nodeReward;
    
    /**
     * 历史最大业绩
     */
    private BigDecimal maxTeamPerf;
    
    /**
     * 历史最大节点业绩
     */
    private BigDecimal maxNodePerf;

    /**
     * 节点池
     */
    private BigDecimal nodePool;

    /**
     * 活期
     */
    private BigDecimal currentAmount;

    /**
     * 待确认数量
     */
    private BigDecimal pendingAmount;

}