package com.aic.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class Result<T> {
    @Schema(description = "响应码 0:成功，非0失败")
    private int code;
    @Schema(description = "消息")
    private String msg;
    @Schema(description = "返回值")
    private T data;
    private Object total;

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> Result<T> success(T data) {
        return new Result<T>(0, "success", data);
    }

    public static <T> Result<T> success(String msg, T data) {
        return new Result<T>(0, msg, data);
    }

    public static <T> Result<T> error(T data) {
        return new Result<T>(500, "error", data);
    }

    public static <T> Result<T> error(String msg, T data) {
        return new Result<T>(500, msg, data);
    }

    public static <T> Result<T> error(int code, String msg, T data) {
        return new Result<T>(code, msg, data);
    }
    
}
