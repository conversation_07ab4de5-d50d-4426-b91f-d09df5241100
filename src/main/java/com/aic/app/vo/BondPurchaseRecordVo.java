package com.aic.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 债券购买记录VO
 */
@Data
@Schema(description = "债券购买记录")
public class BondPurchaseRecordVo {

    @Schema(description = "购买记录ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "购买时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date purchaseTime;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "折扣")
    private BigDecimal discount;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "锁仓时间(天)")
    private Integer lockDays;

    @Schema(description = "状态: 0-待激活 1-锁仓中 2-已完成 3-已取消")
    private Integer status;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "锁仓订单ID")
    private Long lockOrderId;

    @Schema(description = "购买金额")
    private BigDecimal amount;
    
    public BondPurchaseRecordVo() {}

    public BondPurchaseRecordVo(Long id, String orderNo, Date purchaseTime, String productName,
                               BigDecimal price, BigDecimal discount, Integer quantity,
                               Integer lockDays, Integer status, Long productId,
                               Long lockOrderId, BigDecimal amount) {
        this.id = id;
        this.orderNo = orderNo;
        this.purchaseTime = purchaseTime;
        this.productName = productName;
        this.price = price;
        this.discount = discount;
        this.quantity = quantity;
        this.lockDays = lockDays;
        this.status = status;
        this.productId = productId;
        this.lockOrderId = lockOrderId;
        this.amount = amount;
    }
}
