package com.aic.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 债券购买记录VO
 */
@Data
@Schema(description = "债券购买记录")
public class BondPurchaseRecordVo {
    
    @Schema(description = "购买时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date purchaseTime;
    
    @Schema(description = "产品名称")
    private String productName;
    
    @Schema(description = "价格")
    private BigDecimal price;
    
    @Schema(description = "折扣")
    private BigDecimal discount;
    
    @Schema(description = "数量")
    private Integer quantity;
    
    @Schema(description = "锁仓时间(天)")
    private Integer lockDays;
    
    @Schema(description = "状态: 0-待激活 1-锁仓中 2-已完成 3-已取消")
    private Integer status;
    
    public BondPurchaseRecordVo() {}
    
    public BondPurchaseRecordVo(Date purchaseTime, String productName, BigDecimal price, 
                               BigDecimal discount, Integer quantity, Integer lockDays, Integer status) {
        this.purchaseTime = purchaseTime;
        this.productName = productName;
        this.price = price;
        this.discount = discount;
        this.quantity = quantity;
        this.lockDays = lockDays;
        this.status = status;
    }
}
