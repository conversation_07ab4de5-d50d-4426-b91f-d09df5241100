package com.aic.app.vo;

import com.aic.app.model.StakeUser;
import com.aic.app.model.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class RankVo {
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "排名")
    private int serial;
    
    @Schema(description = "用户ID")
    private Long uid;
    
    @Schema(description = "用户邀请码")
    private String code;
    
    @Schema(description = "新增业绩")
    private BigDecimal teamPerf;

//    public RankVo(int serial, User user, StakeUser stakeUser) {
//        this.serial = serial;
//        this.uid = user.getId();
//        this.code = user.getCode();
//        this.teamPerf = stakeUser.getTeamPerf();
//    }
}
