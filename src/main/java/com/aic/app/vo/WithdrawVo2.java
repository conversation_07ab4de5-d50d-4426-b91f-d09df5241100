package com.aic.app.vo;

import com.aic.app.model.UserLog;
import com.aic.app.model.Withdraw;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class WithdrawVo2 {
    
    @Schema(description = "提现记录ID")
    Long id;

    /**
     * 类型：0.提币、 1.释放、 2.提周分红  3.提节点推荐奖励  4.提节点分红
     */
    @Schema(description = "类型：0.提币、 1.释放、 2.提周分红  3.提节点推荐奖励  4.提节点分红")
    private Integer type;

    /**
     * 随机数
     */
    @Schema(description = "随机数")
    private Long nonce;

    @Schema(description = "提现地址")
    private String address;

    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "提现金额")
    private BigDecimal quantity;

    @Schema(description = "交易号")
    private String txid;

    @Schema(description = "到期时间")
    private Long deadline;

    /**
     * 状态 0-待确认 1-审核中 2-成功 3-拒绝回退 4-取消提现
     */
    @Schema(description = "状态 0-待确认 1-审核中 2-成功 3-拒绝回退 4-取消提现")
    private Integer state;
    
    public WithdrawVo2(Withdraw withdraw) {
        this.id = withdraw.getId();
        this.createTime = withdraw.getCreateTime();
        this.quantity = withdraw.getQuantity();
        this.state = withdraw.getState();
        this.address = withdraw.getAddress();
        this.type = withdraw.getType();
        this.nonce = withdraw.getNonce();
        this.deadline = withdraw.getDeadline();
        this.txid = withdraw.getTxid();
    }

    public WithdrawVo2(UserLog userLog) {
        this.id = Long.valueOf(userLog.getId());
        this.createTime = userLog.getCreateTime();
        this.quantity = userLog.getAmount();
        this.state = 2;
    }
}
