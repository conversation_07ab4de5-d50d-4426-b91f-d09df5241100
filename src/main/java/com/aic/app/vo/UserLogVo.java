package com.aic.app.vo;

import com.aic.app.model.UserLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserLogVo {

    @Schema(description = "ID")
    private int id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "用户地址")
    private String address;
    @Schema(description = "类型：1-认购, 2-质押, 3-质押收益, 4-返佣奖励, 5-赎回质押, 6-项目空投, 7-提取收益, 8-提现手续费, 9-提现周分红, 11-划出, 12-划入, 13-空投, 14-购买节点, 15-购买节点返佣, 16-提现节点返佣, 17-提现节点池, 18-节点分红, 19-提现节点返佣失败, 20-周分红, 21-分期释放, 39-质押返佣, 45-直推10%, 46-销毁, 201-线性释放, 202-阶梯释放, 203-一次性释放, 211-静态收益, 212-动态收益, 213-复利收益, 221-锁仓订单创建, 222-锁仓订单完成, 223-锁仓订单取消, 230-购买产品, 231-购买城主商品, 232-增加4倍收益次数, 233-使用4倍收益次数")
    private int type;
    @Schema(description = "理财数量")
    private BigDecimal productAmount;
    @Schema(description = "收益/购买数量")
    private BigDecimal amount;

    @Schema(description = "参与时间: 格式 yyyy-MM-dd HH:mm:ss", example = "2024-08-06 21:20:21")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    
    private String remark;
    private String symbol;
    private String code;

    public UserLogVo(UserLog userLog) {
        BeanUtils.copyProperties(userLog, this);
    }
}
