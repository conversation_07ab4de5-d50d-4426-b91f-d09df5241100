package com.aic.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传响应对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadVo {

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "新文件名")
    private String newFileName;

    @Schema(description = "原始文件名")
    private String originalFilename;

    @Schema(description = "文件访问URL")
    private String url;

    @Schema(description = "文件大小（字节）")
    private Long size;

    /**
     * 创建上传成功的响应对象
     *
     * @param originalFilename 原始文件名
     * @param url             文件访问URL
     * @param size            文件大小
     * @return UploadVo对象
     */
    public static UploadVo success(String originalFilename, String url, Long size) {
        String fileName = url;
        String newFileName = extractFileName(url);
        return new UploadVo(fileName, newFileName, originalFilename, url, size);
    }

    /**
     * 从URL中提取文件名
     *
     * @param url 文件URL
     * @return 文件名
     */
    private static String extractFileName(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        int lastSlashIndex = url.lastIndexOf('/');
        return lastSlashIndex != -1 ? url.substring(lastSlashIndex + 1) : url;
    }
}
