package com.aic.app.vo;

import com.aic.app.model.Project;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ListProjectVo extends BaseVo {
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * LOGO
     */
    @Schema(description = "LOGO")
    private String logo;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 发放的TOKEN
     */
    @Schema(description = "发放的TOKEN")
    private String token;

    /**
     * 官网
     */
    @Schema(description = "官网")
    private String website;

    /**
     * twitter
     */
    @Schema(description = "twitter")
    private String twitter;

    /**
     * telegram
     */
    @Schema(description = "telegram")
    private String telegram;

    /**
     * 状态：0-未开始 1-已开始 2-已结束
     */
    @Schema(description = "状态：0-未开始 1-已开始 2-已结束")
    private Integer state;

    /**
     * 总量
     */
    @Schema(description = "总量")
    private BigDecimal total;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal price;

    /**
     * 已购买数量
     */
    @Schema(description = "已购买数量")
    private Integer quantity;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private long startTime;

    /**
     * 预售时间
     */
    @Schema(description = "预售时间")
    private long presaleTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private long endTime;
    
    @Schema(description = "是否白名单")
    private boolean whitelist;

    private Project.InfoData info;

    public ListProjectVo(Project project) {
        BeanUtils.copyProperties(project, this);
    }
}
