package com.aic.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 我的质押数据VO
 */
@Data
@NoArgsConstructor
@Schema(description = "我的质押数据")
public class HomeDataVo {
    
    /**
     * 全网总质押数量
     */
    @Schema(description = "全网总质押数量")
    private BigDecimal totalStakeAmount;
    
    /**
     * 当前指数
     */
    @Schema(description = "当前指数")
    private BigDecimal currentIndex;
    
    /**
     * 质押本金
     */
    @Schema(description = "质押本金")
    private BigDecimal stakePrincipal;
    
    /**
     * 总利息
     */
    @Schema(description = "总利息")
    private BigDecimal totalInterest;

    /**
     * 我的质押总量（已确认）
     */
    @Schema(description = "我的质押总量（已确认）")
    private BigDecimal myStakeAmount;

    /**
     * 待确认数量
     */
    @Schema(description = "待确认数量")
    private BigDecimal pendingAmount;

    /**
     * 质押静态池
     */
    @Schema(description = "质押静态池")
    private BigDecimal stakeStaticPool;

    /**
     * 质押动态池
     */
    @Schema(description = "质押动态池")
    private BigDecimal stakeDynamicPool;

    /**
     * 锁仓静态池
     */
    @Schema(description = "锁仓静态池")
    private BigDecimal lockStaticPool;

    /**
     * 锁仓动态池
     */
    @Schema(description = "锁仓动态池")
    private BigDecimal lockDynamicPool;

    /**
     * APY（年化收益率）
     */
    @Schema(description = "APY（年化收益率）")
    private BigDecimal apy;
    
    /**
     * 构造方法 - 暂时返回0值
     */
    public HomeDataVo(BigDecimal totalStakeAmount, BigDecimal currentIndex,
                      BigDecimal stakePrincipal, BigDecimal totalInterest,
                      BigDecimal myStakeAmount, BigDecimal pendingAmount,
                      BigDecimal stakeStaticPool, BigDecimal stakeDynamicPool,
                      BigDecimal lockStaticPool, BigDecimal lockDynamicPool,
                      BigDecimal apy) {
        this.totalStakeAmount = totalStakeAmount != null ? totalStakeAmount : BigDecimal.ZERO;
        this.currentIndex = currentIndex != null ? currentIndex : BigDecimal.ZERO;
        this.stakePrincipal = stakePrincipal != null ? stakePrincipal : BigDecimal.ZERO;
        this.totalInterest = totalInterest != null ? totalInterest : BigDecimal.ZERO;
        this.myStakeAmount = myStakeAmount != null ? myStakeAmount : BigDecimal.ZERO;
        this.pendingAmount = pendingAmount != null ? pendingAmount : BigDecimal.ZERO;
        this.stakeStaticPool = stakeStaticPool != null ? stakeStaticPool : BigDecimal.ZERO;
        this.stakeDynamicPool = stakeDynamicPool != null ? stakeDynamicPool : BigDecimal.ZERO;
        this.lockStaticPool = lockStaticPool != null ? lockStaticPool : BigDecimal.ZERO;
        this.lockDynamicPool = lockDynamicPool != null ? lockDynamicPool : BigDecimal.ZERO;
        this.apy = apy != null ? apy : BigDecimal.ZERO;
    }
    
    /**
     * 创建默认数据（全部为0）
     */
    public static HomeDataVo createDefault() {
        return new HomeDataVo(
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO
        );
    }
}
