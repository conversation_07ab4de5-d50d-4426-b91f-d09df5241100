package com.aic.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class RankData {

    @Schema(description = "排行榜数据")
    List<RankVo> list;

    @Schema(description = "可提取周分红")
    BigDecimal weekDynamic;

    @Schema(description = "预计周分红")
    BigDecimal weekDynamicEstimate;
}
