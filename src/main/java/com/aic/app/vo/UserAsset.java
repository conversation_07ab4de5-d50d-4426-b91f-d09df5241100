package com.aic.app.vo;

import com.aic.app.model.AssetEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户资产
 */
@Data
public class UserAsset {

    Map<String, BigDecimal> assets = new HashMap<>();
    
    public void setAsset(String tokenId, BigDecimal amount) {
        assets.put(tokenId, amount);
    }
    
    public BigDecimal getAsset(String tokenId) {
        return assets.get(tokenId);
    }

    /**
     * 检查是否有余额
     * @param tokenId
     * @param amount
     * @return
     */
    public boolean checkBalance(String tokenId, BigDecimal amount) {
        BigDecimal balance = BigDecimal.ZERO;
        if (AssetEnum.XYC.getTokenId().equals(tokenId)) {
            balance = assets.getOrDefault(tokenId, BigDecimal.ZERO);
        } else if (AssetEnum.of(tokenId) != null) {
            balance = assets.getOrDefault(tokenId, BigDecimal.ZERO).add(assets.getOrDefault(AssetEnum.of(tokenId + "1").getTokenId(), BigDecimal.ZERO));
        }
        return balance.compareTo(amount) >= 0;
    }
}
