package com.aic.app.vo;

import com.aic.app.model.UserAsset;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

// import org.mapstruct.Mapper;
// import org.mapstruct.MappingTarget;
// import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import org.springframework.beans.BeanUtils;

@Data
@NoArgsConstructor
public class UserAssetVo {

    private Long id;
    private Long userId;
    private String tokenId;
    @Schema(description = "logo")
    private String logo;
    @Schema(description = "余额")
    private BigDecimal balance = BigDecimal.ZERO;
    @Schema(description = "单价")
    private BigDecimal price;
    @Schema(description = "是否可以质押")
    private boolean stake;
    @Schema(description = "Token地址")
    private String tokenAddress;
    @Schema(description = "质押地址")
    private String stakeAddress;

    // private static final UserAssetMapper MAPPER = Mappers.getMapper(UserAssetMapper.class);

    public UserAssetVo(UserAsset asset) {
        // MAPPER.toVo(asset, this);
        BeanUtils.copyProperties(asset, this);
    }
}

// @Mapper
// interface UserAssetMapper {
//     void toVo(UserAsset source, @MappingTarget UserAssetVo target);
// }
