package com.aic.app.vo;

import com.aic.app.model.Asset;
import com.aic.app.model.StakeUser;
import com.aic.app.model.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class MyProductDataVo {
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "类型：0 普通用户、 1 钱包用户")
    private int type;

    @Schema(description = "BNB余额")
    private BigDecimal bnbBalance;

    @Schema(description = "JU余额")
    private BigDecimal juBalance;
    
    @Schema(description = "余额")
    private BigDecimal balance;
    
    @Schema
    private String address;
    
    @Schema(description = "tokenId")
    private String tokenId;

    @Schema(description = "质押数量(待确认)")
    BigDecimal pendingAmount = BigDecimal.ZERO;
    @Schema(description = "质押数量(已确认)")
    BigDecimal currentAmount = BigDecimal.ZERO;
    @Schema(description = "我的邀请码")
    String code;
    @Schema(description = "输入邀请码")
    String refCode;

    /**
     * 静态池子
     */
    @Schema(description = "静态池子")
    private BigDecimal staticPool = BigDecimal.ZERO;
    /**
     * 动态池子
     */
    @Schema(description = "动态池子")
    private BigDecimal dynamicPool = BigDecimal.ZERO;

    @Schema(description = "可领取奖励")
    BigDecimal canReceive = BigDecimal.ZERO;

    @Schema(description = "可领取奖励(合计)")
    BigDecimal totalCanReceive = BigDecimal.ZERO;

    @Schema(description = "团队人数")
    long teamCount;
    
    @Schema(description = "直推人数")
    long invitation;

    /**
     * 周分红
     */
    @Schema(description = "周分红")
    private BigDecimal weekDynamic = BigDecimal.ZERO;

    // 总质押
    @Schema(description = "总质押")
    private BigDecimal totalStaked;
    // 回购池
    @Schema(description = "回购池")
    private BigDecimal totalSupply;
    // 销毁池
    @Schema(description = "销毁池")
    private BigDecimal totalBurn;
    // dao池
    @Schema(description = "dao池")
    private BigDecimal totalDao;

    // 回购池BNB
    @Schema(description = "回购池BNB")
    private BigDecimal totalSupplyBnb;

    @Schema(description = "ido地址")
    private String idoAddress;
    @Schema(description = "质押地址")
    private String stakeAddress;
    @Schema(description = "token地址")
    private String tokenAddress;

    @Schema(description = "JU价格")
    private BigDecimal juPrice;
    @Schema(description = "BNB价格")
    private BigDecimal bnbPrice;
    @Schema(description = "TOKEN价格")
    private BigDecimal tokenPrice;

    @Schema(description = "产出剩余额度")
    private BigDecimal limit = BigDecimal.ZERO;

    @Schema(description = "我的级别")
    int level;

    public MyProductDataVo(User user, StakeUser stakeUser, Asset asset) {
        if (user != null) {
            this.userId = user.getId();
            this.type = user.getType();
            this.address = user.getAddress();
            this.code = user.getCode();    
            this.level = user.getLevel();
        }
        if (stakeUser != null) {
            this.pendingAmount = stakeUser.getPendingAmount();
            this.currentAmount = stakeUser.getCurrentAmount();
            this.staticPool = stakeUser.getStaticPool();
            this.dynamicPool = stakeUser.getDynamicPool();
            this.canReceive = stakeUser.getCanReceive();
        }
        this.balance = BigDecimal.ZERO;
        this.bnbBalance = BigDecimal.ZERO;
        
        this.tokenId = asset.getTokenId();
        this.totalStaked = asset.getTotalStaked();
        this.totalSupply = asset.getTotalSupply();
        this.totalBurn = asset.getTotalBurn();
        this.totalDao = asset.getTotalDao();
        // 返回70 %, 记录的是100%
        this.totalSupplyBnb = asset.getTotalSupplyBnb().multiply(new BigDecimal("0.7"));
        this.idoAddress = asset.getIdoAddress();
        this.stakeAddress = asset.getStakeAddress();
        this.tokenAddress = asset.getTokenAddress();
        
    }
}
