package com.aic.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Dashboard数据VO
 */
@Data
@NoArgsConstructor
@Schema(description = "Dashboard数据")
public class DashboardVo {
    
    /**
     * 总资产
     */
    @Schema(description = "总资产")
    private BigDecimal totalAssets;
    
    /**
     * 可用余额
     */
    @Schema(description = "可用余额")
    private BigDecimal availableBalance;
    
    /**
     * 冻结余额
     */
    @Schema(description = "冻结余额")
    private BigDecimal frozenBalance;
    
    /**
     * 质押总量
     */
    @Schema(description = "质押总量")
    private BigDecimal totalStaked;
    
    /**
     * 今日收益
     */
    @Schema(description = "今日收益")
    private BigDecimal todayEarnings;
    
    /**
     * 累计收益
     */
    @Schema(description = "累计收益")
    private BigDecimal totalEarnings;
    
    /**
     * 邀请人数
     */
    @Schema(description = "邀请人数")
    private Integer inviteCount;
    
    /**
     * 团队人数
     */
    @Schema(description = "团队人数")
    private Long teamCount;
    
    /**
     * 用户等级
     */
    @Schema(description = "用户等级")
    private Integer userLevel;
    
    /**
     * 静态池收益
     */
    @Schema(description = "静态池收益")
    private BigDecimal staticPoolEarnings;
    
    /**
     * 动态池收益
     */
    @Schema(description = "动态池收益")
    private BigDecimal dynamicPoolEarnings;
    
    /**
     * 创建默认数据（全部为0）
     */
    public static DashboardVo createDefault() {
        DashboardVo vo = new DashboardVo();
        vo.totalAssets = BigDecimal.ZERO;
        vo.availableBalance = BigDecimal.ZERO;
        vo.frozenBalance = BigDecimal.ZERO;
        vo.totalStaked = BigDecimal.ZERO;
        vo.todayEarnings = BigDecimal.ZERO;
        vo.totalEarnings = BigDecimal.ZERO;
        vo.inviteCount = 0;
        vo.teamCount = 0L;
        vo.userLevel = 0;
        vo.staticPoolEarnings = BigDecimal.ZERO;
        vo.dynamicPoolEarnings = BigDecimal.ZERO;
        return vo;
    }
}
