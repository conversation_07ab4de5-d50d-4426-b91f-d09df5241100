package com.aic.app.vo;

import com.aic.app.model.UserProduct;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class UserProductVo {

    @Schema(description = "ID")
    private Integer id;
    @Schema(description = "订单号")
    private String orderNo;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "产品ID")
    private Long productId;
    @Schema(description = "产品名称")
    private String productName;
    @Schema(description = "数量")
    private Integer quantity;
    @Schema(description = "金额")
    private BigDecimal amount;
    @Schema(description = "图片")
    private String image;
    @Schema(description = "价格")
    private BigDecimal price;
    
    @Schema(description = "状态：0-待发货，1-待收货，2-已完成")
    private int status;
    @Schema(description = "购买时间")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;

    private String address;
    
    public UserProductVo(UserProduct userProduct) {
        BeanUtils.copyProperties(userProduct, this);
    }

}
