package com.aic.app.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class StakeChildVo {

    /**
     * 累计静态
     */
    private BigDecimal totalStatic = BigDecimal.ZERO;

    /**
     * 累计动态
     */
    private BigDecimal totalDynamic = BigDecimal.ZERO;

    /**
     * 累计静态
     */
    private BigDecimal sumTotalStatic = BigDecimal.ZERO;

    /**
     * 累计动态
     */
    private BigDecimal sumTotalDynamic = BigDecimal.ZERO;
}
