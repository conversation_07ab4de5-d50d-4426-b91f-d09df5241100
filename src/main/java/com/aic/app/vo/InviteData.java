package com.aic.app.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InviteData {

    @Schema(description = "用户ID")
    Long userId;

    @Schema(description = "用户地址")
    String address;

    @Schema(description = "自身等级")
    int level;

    @Schema(description = "预售等级")
    int preLevel;

    @Schema(description = "绑定交易所的UID")
    String juid;
    
    @Schema(description = "直推人数")
    int inviteCount;

    @Schema(description = "伞下人数")
    long teamCount;
    
    @Schema(description = "分页数据")
    private IPage<InviteRecord> data;
}