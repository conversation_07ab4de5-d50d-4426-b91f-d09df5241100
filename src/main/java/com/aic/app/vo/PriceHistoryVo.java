package com.aic.app.vo;

import com.aic.app.model.PriceHistory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PriceHistoryVo {

    /**
     * TOKEN
     */
    @Schema(description = "TOKEN")
    private String tokenId;
    
    @Schema(description = "价格列表")
    private List<Data> list;
    
    @lombok.Data
    public static class Data {
        /**
         * 价格
         */
        @Schema(description = "价格")
        private BigDecimal price;

        /**
         * 时间戳
         */
        @Schema(description = "时间戳")
        private Long time;
        
        public Data(PriceHistory model) {
            this.price = model.getPrice();
            this.time = model.getTime();
        }
    }

    public PriceHistoryVo(String tokenId, List<PriceHistory> list) {
        this.tokenId = tokenId;
        this.list = list.stream().map(PriceHistoryVo.Data::new).toList();
    }
}
