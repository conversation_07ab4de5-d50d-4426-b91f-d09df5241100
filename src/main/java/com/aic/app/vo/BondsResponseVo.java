package com.aic.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BondsResponseVo {
    
    @Schema(description = "已售债券数量")
    private BigDecimal soldBonds;
    
    @Schema(description = "XYC价格")
    private BigDecimal xycPrice;
    
    @Schema(description = "债券列表")
    private List<ProductVo> bondList;
}