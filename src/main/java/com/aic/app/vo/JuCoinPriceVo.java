package com.aic.app.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

// {"biz":"spot","code":200,"msg":"SUCCESS","msgInfo":[],"data":[{"s":"aic_usdt","t":1736848151953,"p":"5.02588"}]}

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class JuCoinPriceVo {
    int code;
    String msg;
    List<PriceVo> data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class PriceVo {
        String s;
        long t;
        BigDecimal p;
    }

    public BigDecimal getPrice(String symbol) {
        if (data == null || symbol == null) {
            return BigDecimal.ZERO;
        }
        for (PriceVo vo : data) {
            if (symbol.equals(vo.s)) {
                return vo.p;
            }
        }
        return BigDecimal.ZERO;
    }
}
