package com.aic.app.vo;

import com.aic.app.model.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserInfoVo {
    @Schema(description = "是否登录")
    boolean isLogin;
    @Schema(description = "用户id")
    Long userId;
//    @Schema(description = "用户类型")
//    int type;
//    @Schema(description = "邀请码")
//    String code;
    @Schema(description = "钱包地址")
    String address;
    @Schema(description = "上级id")
    Long pid;
    
    public UserInfoVo(User user) {
        if (user != null) {
            this.isLogin = true;
            this.userId = user.getId();
//            this.type = user.getType();
//            this.code = user.getCode();
            this.address = user.getAddress();
            this.pid = user.getPid();
        }
    }
}
