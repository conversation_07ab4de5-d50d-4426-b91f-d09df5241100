package com.aic.app.vo;

import com.aic.app.model.User;
import com.aic.app.model.UserProduct;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InviteRecord {
    
    @Schema(description = "UID")
    Long userId;
    
    @Schema(description = "用户地址")
    String address;
    
    @Schema(description = "邀请人数")
    long invitation;

    @Schema(description = "等级")
    int level;

    @Schema(description = "预售等级")
    int preLevel;
    
    @Schema(description = "质押数量")
    BigDecimal amount;

    @Schema(description = "伞下质押数量")
    BigDecimal totalAmount;

    public InviteRecord(User user) {
        this.userId = user.getId();
        this.address = user.getAddress();
        this.level = user.getLevel();
        this.preLevel = user.getPreLevel();
        this.amount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }

    public InviteRecord(UserProduct userProduct) {
        this.userId = userProduct.getUserId();
        this.amount = userProduct.getAmount();
    }
}
