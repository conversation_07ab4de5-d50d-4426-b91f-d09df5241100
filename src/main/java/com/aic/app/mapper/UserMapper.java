package com.aic.app.mapper;

import com.aic.app.model.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    @Insert("insert into user(id, code) value (#{u.id}, #{u.code})")
    int insertUser(@Param("u") User user);

    @Update("update user set pid = #{u.pid}, code = #{u.code} where id = #{u.id}")
    int updatePid(@Param("u") User user);

    @Select("select ifnull(max(id), 0) from user")
    long maxUserId();

    @Select("select count(*) from user where pid = #{userId}")
    int sumChildren(@Param("userId")  Long id);

    @Select("select IFNULL(sum(b.amount), 0) from user_log b where b.user_id=#{userId} and b.type=3")
    BigDecimal sumParentReward(@Param("userId") Long userId);

    @Update("update user set quadruple_reward_times = quadruple_reward_times + #{times} where id = #{userId}")
    int addQuadrupleRewardTimes(@Param("userId") Long userId, @Param("times") int times);

    @Update("update user set quadruple_reward_times = quadruple_reward_times - #{times} where id = #{userId} and quadruple_reward_times >= #{times}")
    int useQuadrupleRewardTimes(@Param("userId") Long userId, @Param("times") int times);

    @Select("select quadruple_reward_times from user where id = #{userId}")
    Integer getQuadrupleRewardTimes(@Param("userId") Long userId);
}
