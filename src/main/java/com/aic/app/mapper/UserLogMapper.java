package com.aic.app.mapper;

import com.aic.app.model.RewardData;
import com.aic.app.model.UserLog;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserLogMapper extends BaseMapper<UserLog> {
    @Select("""
        <script>
            select 
                a.*,c.code,c.address
            from
                user_log a 
                left join (select id,code,address from user) c on a.user_id = c.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    <E extends IPage<UserLog>> E page(E page, @Param(Constants.WRAPPER) Wrapper<UserLog> queryWrapper);

    @Select("""
        select ifnull(sum(amount), 0) from user_log where type in (3,4) and create_time >= #{date}
    """)
    BigDecimal getTodayAmount(@Param("date") Date date);


    @Select("""
        select abs(ifnull(sum(a.amount), 0)) from user_log a, user b where a.user_id=b.id and a.type = #{type} and b.type=0 and a.create_time >= '2025-05-12 15:23'
    """)
    BigDecimal getAmount(@Param("date") LocalDate date, @Param("type") int type);
    
    @Select("""
        select date_format(create_time, '%Y-%m-%d') as day,
               sum(if(type = 3, amount, 0))         as static_amount,
               sum(if(type in (39,45), amount, 0))        as dynamic_amount
        from user_log
        where type in (3, 39, 45)
        group by day
        order by day desc
    """)
    IPage<RewardData> pageRewardData(IPage<RewardData> page);

    @Select("""
        select ifnull(sum(amount), 0) from user_log
        where type in (3, 4, 15, 18, 20, 39, 45, 211, 212, 213)
    """)
    BigDecimal getTotalInterest();
}
