package com.aic.app.mapper;

import com.aic.app.model.Product;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 统计指定类型产品的已售总数
     * @param type 产品类型
     * @return 已售总数
     */
    @Select("SELECT IFNULL(SUM(sold), 0) FROM product WHERE type = #{type}")
    BigDecimal selectSoldSumByType(@Param("type") Integer type);
}
