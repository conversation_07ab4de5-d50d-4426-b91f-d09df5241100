package com.aic.app.mapper;

import com.aic.app.model.StakeUserModel;
import com.aic.app.model.UserProduct;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface UserProductMapper extends BaseMapper<UserProduct> {
    
    @Select("""
        <script>
            select 
                a.*,c.code,c.address,d.name as product_name
            from
                user_product a 
                inner join (select id,code,address from user) c on a.user_id = c.id
                inner join (select id,name from product) d on a.product_id = d.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    <E extends IPage<UserProduct>> E page(E page, @Param(Constants.WRAPPER) Wrapper<UserProduct> queryWrapper);

    @Select("""
        <script>
            select a.*
            from user_product a 
            inner join user b on a.user_id = b.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    IPage<UserProduct> pageChildOrders(Page<UserProduct> page, @Param(Constants.WRAPPER) Wrapper<UserProduct> qw);
}
