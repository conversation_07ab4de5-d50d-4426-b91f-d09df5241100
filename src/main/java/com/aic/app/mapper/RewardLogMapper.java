package com.aic.app.mapper;

import com.aic.app.model.RewardLog;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface RewardLogMapper extends BaseMapper<RewardLog> {
    
    @Select("select 0")
    BigDecimal sumParentAmount(@Param("userId") Long userId);

    @Select("""
        <script>
            select 
                a.*,c.code,c.address
            from
                reward_log a 
                left join (select id,code,address from user) c on a.user_id = c.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    <E extends IPage<RewardLog>> E page(E page, @Param(Constants.WRAPPER) Wrapper<RewardLog> queryWrapper);
}
