package com.aic.app.mapper;

import com.aic.app.model.UserStake;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【user_stake(用户理财产品表)】的数据库操作Mapper
* @createDate 2025-03-13 22:20:50
* @Entity com.aic.app.model.UserStake
*/
public interface UserStakeMapper extends BaseMapper<UserStake> {

    @Select("""
        <script>
            select 
                a.*,c.code,c.address
            from
                user_stake a 
                left join (select id,code,address from user) c on a.user_id = c.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    <E extends IPage<UserStake>> E page(E page, @Param(Constants.WRAPPER) Wrapper<UserStake> queryWrapper);

    /**
     * 查询用户伞下所有用户在指定时间段内的已确认质押总额
     */
    @org.apache.ibatis.annotations.Select({
        "<script>",
        "SELECT IFNULL(SUM(if(a.type=0,a.quantity,-a.quantity)),0) FROM user_stake a",
        "LEFT JOIN user_relation b ON a.user_id = b.id",
        "WHERE b.path LIKE #{path}",
        "<if test='beginTime != null'>AND a.create_time &gt;= #{beginTime}</if>",
        "<if test='endTime != null'>AND a.create_time &lt;= #{endTime}</if>",
        "</script>"
    })
    java.math.BigDecimal getStakeSum(@org.apache.ibatis.annotations.Param("path") String path,
                                     @org.apache.ibatis.annotations.Param("beginTime") java.util.Date beginTime,
                                     @org.apache.ibatis.annotations.Param("endTime") java.util.Date endTime);
}




