package com.aic.app.mapper;

import com.aic.app.model.LockOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 锁仓订单Mapper
 */
@Mapper
public interface LockOrderMapper extends BaseMapper<LockOrder> {
    
    /**
     * 获取用户锁仓总额
     */
    @Select("SELECT COALESCE(SUM(lock_amount), 0) FROM lock_order WHERE user_id = #{userId} AND token_id = #{tokenId} AND status = 1")
    BigDecimal getUserTotalLockAmount(@Param("userId") Long userId, @Param("tokenId") String tokenId);
    
    /**
     * 获取用户可提取金额
     */
    @Select("SELECT COALESCE(SUM(available_amount), 0) FROM lock_order WHERE user_id = #{userId} AND token_id = #{tokenId} AND status = 1")
    BigDecimal getUserAvailableAmount(@Param("userId") Long userId, @Param("tokenId") String tokenId);
    
    /**
     * 获取需要线性释放的订单
     */
    @Select("SELECT * FROM lock_order WHERE status = 1 AND released_days < total_days")
    List<LockOrder> getLinearReleaseOrders();
    
    /**
     * 获取启用静态收益的订单
     */
    @Select("SELECT * FROM lock_order WHERE status = 1 AND enable_static = 1")
    List<LockOrder> getStaticRewardOrders();
    
    /**
     * 获取启用动态收益的订单
     */
    @Select("SELECT * FROM lock_order WHERE status = 1 AND enable_dynamic = 1")
    List<LockOrder> getDynamicRewardOrders();
    
    /**
     * Get total lock amount for specific token
     */
    @Select("SELECT COALESCE(SUM(lock_amount), 0) FROM lock_order WHERE token_id = #{tokenId} AND status = 1")
    BigDecimal getTotalLockAmount(@Param("tokenId") String tokenId);
}