package com.aic.app.mapper;

import com.aic.app.model.User;
import com.aic.app.model.UserRelation;
import com.aic.app.model.UserRelationModel;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface UserRelationMapper extends BaseMapper<UserRelation> {

    @Select("select a.id,a.pid from user_relation a where a.pid is not null and a.path is null")
    List<UserRelation> findUnSyncUsers();

//    @Select("select a.id,a.pid from user_relation a where a.pid is not null and a.path is null limit 1")
//    UserRelation findUnSyncUser();
//
//    @Select("select a.id,a.pid,a.path,b.level,b.active,b.phone from user_relation a left join user b on b.id=a.id where a.id=#{id}")
//    PhoneRelationVo findUser(@Param("id") String id);
//
//    @Select("<script>select a.id,a.pid,a.path,b.level,b.active,b.phone from user_relation a left join user b on b.id=a.id ${ew.customSqlSegment}</script>")
//    List<PhoneRelationVo> listChildUsers(@Param(Constants.WRAPPER) Wrapper<UserRelation> wrapper);
//
//    @Select("<script>select IFNULL(sum(b.quantity), 0) from user_relation a inner join (select user_id, SUM(quantity) as quantity from user_order where 1=1 <if test='beginTime!=null and endTime!=null'> and create_time between #{beginTime} and #{endTime}</if> group by user_id) b on b.user_id=a.id ${ew.customSqlSegment}</script>")
//    int sumChildUsers(@Param(Constants.WRAPPER) Wrapper<UserRelation> wrapper, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    @Insert("insert into user_relation(id,pid) select a.id,a.pid from user a left join user_relation b on b.id = a.id where b.id is null")
    int syncUsers();

    @Select("select a.id,a.pid from user a inner join user_relation b on b.id = a.id where IFNULL(a.pid,'') != IFNULL(b.pid, '')")
    List<User> findUnSyncUsers2();

    @Select("select count(1) from user_relation where pid=#{id}")
    long countChild(@Param("id") Long id);

    @Select("select count(1) from user_relation where path like CONCAT('%/', #{id}, '%')")
    long countTeam(@Param("id") Long id);

    @Select("select a.*, (b.pending_amount+b.current_amount) as current_amount, b.sum_amount, b.team_perf from user_relation a inner join stake_user b on a.id=b.user_id and b.token_id = #{tokenId} ${ew.customSqlSegment}")
    List<UserRelationModel> listChildren(@Param(Constants.WRAPPER) Wrapper<UserRelation> gt, @Param("tokenId") String tokenId);

    @Select("select a.*, (b.amount) as current_amount from user_relation a inner join (select user_id, sum(amount) as amount from user_product group by user_id) b on a.id=b.user_id ${ew.customSqlSegment}")
    List<UserRelationModel> listChildren2(@Param(Constants.WRAPPER) Wrapper<UserRelation> gt);
}




