package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AddressForm {
    
    @Schema(description = "ID")
    private int id;
    @Schema(description = "联系人")
    private String contact;
    @Schema(description = "区号")
    private String area;
    @Schema(description = "联系电话")
    private String phone;
    @Schema(description = "地址")
    private String address;
}
