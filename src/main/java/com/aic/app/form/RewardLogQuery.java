package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public record RewardLogQuery(
        @DateTimeFormat(
                pattern = "yyyy-MM-dd"
        )
        @Schema(description = "开始日期") Date startDate,
        @DateTimeFormat(
                pattern = "yyyy-MM-dd"
        )
        @Schema(description = "结束日期") Date endDate,
        @Schema(description = "页数") Integer page,
        @Schema(description = "每页页数：默认10") Integer size) {
}
