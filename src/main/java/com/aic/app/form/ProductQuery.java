package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ProductQuery extends PageQuery {

    @Schema(description = "产品类型列表，1-预售节点 2-普通商品 3-债券 4-城主")
    private List<Integer> types;

    @Schema(description = "产品名称，支持模糊查询")
    private String name;

    @Schema(description = "排序字段，支持：id、createTime、price", example = "createTime")
    private String prop;

    @Schema(description = "排序方式，ascending-升序，descending-降序", example = "descending")
    private String order;

}
