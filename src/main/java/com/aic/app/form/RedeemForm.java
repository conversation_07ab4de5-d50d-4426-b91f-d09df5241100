package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;

public record RedeemForm(@Schema(description = "产品ID：获取不需要传ID，定期传我的理财产品ID")
                         int id,
                         @Positive(message = "数量必现大于0") @Schema(description = "赎回数量：活期需要传数量，定期不需要传")
                         BigDecimal amount) {

}
