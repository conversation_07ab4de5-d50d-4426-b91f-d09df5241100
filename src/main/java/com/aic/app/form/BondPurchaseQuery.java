package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 债券购买记录查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BondPurchaseQuery extends PageQuery {
    
    @Schema(description = "锁仓状态列表，0-待激活 1-锁仓中 2-已完成")
    private List<Integer> status;
    
    @Schema(description = "产品ID列表")
    private List<Long> productId;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期")
    private Date startDate;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期")
    private Date endDate;
}
