package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ClaimForm {
    @Schema(description = "Token ID") 
    String tokenId;
    @Schema(description = "产品ID：2-30天 3-60天 4-90天 5-180天")
    Long productId;
    @Schema(description = "理财数量")
    @Positive(message = "数量必须大于0")
    BigDecimal amount;
}
