package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class WithdrawNormalProductForm {

    @Schema(description = "普通商品订单ID", required = true)
    @NotNull(message = "订单ID不能为空")
    private Long userProductId;

    @Schema(description = "提取金额", required = true)
    @NotNull(message = "提取金额不能为空")
    private BigDecimal amount;

    @Schema(description = "TOKEN ID", example = "XYC")
    private String tokenId = "XYC";
}
