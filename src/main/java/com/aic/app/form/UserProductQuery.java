package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserProductQuery extends PageQuery {
    
    @Schema(description = "产品类型列表，1-预售节点 2-普通商品 3-债券 4-承租")
    private List<Integer> types;
    
    @Schema(description = "状态列表，0-待确认 1-有效 2-已到期 3-已赎回 4-已复投 5-已结束")
    private List<Integer> status;
    
    @Schema(description = "产品ID列表")
    private List<Integer> productId;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "开始日期")
    private Date startDate;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束日期")
    private Date endDate;
}
