package com.aic.app.form;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PageQuery {

    @Schema(description = "页数", example = "1")
    Integer page;
    @Schema(description = "每页页数：默认10", example = "10")
    Integer size;
    
    @Hidden
    String prop;
    @Hidden
    String order;
    
    public String getProp() {
        if (prop == null) {
            return null;
        }
        // 驼峰转下划线
        return prop.replaceAll("[A-Z]", "_$0").toLowerCase();
    }

    public Integer getPage() {
        if (page == null || page < 1) {
            return 1;
        }
        return page;
    }

    public Integer getSize() {
        if (size == null || size < 1) {
            return 10;
        }
        return size;
    }
}
