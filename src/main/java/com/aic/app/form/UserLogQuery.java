package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public record UserLogQuery(@Schema(description = "类型：1-认购, 2-质押, 3-质押收益, 4-返佣奖励, 5-赎回质押, 6-项目空投, 7-提取收益, 8-提现手续费, 9-提现周分红, 11-划出, 12-划入, 13-空投, 14-购买节点, 15-购买节点返佣, 16-提现节点返佣, 17-提现节点池, 18-节点分红, 19-提现节点返佣失败, 20-周分红, 21-分期释放, 39-质押返佣, 45-直推10%, 46-销毁, 201-线性释放, 202-阶梯释放, 203-一次性释放, 211-静态收益, 212-动态收益, 213-复利收益, 221-锁仓订单创建, 222-锁仓订单完成, 223-锁仓订单取消, 230-购买产品, 231-购买城主商品, 232-增加4倍收益次数, 233-使用4倍收益次数") Integer[] type,
                           @DateTimeFormat(
                                   pattern = "yyyy-MM-dd"
                           )
                           @Schema(description = "开始日期") Date startDate,
                           @DateTimeFormat(
                                   pattern = "yyyy-MM-dd"
                           )
                           @Schema(description = "结束日期") Date endDate,
                           @Schema(description = "页数") Integer page,
                           @Schema(description = "tokenId") String tokenId,
                           @Schema(description = "symbol") String symbol,
                           @Schema(description = "每页页数：默认10") Integer size) {
}
