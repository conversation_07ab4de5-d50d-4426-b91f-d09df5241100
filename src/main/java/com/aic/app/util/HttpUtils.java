package com.aic.app.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Map;

@Slf4j
public final class HttpUtils {

    public static final HttpClient httpClient = newHttpClient();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static HttpClient newHttpClient() {
        HttpClient.Builder builder = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(5));
        if (Utils.isDev()) {
//            builder = builder.proxy(java.net.ProxySelector.of(new java.net.InetSocketAddress("127.0.0.1", 1087)));
        }
        return builder
                .build();
    }
    
    public static <T> T get(String url, Class<T> clazz) {
        try {
            log.debug("[http] GET url = {}", url);
            String body = httpClient.send(HttpRequest.newBuilder().GET().uri(URI.create(url)).build(),
                    HttpResponse.BodyHandlers.ofString()).body();
            if (Utils.isDev()) {
                log.debug(body);
            }
            return objectMapper.readValue(body, clazz);   
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static <T> T postJson(String url, Map<String, Object> data, Class<T> clazz) {
        try {
            log.debug("[http] POST url = {} data = {}", url, data);
            String requestData = objectMapper.writeValueAsString(data);
            String body = httpClient.send(HttpRequest.newBuilder().POST(HttpRequest.BodyPublishers.ofString(requestData))
                            .header("Content-Type", "application/json")
                            .uri(URI.create(url)).build(),
                    HttpResponse.BodyHandlers.ofString()).body();
            if (Utils.isDev()) {
                log.debug(body);
            }
            return objectMapper.readValue(body, clazz);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    
}
