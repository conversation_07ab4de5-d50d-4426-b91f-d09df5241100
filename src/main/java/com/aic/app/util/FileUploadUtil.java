package com.aic.app.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传工具类
 */
@Slf4j
public class FileUploadUtil {

    /**
     * 默认上传路径
     */
    private static final String DEFAULT_BASE_DIR = "upload";

    /**
     * 允许的图片文件扩展名
     */
    private static final List<String> ALLOWED_IMAGE_EXTENSIONS = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp"
    );

    /**
     * 最大文件大小 (10MB)
     */
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return 文件相对路径
     * @throws IOException 上传失败时抛出异常
     */
    public static String upload(MultipartFile file) throws IOException {
        return upload(DEFAULT_BASE_DIR, file);
    }

    /**
     * 上传文件到指定目录
     *
     * @param baseDir 基础目录
     * @param file    上传的文件
     * @return 文件相对路径
     * @throws IOException 上传失败时抛出异常
     */
    public static String upload(String baseDir, MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("上传文件不能为空");
        }

        // 验证文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IOException("文件大小不能超过10MB");
        }

        // 获取原始文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IOException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename);
        if (!ALLOWED_IMAGE_EXTENSIONS.contains(extension.toLowerCase())) {
            throw new IOException("不支持的文件类型，仅支持: " + String.join(", ", ALLOWED_IMAGE_EXTENSIONS));
        }

        // 生成新的文件名
        String newFileName = generateFileName(extension);

        // 创建日期目录结构 (yyyy/MM/dd)
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String relativePath = baseDir + "/" + datePath + "/" + newFileName;

        // 获取应用根目录的绝对路径
        String userDir = System.getProperty("user.dir");
        Path absoluteUploadPath = Paths.get(userDir, relativePath);
        Path parentDir = absoluteUploadPath.getParent();

        // 创建目录
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }

        // 保存文件
        file.transferTo(absoluteUploadPath.toFile());

        log.info("文件上传成功: {} -> {} (绝对路径: {})", originalFilename, relativePath, absoluteUploadPath.toString());

        return "/" + relativePath.replace("\\", "/");
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名（不包含点）
     */
    private static String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 生成唯一文件名
     *
     * @param extension 文件扩展名
     * @return 新文件名
     */
    private static String generateFileName(String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return timestamp + "_" + uuid + "." + extension;
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            // 移除开头的斜杠
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

            // 获取应用根目录的绝对路径
            String userDir = System.getProperty("user.dir");
            Path absolutePath = Paths.get(userDir, cleanPath);

            if (Files.exists(absolutePath)) {
                Files.delete(absolutePath);
                log.info("文件删除成功: {} (绝对路径: {})", filePath, absolutePath.toString());
                return true;
            } else {
                log.warn("文件不存在: {} (绝对路径: {})", filePath, absolutePath.toString());
                return false;
            }
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean fileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;
            return Files.exists(Paths.get(cleanPath));
        } catch (Exception e) {
            log.error("检查文件是否存在时出错: {}", filePath, e);
            return false;
        }
    }
}
