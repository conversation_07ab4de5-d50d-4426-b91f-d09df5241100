package com.aic.app.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Date;

// 自定义Base32实现，因为Google Authenticator需要Base32编码
class Base32 {
    private static final String BASE32_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    public String encode(byte[] data) {
        StringBuilder result = new StringBuilder();
        int bits = 0;
        int value = 0;

        for (byte b : data) {
            value = (value << 8) | (b & 0xff);
            bits += 8;

            while (bits >= 5) {
                bits -= 5;
                result.append(BASE32_CHARS.charAt((value >> bits) & 0x1f));
            }
        }

        if (bits > 0) {
            result.append(BASE32_CHARS.charAt((value << (5 - bits)) & 0x1f));
        }

        return result.toString();
    }

    public byte[] decode(String base32) {
        base32 = base32.toUpperCase().replaceAll("[^A-Z2-7]", "");
        byte[] result = new byte[base32.length() * 5 / 8];
        int buffer = 0;
        int bitsLeft = 0;
        int resultIndex = 0;

        for (char c : base32.toCharArray()) {
            int value = BASE32_CHARS.indexOf(c);
            if (value < 0) {
                continue;
            }

            buffer = (buffer << 5) | value;
            bitsLeft += 5;

            if (bitsLeft >= 8) {
                bitsLeft -= 8;
                result[resultIndex++] = (byte) (buffer >> bitsLeft);
            }
        }

        return result;
    }
}

/**
 * Google Authenticator 工具类
 */
public class GoogleAuthenticator {

    /**
     * 时间前后偏移量，用于防止客户端时间不精确导致验证失败
     */
    private static final int WINDOW_SIZE = 1;

    /**
     * 生成密钥
     * @return 密钥
     */
    public static String generateSecretKey() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[10]; // 10字节生成16个字符的Base32密钥
        random.nextBytes(bytes);
        Base32 base32 = new Base32();
        return base32.encode(bytes);
    }

    /**
     * 生成二维码内容
     * @param user 用户名
     * @param host 域名
     * @param secret 密钥
     * @return 二维码内容
     */
    public static String getQRBarcodeURL(String user, String host, String secret) {
        String format = "otpauth://totp/%s:%s?secret=%s&issuer=%s";
        return String.format(format, host, user, secret, host);
    }

    /**
     * 验证 Google Authenticator 验证码
     * @param secret 密钥
     * @param code 验证码
     * @return 验证结果
     */
    public static boolean verify(String secret, String code) {
        if (code == null || code.length() != 6) {
            return false;
        }

        try {
            // 使用Base32解码密钥
            Base32 base32 = new Base32();
            byte[] decodedKey = base32.decode(secret);

            // 当前时间
            long currentTime = new Date().getTime() / 1000 / 30;

            // 验证当前时间及前后偏移量的验证码
            for (int i = -WINDOW_SIZE; i <= WINDOW_SIZE; ++i) {
                String hash = calculateCode(decodedKey, currentTime + i);
                if (hash.equals(code)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 根据密钥和时间计算验证码
     * @param key 密钥
     * @param time 时间
     * @return 验证码
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    private static String calculateCode(byte[] key, long time) throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] data = new byte[8];
        long value = time;
        for (int i = 8; i-- > 0; value >>>= 8) {
            data[i] = (byte) value;
        }

        try {
            SecretKeySpec signKey = new SecretKeySpec(key, "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signKey);
            byte[] hash = mac.doFinal(data);
            return processHash(hash);
        } catch (Exception e) {
            e.printStackTrace();
            return "000000";
        }
    }

    /**
     * 处理哈希值，生成6位验证码
     * @param hash 哈希值
     * @return 6位验证码
     */
    private static String processHash(byte[] hash) {
        int offset = hash[hash.length - 1] & 0xF;
        long truncatedHash = 0;
        for (int i = 0; i < 4; ++i) {
            truncatedHash <<= 8;
            truncatedHash |= (hash[offset + i] & 0xFF);
        }

        truncatedHash &= 0x7FFFFFFF;
        truncatedHash %= 1000000;

        return String.format("%06d", truncatedHash);
    }

    public static void main(String[] args) {
//        String secretKey = generateSecretKey();
//        System.out.println(secretKey);
//        String qrCodeContent = getQRBarcodeURL("admin", "dop-app", secretKey);
//        System.out.println(qrCodeContent);
        // 验证 4L2DZCZRN7EUFUAL code
        System.out.println(verify("4L2DZCZRN7EUFUAL", "925235"));
    }
}
