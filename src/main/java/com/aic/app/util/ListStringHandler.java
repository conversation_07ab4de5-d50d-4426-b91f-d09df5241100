package com.aic.app.util;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
public class ListStringHandler extends AbstractJsonTypeHandler<List<String>> {
    
    private final ObjectMapper om = new ObjectMapper();
    
    @Override
    protected List<String> parse(String json) {
        try {
            return om.readValue(json, new TypeReference<>(){});
        } catch (JsonProcessingException e) {
            log.error("序列化异常", e);
        }
        return Collections.emptyList();
    }

    @Override
    protected String toJson(List<String> obj) {
        try {
            return om.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("序列化异常", e);
        }
        return "[]";
    }

}
