package com.aic.app.util;

import com.aic.app.exception.BizException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Supplier;

public final class BizAssert {

    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new BizException(400, message);
        }
    }

    public static void isFalse(boolean expression, Supplier<BizException> callback) {
        if (expression) {
            throw callback.get();
        }
    }
    
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new BizException(400, message);
        }
    }
    
    public static void isTrue(boolean expression, Supplier<BizException> callback) {
        if (!expression) {
            throw callback.get();
        }
    }

    public static void isEmpty(Object obj, Supplier<BizException> callback) {
        if (ObjectUtils.isNotEmpty(obj)) {
            throw callback.get();
        }
    }

    public static void isNotEmpty(String obj, Supplier<BizException> callback) {
        if (StringUtils.isEmpty(obj)) {
            throw callback.get();
        }
    }

    public static void notEmpty(Object obj, Supplier<BizException> callback) {
        if (ObjectUtils.isEmpty(obj)) {
            throw callback.get();
        }
    }

    public static void notEmpty(Object obj, String message) {
        if (ObjectUtils.isEmpty(obj)) {
            throw new BizException(400, message);
        }
    }

    public static void isNull(Object obj, String message) {
        if (obj != null) {
            throw new BizException(400, message);
        }
    }
    
    public static void notNull(Object obj, String message) {
        if (obj == null) {
            throw new BizException(400, message);
        }
    }

    public static void notNull(Object obj, Supplier<BizException> callback) {
        if (obj == null) {
            throw callback.get();
        }
    }

}
