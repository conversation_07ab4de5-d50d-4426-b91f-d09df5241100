package com.aic.app.util;

import java.nio.charset.StandardCharsets;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Map;
public class SignUtils {

    public static String preHash(String timestamp, String method, String requestPath, String queryString) {
        return timestamp + method + requestPath + queryString;
    }

    public static String sign(String message, String secretKey) throws Exception {
        // 使用 HMAC-SHA256 对预签名字符串进行签名
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] hashBytes = sha256_HMAC.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes);
    }

    public static Signature createSignature(String method, String requestPath, String params, String secretKey) throws Exception {
        // 获取 ISO 8601 格式时间戳
        String timestamp = getISO8601Timestamp();
        // 生成签名
        String message = preHash(timestamp, method, requestPath, params);
        String signature = sign(message, secretKey);
        return new Signature(signature, timestamp);
    }

//    private static String getISO8601Timestamp() {
//        // 获取 ISO 8601 格式的时间戳 (例: 2024-12-07T14:00:00Z)
//        return java.time.OffsetDateTime.now().toString().substring(0, 19) + "Z";
//    }

    private static String getISO8601Timestamp() {
        // 使用自定义格式化器获取没有微秒的 ISO 8601 格式时间戳
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(java.time.ZoneOffset.UTC);
        return formatter.format(OffsetDateTime.now().withOffsetSameInstant(java.time.ZoneOffset.UTC)).substring(0, 19) + "Z";
    }

    // 用于返回签名和时间戳
    public static class Signature {
        public String signature;
        public String timestamp;

        public Signature(String signature, String timestamp) {
            this.signature = signature;
            this.timestamp = timestamp;
        }

        public String getSignature() {
            return signature;
        }

        public String getTimestamp() {
            return timestamp;
        }
    }
    
    public static Map<String, String> signRequestHeader(String method, String requestPath, String params) throws Exception {
        // API 凭证和项目 ID
        String apiKey = "e7ed5e19-1293-4540-a0e4-50501f3ef6bc";
        String secretKey = "767CA34B733B29EAE350A4FA3FF7AA0C";
        String passphrase = "Cc123123.";
        String project = "3287c357e6fef5c69e59797aea5c107d";    // 此处仅适用于 WaaS APIs
        Signature sign = createSignature(method, requestPath, params, secretKey);

        // 创建请求头的 Map
        return Map.of(
                "OK-ACCESS-KEY", apiKey,
                "OK-ACCESS-SIGN", sign.signature,
                "OK-ACCESS-TIMESTAMP", sign.timestamp,
                "OK-ACCESS-PASSPHRASE", passphrase,
                "OK-ACCESS-PROJECT", project, // 这仅适用于 WaaS APIs
                "Content-Type", "application/json" // POST 请求需要加上这个头部
        );
    }

}
