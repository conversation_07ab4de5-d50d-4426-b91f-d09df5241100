package com.aic.app.util;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Random;

@Slf4j
public final class Utils {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
    
    private static boolean isCloseTime = false;
    
    public static void setClose(boolean isClose) {
        isCloseTime = isClose;
    }

    /**
     * 是否是结算时间
     * @return
     */
    public static boolean isCloseTime() {
        if (isCloseTime) {
            return true;
        }
        Calendar instance = Calendar.getInstance();
        int hour = instance.get(Calendar.HOUR_OF_DAY);
        int minute = instance.get(Calendar.MINUTE);
        int second = instance.get(Calendar.SECOND);
        // 23:59:30 - 00:05:59 期间为结算时间，不能提现
        if (hour == 23 && minute == 59 && second >= 30) {
            return true;
        }
        // 07:59:30 - 08:05:59 期间为结算时间，不能提现
        // 15:59:30 - 16:05:59 期间为结算时间，不能提现
        
        
        
        return hour == 0 && minute <= 5;
    } 
    
    public static String formatBeginTime(Date date) {
        return formatDate(date, "yyyy-MM-dd 00:00:00");
    }
    
    public static String formatEndTime(Date date) {
        return formatDate(date, "yyyy-MM-dd 23:59:59");
    }
    
    public static String formatDate(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }
    
    public static String genOrderNo() {
        String day = dateFormat.format(new Date());
        String index = String.format("%03d", new Random().nextInt(1000));
        return day + index;
    }

    private static final char[] CHARSET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();

    public static String generateInviteCode() {
        final int CODE_LENGTH = 6;
        final Random RNG = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            int idx = RNG.nextInt(CHARSET.length);
            code.append(CHARSET[idx]);
        }
        return code.toString();
    }

    /**
     * 是否开发环境
     */
    public static boolean isDev() {
        String home = System.getenv("HOME");
        return home != null && (home.startsWith("/Users/<USER>") || home.startsWith("/Users/<USER>"));
    }
    
    public static void sendNotify(String msg) {
//        http://h5.gex.group/notify?msg=hello
        if (isDev()) {
            System.out.println("[notify] msg=" + msg);
            return;
        }
        try {
            String _msg = java.net.URLEncoder.encode(msg, StandardCharsets.UTF_8);
            String url = "http://h5.gex.group/notify?msg=" + _msg;
            NotifyResult ret = HttpUtils.get(url, NotifyResult.class);
            log.info("[notify] msg={}, result = {}", msg, ret);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NotifyResult {
        String msg;
    }

    public static void main(String[] args) {
        sendNotify("[job7] 执行成功");
    }


}
