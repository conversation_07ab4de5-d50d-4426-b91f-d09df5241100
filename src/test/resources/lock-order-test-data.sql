-- 锁仓订单系统测试数据文件
-- 基于现有test-data.sql扩展，添加锁仓订单相关测试数据

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS xyc CHARACTER SET utf8mb4;
USE xyc;

-- 按依赖反向顺序删除表，确保清理干净
DROP TABLE IF EXISTS `lock_order`, `stake_reward_log`, `biz_log`, `user_stake`, `user_project`, `whitelist`, `project`, `user_relation`, `reward_log`, `user_log`, `user_address`, `user_product`, `stake_user`, `product`, `user`, `asset_transfer`, `user_asset`, `asset`, `sys_config`, `events`, `withdraw`, `price_history`, `node_product`;

--
-- 表结构定义
--

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `type` int(1) NOT NULL DEFAULT '0' COMMENT '账户类型: 0-普通账户 1-钱包账户',
  `address` varchar(42) DEFAULT NULL COMMENT '地址',
  `code` varchar(32) DEFAULT NULL COMMENT '我的邀请码',
  `pid` int(32) DEFAULT NULL COMMENT '邀请人ID',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '等级',
  `pre_level` int(11) NOT NULL DEFAULT '1' COMMENT '预售等级',
  `lock_level` tinyint(1) DEFAULT '0' NOT NULL COMMENT '是否锁定等级',
  `set_level_time` datetime DEFAULT NULL COMMENT '设置等级时间',
  `last_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `juid` varchar(255) DEFAULT NULL COMMENT '交易所ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='用户表';

CREATE TABLE `product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL COMMENT '类型(1-预售节点 2-普通商品 3-债券 4-城主)',
  `name` varchar(128) NOT NULL COMMENT '名称',
  `description` varchar(1000) DEFAULT NULL COMMENT '商品描述',
  `price` decimal(25,8) NOT NULL COMMENT '价格',
  `rate` decimal(25,6) NOT NULL COMMENT '日收益率',
  `fee` decimal(25,6) NOT NULL COMMENT '手续费',
  `day` int(11) NOT NULL COMMENT '天数',
  `stock` decimal(25,8) NOT NULL DEFAULT '-1' COMMENT '总量: -1 表示不限',
  `sold` decimal(25,8) NOT NULL DEFAULT '0' COMMENT '已售数量',
  `image` varchar(512) DEFAULT NULL COMMENT '图片',
  `enable` bigint(1) NOT NULL DEFAULT '0' COMMENT '是否有效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='商品表';

CREATE TABLE `stake_user` (
    `id`             int auto_increment primary key,
    `user_id`        int                                 not null comment '用户ID',
    `token_id`       varchar(32)                         not null comment '资产ID',
    `current_amount` decimal(25, 8) default 0.00000000    not null comment '活期限额',
    `pending_amount` decimal(25, 8) default 0.00000000    not null comment '待确认',
    `static_pool`    decimal(25, 8) default 0.00000000    not null comment '静态池子',
    `dynamic_pool`   decimal(25, 8) default 0.00000000    not null comment '动态池子',
    `today_static`   decimal(25, 8) default 0.00000000    not null comment '今日静态收益',
    `total_static`   decimal(25, 8) default 0.00000000    not null comment '累计静态收益',
    `today_dynamic`  decimal(25, 8) default 0.00000000    not null comment '今日动态收益',
    `total_dynamic`  decimal(25, 8) default 0.00000000    not null comment '累计动态收益',
    `today_buy`      decimal(25, 8) default 0.00000000    not null comment '今日购买',
    `total_buy`      decimal(25, 8) default 0.00000000    not null comment '累计购买',
    `can_receive`    decimal(25, 8) default 0.00000000    not null comment '可领取',
    `week_dynamic`   decimal(25, 8) default 0.00000000    not null comment '周分红',
    `team_perf`      decimal(25, 8) default 0.00000000    not null comment '团队业绩',
    `old_team_perf`  decimal(25, 8) default 0.00000000    not null comment '上周团队业绩',
    `max_team_perf`  decimal(25, 8) default 0.00000000    not null comment '历史最大团队业绩',
    `node`           int            default 0             not null comment '节点数量',
    `node_perf`      decimal(25, 8) default 0.00000000    not null comment '节点业绩',
    `max_node_perf`  decimal(25, 8) default 0.00000000    not null comment '历史最大节点业绩',
    `node_reward`    decimal(25, 8) default 0.00000000    not null comment '节点返佣',
    `node_pool`      decimal(25, 8) default 0.00000000    not null comment '节点池',
    `sum_amount`     decimal(25, 8) default 0.00000000    not null comment '伞下业绩',
    `stake_first`    tinyint(1)     default 0             not null comment '是否完成首次质押',
    `stake_limit`    decimal(25, 8) default 0.00000000    not null comment '质押额度',
    `burn_limit`     decimal(25, 8) default 0.00000000    not null comment '销毁额度',
    `create_time`    datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    `update_time`    datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
) COMMENT='质押用户表';

CREATE TABLE `user_stake` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL COMMENT '类型：0-质押 1-解除质押',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token_id` varchar(32) NOT NULL COMMENT 'TOKEN',
  `quantity` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '数量',
  `txid` varchar(66) DEFAULT NULL COMMENT '交易号',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` bigint(1) NOT NULL DEFAULT '0' COMMENT '0-待确认 1-有效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='用户质押记录';

CREATE TABLE `sys_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reward_rate` decimal(25,8) NOT NULL COMMENT '收益率',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='系统配置表';

CREATE TABLE `user_relation` (
  `id` int(11) NOT NULL,
  `pid` int(11) DEFAULT NULL,
  `layer` int(11) NOT NULL DEFAULT '0',
  `path` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE TABLE `asset` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token_id` varchar(32) NOT NULL COMMENT '资产ID',
  `token_name` varchar(32) NOT NULL COMMENT '资产名称',
  `logo` varchar(512) DEFAULT NULL,
  `ido_address` varchar(64) DEFAULT NULL,
  `stake_address` varchar(64) DEFAULT NULL,
  `token_address` varchar(64) DEFAULT NULL,
  `stake` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可以质押',
  `total_staked` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '总质押',
  `total_supply` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '回购池',
  `total_burn` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '销毁池',
  `total_dao` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT 'dao池',
  `version` int(13) NOT NULL DEFAULT '0' COMMENT '版本',
  `total_week_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '周分红池',
  `real_week_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '周分红池(实发)',
  `node_address` varchar(66) DEFAULT NULL COMMENT '节点地址',
  `node_reward` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点返佣',
  `node_price` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点价格',
  `withdraw_address` varchar(66) DEFAULT NULL COMMENT '提现地址',
  `node_target` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点目标业绩',
  `node_rate` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点未达业绩拿的比例',
  `node_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点分红',
  `total_supply_bnb` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '回购池bnb',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产';

CREATE TABLE `user_product` (
    `id`                  int auto_increment primary key,
    `user_id`             int(11) not null comment '用户ID',
    `quantity`            int(11) not null default 0 comment '数量',
    `price`               decimal(25,8) not null default 0 comment '价格',
    `type`                int(1) not null comment '类型 1-节点 2-普通商品 3-债券 4-城主',
    `order_no`            varchar(32) not null comment '订单号',
    `product_id`          int(1) not null comment '产品ID',
    `power`               decimal(25,8) not null default 0 comment '释放金额',
    `amount`              decimal(25,8) not null default 0 comment '申请金额',
    `profit`              decimal(25,8) not null default 0 comment '利息',
    `fee`                 decimal(25,8) not null default 0 comment '手续费',
    `rate`                decimal(25,6) not null default 0.004 comment '日收益率',
    `day`                 int(11) not null default 0 comment '天数',
    `release_day`         int(11) not null default 0 comment '发放天数',
    `create_time`         datetime not null comment '创建时间',
    `status`              int(1) not null default 0 comment '0-待确认 1-有效，2-已经期',
    `update_time`         datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `daily_release_amount` decimal(25,8) default 0 comment '每日释放金额',
    `total_released`      decimal(25,8) default 0 comment '已释放总金额',
    `available_amount`    decimal(25,8) default 0 comment '可提取金额(已释放未提取)',
    `last_release_date`   date comment '最后释放日期'
) COMMENT='用户订单表';

CREATE TABLE `user_log` (
    `id`          int auto_increment primary key,
    `user_id`     int(32) not null comment '用户ID',
    `type`        int(2) not null comment '类型',
    `product_amount` decimal(25,8) not null default 0 comment '理财金额',
    `amount`      decimal(25,8) not null default 0 comment '金额',
    `last_amount` decimal(25,8) not null default 0 comment '金额',
    `remark`      varchar(512) null comment '备注',
    `symbol`      varchar(32) DEFAULT 'XYC' null comment '币种',
    `token_id`    varchar(32) null comment 'TOKEN',
    `create_time` datetime not null comment '创建时间'
) COMMENT='用户流水';

-- 锁仓订单表
CREATE TABLE `lock_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` VARCHAR(64) NOT NULL COMMENT '锁仓订单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `source_type` TINYINT NOT NULL COMMENT '来源类型: 1-节点产品 2-普通商品 3-债券产品 4-质押 5-其他',
    `source_id` BIGINT NULL COMMENT '来源ID(user_product.id或其他业务ID)',
    `token_id` VARCHAR(32) NOT NULL COMMENT '代币ID',
    
    -- 锁仓基本信息
    `lock_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '锁仓总金额',
    `released_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '已释放金额',
    `available_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '可提取金额(已释放未提取)',
    
    -- 释放规则
    `release_type` TINYINT NOT NULL COMMENT '释放类型: 1-线性释放 2-阶梯释放 3-一次性释放',
    `total_days` INT NOT NULL COMMENT '总锁仓天数',
    `released_days` INT NOT NULL DEFAULT 0 COMMENT '已释放天数',
    `daily_release_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '每日释放金额',
    
    -- 收益相关
    `static_rate` DECIMAL(10,6) NOT NULL DEFAULT 0 COMMENT '静态收益率',
    `enable_static` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用静态收益: 0-否 1-是',
    `enable_dynamic` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用动态收益: 0-否 1-是',
    `compound_interest` TINYINT NOT NULL DEFAULT 0 COMMENT '是否复利: 0-否 1-是',
    
    -- 状态和释放计数
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-待激活 1-锁仓中 2-已完成 3-已取消',
    
    -- 扩展配置
    `lock_config` JSON NULL COMMENT '锁仓配置(阶梯释放规则等)',
    
    -- 审计字段
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_token_id` (`token_id`),
    KEY `idx_source` (`source_type`, `source_id`),
    KEY `idx_released_days` (`released_days`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='锁仓订单表';

--
-- 插入测试数据
--

-- 1. 系统配置 (静态收益率为 0.4%)
INSERT INTO `sys_config` (`id`, `reward_rate`) VALUES (1, 0.004);

-- 2. 资产配置 (XYC 为可质押资产)
INSERT INTO `asset`(`id`, `token_id`, `token_name`, `stake`, `stake_address`, `token_address`)
VALUES (1, 'XYC', 'XYC', 1, '0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca', '0x8a6556FaA0846d329D470Ce1342236ca2c6609d0');

-- 3. 测试产品数据
INSERT INTO `product` (`id`, `type`, `name`, `description`, `price`, `rate`, `fee`, `day`, `stock`, `sold`, `enable`) VALUES
-- 节点产品
(1001, 1, '节点产品A', '30天节点产品，支持复利', 1000.00000000, 0.010000, 0.000000, 30, -1, 0, 1),
(1007, 1, '节点产品B', '60天节点产品，支持复利', 5000.00000000, 0.012000, 0.000000, 60, -1, 0, 1),

-- 普通商品
(1002, 2, '普通商品A', '360天线性释放普通商品', 500.00000000, 0.004000, 0.000000, 360, -1, 0, 1),
(1008, 2, '普通商品B', '360天线性释放普通商品', 1000.00000000, 0.004000, 0.000000, 360, -1, 0, 1),

-- 债券产品
(1003, 3, '线性债券A', '180天线性释放债券', 2000.00000000, 0.008000, 0.000000, 180, 100, 0, 1),
(1005, 3, '阶梯债券B', '90天阶梯释放债券', 5000.00000000, 0.012000, 0.000000, 90, 50, 0, 1),
(1006, 3, '定期债券C', '365天到期一次性释放', 10000.00000000, 0.015000, 0.000000, 365, 20, 0, 1),

-- 城主商品
(1004, 4, '城主商品', '增加4倍收益次数', 100.00000000, 0.000000, 0.000000, 1, 1000, 0, 1);

-- 4. 测试用户数据
INSERT INTO `user` (`id`, `type`, `address`, `code`, `pid`, `level`, `create_time`) VALUES
(100001, 1, '0x1111111111111111111111111111111111111111', 'USER001', NULL, 1, '2024-01-01 00:00:00'),
(100002, 1, '0x2222222222222222222222222222222222222222', 'USER002', 100001, 1, '2024-01-01 00:00:00'),
(100003, 1, '0x3333333333333333333333333333333333333333', 'USER003', 100001, 1, '2024-01-01 00:00:00'),
(100004, 1, '0x4444444444444444444444444444444444444444', 'USER004', 100002, 1, '2024-01-01 00:00:00'),
(100005, 1, '0x5555555555555555555555555555555555555555', 'USER005', 100002, 1, '2024-01-01 00:00:00');

-- 5. 用户关系数据
INSERT INTO `user_relation` (`id`, `pid`, `layer`, `path`) VALUES
(100001, NULL, 0, '/100001'),
(100002, 100001, 1, '/100001/100002'),
(100003, 100001, 1, '/100001/100003'),
(100004, 100002, 2, '/100001/100002/100004'),
(100005, 100002, 2, '/100001/100002/100005');

-- 6. 质押用户数据
INSERT INTO `stake_user` (`user_id`, `token_id`, `current_amount`, `total_buy`, `pending_amount`, `static_pool`) VALUES
(100001, 'XYC', 1000, 1000, 0, 50),
(100002, 'XYC', 5000, 5000, 0, 100),
(100003, 'XYC', 3000, 3000, 0, 75),
(100004, 'XYC', 2000, 2000, 0, 40),
(100005, 'XYC', 1500, 1500, 0, 30);

-- 7. 测试用的用户购买记录（用于测试数据迁移）
INSERT INTO `user_product` (`id`, `user_id`, `type`, `order_no`, `product_id`, `amount`, `quantity`, `day`, `rate`, `status`, `create_time`) VALUES
-- 节点产品订单
(2001, 100001, 1, 'NODE_ORDER_001', 1001, 1000.00000000, 1, 30, 0.010000, 1, '2024-01-01 10:00:00'),
(2002, 100002, 1, 'NODE_ORDER_002', 1002, 5000.00000000, 1, 60, 0.012000, 1, '2024-01-01 11:00:00'),

-- 普通商品订单
(2003, 100003, 2, 'NORMAL_ORDER_001', 1003, 3600.00000000, 1, 1, 0.004000, 1, '2024-01-01 12:00:00'),
(2004, 100004, 2, 'NORMAL_ORDER_002', 1004, 1800.00000000, 1, 1, 0.004000, 1, '2024-01-01 13:00:00'),

-- 债券产品订单
(2005, 100005, 3, 'BOND_ORDER_001', 1005, 1800.00000000, 1, 180, 0.008000, 1, '2024-01-01 14:00:00'),

-- 城主商品订单
(2006, 100001, 4, 'CITY_ORDER_001', 1004, 100.00000000, 1, 1, 0.000000, 1, '2024-01-01 15:00:00');

-- 8. 测试用的锁仓订单数据（模拟已存在的锁仓订单）
INSERT INTO `lock_order` (`id`, `order_no`, `user_id`, `source_type`, `source_id`, `token_id`, `lock_amount`, `released_amount`, `available_amount`, `release_type`, `total_days`, `released_days`, `daily_release_amount`, `static_rate`, `enable_static`, `enable_dynamic`, `compound_interest`, `status`, `create_time`) VALUES
-- 线性释放测试订单
(3001, 'LOCK_TEST_001', 100001, 1, 2001, 'XYC', 1000.00000000, 0.00000000, 0.00000000, 1, 30, 0, 33.33333333, 0.010000, 1, 1, 1, 1, '2024-01-01 10:00:00'),
(3002, 'LOCK_TEST_002', 100002, 2, 2003, 'XYC', 3600.00000000, 0.00000000, 0.00000000, 1, 360, 0, 10.00000000, 0.004000, 1, 1, 0, 1, '2024-01-01 12:00:00'),

-- 一次性释放测试订单（已到期）
(3003, 'LOCK_TEST_003', 100003, 3, 2005, 'XYC', 5000.00000000, 0.00000000, 0.00000000, 3, 30, 30, 0.00000000, 0.008000, 1, 1, 0, 1, '2024-01-01 14:00:00'),

-- 阶梯释放测试订单
(3004, 'LOCK_TEST_004', 100004, 3, 2005, 'XYC', 2000.00000000, 0.00000000, 0.00000000, 2, 60, 0, 0.00000000, 0.008000, 1, 1, 0, 1, '2024-01-01 16:00:00'),

-- 已完成的订单
(3005, 'LOCK_TEST_005', 100005, 1, 2001, 'XYC', 500.00000000, 500.00000000, 500.00000000, 1, 10, 10, 50.00000000, 0.010000, 1, 1, 1, 2, '2024-01-01 18:00:00');

-- 9. 初始化一些用户日志数据
INSERT INTO `user_log` (`user_id`, `type`, `amount`, `remark`, `create_time`) VALUES
(100001, 230, 1000.00000000, '购买产品: 节点产品A，数量: 1 - 订单ID: 2001, 产品类型: 1', '2024-01-01 10:00:00'),
(100001, 221, 1000.00000000, '创建锁仓订单 - 锁仓订单ID: 3001, 产品类型: 1', '2024-01-01 10:01:00'),
(100002, 230, 3600.00000000, '购买产品: 普通商品A，数量: 1 - 订单ID: 2003, 产品类型: 2', '2024-01-01 12:00:00'),
(100001, 231, 100.00000000, '购买城主商品 - 订单ID: 2006, 产品类型: 4', '2024-01-01 15:00:00'),
(100001, 232, 2.00000000, '增加4倍收益次数: 2次 - 用户ID: 100001', '2024-01-01 15:01:00');