-- 自动生成的测试数据文件
-- 基于 doc/质押功能.md 和 ddl.sql

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS xyc CHARACTER SET utf8mb4;
USE xyc;

-- 按依赖反向顺序删除表，确保清理干净
DROP TABLE IF EXISTS `stake_reward_log`, `biz_log`, `user_stake`, `user_project`, `whitelist`, `project`, `user_relation`, `reward_log`, `user_log`, `user_address`, `user_product`, `stake_user`, `product`, `user`, `asset_transfer`, `user_asset`, `asset`, `sys_config`, `events`, `withdraw`, `price_history`, `node_product`;

--
-- 表结构定义
--

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `type` int(1) NOT NULL DEFAULT '0' COMMENT '账户类型: 0-普通账户 1-钱包账户',
  `address` varchar(42) DEFAULT NULL COMMENT '地址',
  `code` varchar(32) DEFAULT NULL COMMENT '我的邀请码',
  `pid` int(32) DEFAULT NULL COMMENT '邀请人ID',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '等级',
  `pre_level` int(11) NOT NULL DEFAULT '1' COMMENT '预售等级',
  `lock_level` tinyint(1) DEFAULT '0' NOT NULL COMMENT '是否锁定等级',
  `set_level_time` datetime DEFAULT NULL COMMENT '设置等级时间',
  `quadruple_reward_times` int(11) NOT NULL DEFAULT '5' COMMENT '4倍收益次数',
  `last_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `juid` varchar(255) DEFAULT NULL COMMENT '交易所ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

CREATE TABLE `product` (
    `id` int NOT NULL AUTO_INCREMENT,
    `type` int(1) NOT NULL COMMENT '类型',
    `name` varchar(128) NOT NULL COMMENT '名称',
    `description` varchar(1000) NULL COMMENT '商品描述',
    `price` decimal(25,8) NOT NULL COMMENT '价格',
    `rate` decimal(25,6) NOT NULL COMMENT '日收益率',
    `fee` decimal(25,6) NOT NULL COMMENT '手续费',
    `day` int(11) NOT NULL COMMENT '天数',
    `image` varchar(512) NULL COMMENT '图片',
    `enable` bigint(1) NOT NULL DEFAULT 0 COMMENT '是否有效',
    `sold` decimal(25,8) NOT NULL DEFAULT 0 COMMENT '已售数量',
    `limit_time` varchar(20) NULL COMMENT '限制时间段 (格式: "10:00-13:00", 为空表示全天可抢购)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='商品表';

CREATE TABLE `stake_user` (
    `id`             int auto_increment primary key,
    `user_id`        int                                 not null comment '用户ID',
    `token_id`       varchar(32)                         not null comment '资产ID',
    `current_amount` decimal(25, 8) default 0.00000000    not null comment '活期限额',
    `pending_amount` decimal(25, 8) default 0.00000000    not null comment '待确认',
    `static_pool`    decimal(25, 8) default 0.00000000    not null comment '静态池子',
    `dynamic_pool`   decimal(25, 8) default 0.00000000    not null comment '动态池子',
    `today_static`   decimal(25, 8) default 0.00000000    not null comment '今日静态收益',
    `total_static`   decimal(25, 8) default 0.00000000    not null comment '累计静态收益',
    `today_dynamic`  decimal(25, 8) default 0.00000000    not null comment '今日动态收益',
    `total_dynamic`  decimal(25, 8) default 0.00000000    not null comment '累计动态收益',
    `today_buy`      decimal(25, 8) default 0.00000000    not null comment '今日购买',
    `total_buy`      decimal(25, 8) default 0.00000000    not null comment '累计购买',
    `can_receive`    decimal(25, 8) default 0.00000000    not null comment '可领取',
    `week_dynamic`   decimal(25, 8) default 0.00000000    not null comment '周分红',
    `team_perf`      decimal(25, 8) default 0.00000000    not null comment '团队业绩',
    `old_team_perf`  decimal(25, 8) default 0.00000000    not null comment '上周团队业绩',
    `max_team_perf`  decimal(25, 8) default 0.00000000    not null comment '历史最大团队业绩',
    `node`           int            default 0             not null comment '节点数量',
    `node_perf`      decimal(25, 8) default 0.00000000    not null comment '节点业绩',
    `max_node_perf`  decimal(25, 8) default 0.00000000    not null comment '历史最大节点业绩',
    `node_reward`    decimal(25, 8) default 0.00000000    not null comment '节点返佣',
    `node_pool`      decimal(25, 8) default 0.00000000    not null comment '节点池',
    `sum_amount`     decimal(25, 8) default 0.00000000    not null comment '伞下业绩',
    `stake_first`    tinyint(1)     default 0             not null comment '是否完成首次质押',
    `stake_limit`    decimal(25, 8) default 0.00000000    not null comment '质押额度',
    `burn_limit`     decimal(25, 8) default 0.00000000    not null comment '销毁额度',
    `create_time`    datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    `update_time`    datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
) COMMENT='质押用户表';

CREATE TABLE `user_stake` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL COMMENT '类型：0-质押 1-解除质押',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token_id` varchar(32) NOT NULL COMMENT 'TOKEN',
  `quantity` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '数量',
  `txid` varchar(66) DEFAULT NULL COMMENT '交易号',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` bigint(1) NOT NULL DEFAULT '0' COMMENT '0-待确认 1-有效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='用户质押记录';

CREATE TABLE `sys_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reward_rate` decimal(25,8) NOT NULL COMMENT '金额',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='系统配置表';

CREATE TABLE `user_relation` (
  `id` int(11) NOT NULL,
  `pid` int(11) DEFAULT NULL,
  `layer` int(11) NOT NULL DEFAULT '0',
  `path` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE TABLE `asset` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token_id` varchar(32) NOT NULL COMMENT '资产ID',
  `token_name` varchar(32) NOT NULL COMMENT '资产名称',
  `logo` varchar(512) DEFAULT NULL,
  `ido_address` varchar(64) DEFAULT NULL,
  `stake_address` varchar(64) DEFAULT NULL,
  `token_address` varchar(64) DEFAULT NULL,
  `stake` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可以质押',
  `total_staked` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '总质押',
  `total_supply` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '回购池',
  `total_burn` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '销毁池',
  `total_dao` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT 'dao池',
  `version` int(13) NOT NULL DEFAULT '0' COMMENT '版本',
  `total_week_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '周分红池',
  `real_week_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '周分红池(实发)',
  `node_address` varchar(66) DEFAULT NULL COMMENT '节点地址',
  `node_reward` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点返佣',
  `node_price` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点价格',
  `withdraw_address` varchar(66) DEFAULT NULL COMMENT '提现地址',
  `node_target` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点目标业绩',
  `node_rate` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点未达业绩拿的比例',
  `node_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点分红',
  `total_supply_bnb` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '回购池bnb',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资产';

CREATE TABLE `stake_reward_log` (
    `id`             int auto_increment primary key,
    `user_id`        int                       not null comment '用户ID',
    `child_user_id`  int                       not null comment '下级用户ID',
    `level`          int                       not null comment '层级',
    `product_amount` decimal(25, 8) default 0.00000000 not null comment '质押数量',
    `amount`         decimal(25, 8)            not null comment '返佣数量',
    `create_time`    datetime                  not null comment '创建时间',
    `symbol`         varchar(32)    default 'FIST'     null comment '币种',
    `product_id`     int                       null comment '用户下单ID',
    `pay_method`     varchar(32)               null comment '支付方式',
    `price`          decimal(25, 8)            null comment '币种价格'
) COMMENT='质押返佣奖励日志';

CREATE TABLE `user_product` (
    `id`                  int auto_increment primary key,
    `user_id`             int(11) not null comment '用户ID',
    `quantity`            int(11) not null default 0 comment '数量',
    `price`               decimal(25,8) not null default 0 comment '价格',
    `type`                int(1) not null comment '类型 0-活期 1-定期',
    `order_no`            varchar(32) not null comment '订单号',
    `product_id`          int(1) not null comment '产品ID',
    `power`               decimal(25,8) not null default 0 comment '释放金额',
    `amount`              decimal(25,8) not null default 0 comment '申请金额',
    `profit`              decimal(25,8) not null default 0 comment '利息',
    `fee`                 decimal(25,8) not null default 0 comment '手续费',
    `rate`                decimal(25,6) not null default 0.01 comment '日收益率',
    `day`                 int(11) not null default 0 comment '天数',
    `release_day`         int(11) not null default 0 comment '发放天数',
    `create_time`         datetime not null comment '创建时间',
    `status`              int(1) not null default 0 comment '0-待确认 1-有效，2-已经期',
    `update_time`         datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `daily_release_amount` decimal(25,8) default 0 comment '每日释放金额',
    `total_released`      decimal(25,8) default 0 comment '已释放总金额',
    `available_amount`    decimal(25,8) default 0 comment '可提取金额(已释放未提取)',
    `last_release_date`   date comment '最后释放日期'
) COMMENT='用户订单表';

CREATE TABLE `user_log` (
    `id`          int auto_increment primary key,
    `user_id`     int(32) not null comment '用户ID',
    `type`        int(2) not null comment '类型',
    `product_amount` decimal(25,8) not null comment '理财金额',
    `amount`      decimal(25,8) not null comment '金额',
    `last_amount` decimal(25,8) not null default 0 comment '金额',
    `remark`      varchar(128) null comment '备注',
    `symbol`      varchar(32) DEFAULT 'FTST' null comment '币种',
    `token_id`    varchar(32) null comment 'TOKEN',
    `create_time` datetime not null comment '创建时间'
) COMMENT='用户流水';

--
-- 插入测试数据
--

-- 1. 系统配置 (静态收益率为 1%)
INSERT INTO `sys_config` (`id`, `reward_rate`) VALUES (1, 0.01);

-- 2. 资产配置 (FIST 为可质押资产)
INSERT INTO `asset`(`id`, `token_id`, `token_name`, `stake`, `stake_address`, `token_address`)
VALUES (1, 'FIST', 'FIST', 1, '0x019d37b8C21Bf7741B30e1De2eAf6a5846bA79Ca', '0x8a6556FaA0846d329D470Ce1342236ca2c6609d0');

-- 3. 产品
INSERT INTO `product` (id, type, name, price, rate, fee, day, enable) VALUES 
(1, 1, '预售1', 100, 0, 0, 1, 1),
(2, 1, '预售2', 1000, 0, 0, 2, 1),
(3, 1, '预售3', 5000, 0, 0, 3, 1),
(4, 1, '预售4', 20000, 0, 0, 4, 1),
(5, 1, '预售5', 50000, 0, 0, 5, 1),
(6, 1, '预售6', 100000, 0, 0, 6, 1),
(7, 2, 'iPhone16 pro max', 12000, 0, 0, 1, 1);

-- 4. 用户和用户关系
INSERT INTO `user` (`id`, `address`, `code`, `pid`) VALUES
(100000, '0x1000000000000000000000000000000000000000', 'AAAAAA', NULL),
(100001, '0x1000010000000000000000000000000000000001', 'BBBBBB', 100000),
(100002, '0x1000020000000000000000000000000000000002', 'CCCCCC', 100001),
(100003, '0x1000030000000000000000000000000000000003', 'DDDDDD', 100002),
(100004, '0x1000040000000000000000000000000000000004', 'EEEEEE', 100003),
(100005, '0x1000050000000000000000000000000000000005', 'FFFFFF', 100004),
(100011, '0x1000110000000000000000000000000000000011', 'LLLLLL', 100000),
(100012, '0x1000120000000000000000000000000000000012', 'MMMMMM', 100011),
(100013, '0x1000130000000000000000000000000000000013', 'NNNNNN', 100011);

INSERT INTO `user_relation` (`id`, `pid`, `layer`, `path`) VALUES
(100000, NULL, 0, NULL),
(100001, 100000, 1, '/100000/100001'),
(100002, 100001, 2, '/100000/100001/100002'),
(100003, 100002, 3, '/100000/100001/100002/100003'),
(100004, 100003, 4, '/100000/100001/100002/100003/100004'),
(100011, 100000, 1, '/100000/100011'),
(100012, 100011, 2, '/100000/100011/100012'),
(100013, 100011, 2, '/100000/100011/100013');

-- 5. 质押数据
INSERT INTO `user_stake` (`type`, `user_id`, `token_id`, `quantity`, `create_time`, `status`) VALUES
(0, 100000, 'FIST', 2000, NOW(), 1),
(0, 100001, 'FIST', 100, NOW(), 1),
(0, 100011, 'FIST', 100, NOW(), 1),
(0, 100002, 'FIST', 5000, NOW(), 1),
(0, 100003, 'FIST', 5000, NOW(), 1),
(0, 100004, 'FIST', 10000, NOW(), 1),
(0, 100012, 'FIST', 5000, NOW(), 1),
(0, 100013, 'FIST', 5000, NOW(), 1);

-- 6. 质押用户汇总
INSERT INTO `stake_user` (`user_id`, `token_id`, `current_amount`, `total_buy`, `pending_amount`, `static_pool`) VALUES
(100000, 'FIST', 2000, 2000, 0, 0),
(100001, 'FIST', 100, 100, 0, 0),
(100002, 'FIST', 5000, 5000, 500, 0),
(100003, 'FIST', 5000, 5000, 0, 0),
(100004, 'FIST', 10000, 10000, 0, 100), -- 静态池子有余额
(100011, 'FIST', 100, 100, 0, 0),
(100012, 'FIST', 5000, 5000, 0, 0),
(100013, 'FIST', 5000, 5000, 0, 0);

-- 7. 普通商品订单
INSERT INTO `user_product` (`user_id`, `type`, `order_no`, `product_id`, `amount`, `status`, `create_time`, `last_release_date`, `rate`) VALUES 
(100003, 2, 'NORMAL_ORDER_001', 99, 3600, 1, '2025-07-10 00:00:00', '2025-07-14', 0.01),
(100004, 2, 'NORMAL_ORDER_002', 99, 1000, 4, '2025-01-01 00:00:00', NULL, 0.01),
(100005, 2, 'NORMAL_ORDER_003', 99, 7200, 1, '2025-07-10 00:00:00', NULL, 0.01);