# TestContainers ????
spring.application.name=app-test
server.port=0

# ????? - ?? TestContainers ????
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

# Druid ?????
spring.datasource.druid.initial-size=2
spring.datasource.druid.max-active=10
spring.datasource.druid.min-idle=2
spring.datasource.druid.max-wait=60000
spring.datasource.druid.validation-query=SELECT 1
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true

# Redis ?? - ?? TestContainers ????
spring.data.redis.database=0

# ????
logging.level.com.aic.app.mapper=debug
logging.level.com.aic.app=debug
logging.level.org.testcontainers=INFO
logging.level.com.github.dockerjava=WARN
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# ??????
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

# ????
app.open=true
app.hello=test container hello!
app.initBalance=100000000

# ?????????
router.address=0x2bE080AF2eac3d6CD01DE1B21d31139d9AC6D9F7
referral.address=0xCD69352A989711A7786fcf296f48A36f932DDc72

# JuCoin API
jc.host=https://api.jucoin.vc

# Swagger ??
springdoc.swagger-ui.path=/swagger-ui.html
