drop table if exists xyc.sys_user;
CREATE TABLE xyc.`sys_user`
(
    `id`          int not null auto_increment,
    `account`     text,
    `password`    text,
    `name`        text,
    `login_check` text,
    `state`       int         DEFAULT NULL,
    `create_date` datetime    DEFAULT NULL,
primary key (id)
);

truncate table xyc.sys_user;
insert into xyc.sys_user(id, account, password, name, login_check, state, create_date)
value (1, 'admin', 'ade8110ecb08952f9667d36b5aa63307', 'admin', 'PasswordImgCodeCheck', 0, now());


 insert into xyc.sys_user(id, account, password, name, login_check, state, create_date)
    value (1000, 'sys', 'ad0f76a2c6d7e48fcaca652b8d7e21a9', 'sys', 'PasswordImgCodeCheck', 0, now());


# add google_code col
alter table xyc.sys_user add column google_code varchar(128) null comment '谷歌二次验证私钥';

# add role col
alter table xyc.sys_user add column role varchar(32) not null default 'NORMAL' comment '角色: ADMIN-管理员 NORMAL-普通角色';

# update admin role ADMIN
update xyc.sys_user set role = 'ADMIN' where account = 'admin';

# 4L2DZCZRN7EUFUAL
update xyc.sys_user set google_code = '4L2DZCZRN7EUFUAL' where account = 'admin';
update xyc.sys_user set google_code = '4L2DZCZRN7EUFUAL' where account = 'sys';
