
#ijhttp --private-env-file http-client.private.env.json --env dev 测试.http 登录
# await ethereum.request({ method: 'eth_requestAccounts' })
#const signature = await ethereum.request({
#            method: 'personal_sign',
#            params: ["Welcome to Xyc fun\n\nWallet address:\n0xb5686faa50e064917593ad753c0c26b7764fdd85\n\nNonce:\n08f151f8-2832-4e00-8fdb-63e730731408", ethereum.selectedAddress]
#});
#console.log('Signature:', signature);

### 获取签名信息

GET {{host}}/api/sign-msg?address=******************************************

### 地址登录

POST {{host}}/api/address/login
Content-Type: application/json

{
  "address": "******************************************",
  "sign": "0xc16b3b9d2e5a300d387ab28758eabbefc72658953302ecf7b11a1a5f406876a91aa207928233e36aa6bc94b71b344fee698a602a5239dfc68f28f6c94027734e1c"
}

> {%

    client.global.set("token", response.body.data.token)

%}


### 获取用户信息

GET {{host}}/api/userinfo
Authorization: {{token}}




### 可质押资产

GET {{host}}/api/stake-assets

### 质押首页数据

GET {{host}}/api/user/stake/FIST
Authorization: {{token}}

### 质押

POST {{host}}/api/user/stake
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "amount": "1000"
}

### 解除质押

POST {{host}}/api/user/unstake
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "amount": "500"
}

### 质押记录

GET {{host}}/api/user/stake/records/FIST
Authorization: {{token}}


### 我的资产

GET {{host}}/api/user/assets
Authorization: {{token}}

#
#### 获取理财数据
#
#GET {{host}}/api/product/info
#Authorization: {{token}}


### 释放记录

GET {{host}}/api/user/withdraw/records/FIST
Authorization: {{token}}

### 提取静态（30天）

POST {{host}}/api/user/claim-static
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "productId": 2,
  "amount": 1
}

### 提取静态（60天）

POST {{host}}/api/user/claim-static
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "productId": 3,
  "amount": 4
}

### 提取静态（90天）

POST {{host}}/api/user/claim-static
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "productId": 4,
  "amount": 1000
}

### 提取静态（180天）

POST {{host}}/api/user/claim-static
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FTST",
  "productId": 5,
  "amount": 100
}

### 提取动态（30天）

POST {{host}}/api/user/claim-dynamic
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "productId": 2,
  "amount": 3
}

### 提取动态（60天）

POST {{host}}/api/user/claim-dynamic
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "productId": 3,
  "amount": 1000
}

### 提取动态（90天）

POST {{host}}/api/user/claim-dynamic
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "productId": 4,
  "amount": 1000
}

### 提取动态（180天）

POST {{host}}/api/user/claim-dynamic
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FTST",
  "productId": 5,
  "amount": 100
}

### 提取周分红

POST {{host}}/api/user/claim-week
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FTST",
  "amount": 10
}

### 释放列表

GET {{host}}/api/user/claim-record/FIST
Authorization: {{token}}

### 提现到钱包

POST {{host}}/api/user/withdraw
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "amount": 0.1
}

### 划转记录

GET {{host}}/api/user/transfer
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "BNB",
  "quantity": "0.1"
}

### 划出

POST {{host}}/api/user/transfer
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "BNB",
  "quantity": "0.1"
}

### 用户流水

GET {{host}}/api/user/user-log?tokenId=FIST&page=1&size=2&type=3,4
Authorization: {{token}}

### 邀请记录

GET {{host}}/api/user/invite-records?page=1&size=10&tokenId=FIST
Authorization: {{token}}

### 排行

GET {{host}}/api/user/rank/FIST
Authorization: {{token}}

### 提现记录

GET {{host}}/api/user/withdraw-records?page=1&size=10&tokenId=FIST
Authorization: {{token}}

### 历史价格

GET {{host}}/api/prices/FIST

### 产品列表

GET {{host}}/api/product


### 获取购买项目签名

POST {{host}}/api/user/wallet/buy/1
Authorization: {{token}}
Content-Type: application/json

### 获取提现签名

POST {{host}}/api/user/wallet/withdraw
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "amount": 0.1
}

### 获取提取静态签名

POST {{host}}/api/user/wallet/claim-static
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FTST",
  "productId": 5,
  "amount": "497.98478738"
}

### 获取提取周分红签名

POST {{host}}/api/user/wallet/claim-week
Authorization: {{token}}
Content-Type: application/json

{
  "tokenId": "FIST",
  "amount": 10
}


### 获取节点信息

GET {{host}}/api/node/info/FIST
Authorization: {{token}}

### 购买节点

POST {{host}}/api/node/buy/FIST
Authorization: {{token}}

### 返佣记录

GET {{host}}/api/user/user-log?type=15
Authorization: {{token}}

### 提现记录

GET {{host}}/api/user/user-log?type=16,17
Authorization: {{token}}


### 分红记录

GET {{host}}/api/user/user-log?type=18
Authorization: {{token}}


### 提现

POST {{host}}/api/node/withdraw/FIST
Authorization: {{token}}
Content-Type: application/json

{
  "amount": "2"
}

### 提取分红

POST {{host}}/api/node/receive/FIST
Authorization: {{token}}
Content-Type: application/json

{
  "amount": "4"
}

