-- 产品表新增已售数量字段
alter table xyc.product add column sold decimal(25,8) not null default 0 comment '已售数量' after day;
-- 产品表新增总量字段
alter table xyc.product add column total decimal(25,8) not null default -1 comment '总量: -1 表示不限' after day;
-- 产品新增折扣字段
alter table xyc.product add column discount decimal(25,8) not null default 0 comment '折扣' after total;
delete from xyc.product where id in (1001,1002,1003,1004);
-- 插入债券产品
insert into xyc.product(id, type, name, price, rate, fee, day, enable, discount, total)
values (1001, 3, 'XYC-USDT', 100, 0, 0, 90, 1, 0.05, 100000),
       (1002, 3, 'XYC-USDT', 1000, 0, 0, 180, 1, 0.08, 100000),
       (1003, 3, 'XYC-USDT', 5000, 0, 0, 360, 1, 0.1, 100000),
       (1004, 3, 'XYC-USDT', 20000, 0, 0, 540, 1,0.12, 100000);

-- 插入城主产品
delete from xyc.product where id in (2001);
insert into xyc.product(id, type, name, price, rate, fee, day, enable, total)
values (2001, 4, '城主', 100, 0, 0, 1, 1, 100);

-- user_stake 表新增两个字段
alter table xyc.user_stake add column pay_token varchar(32) null default '' comment '支付TOKEN' after quantity;
alter table xyc.user_stake add column ori_amount decimal(25,8) null default 0 comment '原金额' after pay_token;

-- add col limit_time
alter table xyc.product add column limit_time varchar(20) null comment '限制时间段 (格式: "10:00-13:00", 为空表示全天可抢购)' after enable;

-- 添加4倍收益次数字段到用户表
-- 每个用户初始有5次4倍收益次数
-- 购买城主商品增加2次
-- 推荐有效用户（质押100及以上）增加1次

ALTER TABLE xyc.user ADD COLUMN quadruple_reward_times INT NOT NULL DEFAULT 5 COMMENT '4倍收益次数';

-- 为现有用户设置初始次数
UPDATE xyc.user SET quadruple_reward_times = 5 WHERE quadruple_reward_times IS NULL OR quadruple_reward_times = 0;


-- 为stake_user表添加锁仓静态池和锁仓动态池字段
-- 这两个字段用于存储用户的锁仓收益池数据
ALTER TABLE xyc.stake_user
    ADD COLUMN lock_static_pool DECIMAL(25,8) DEFAULT 0.00000000 NOT NULL COMMENT '锁仓静态池' AFTER dynamic_pool;

ALTER TABLE xyc.stake_user
    ADD COLUMN lock_dynamic_pool DECIMAL(25,8) DEFAULT 0.00000000 NOT NULL COMMENT '锁仓动态池' AFTER lock_static_pool;

-- 为现有数据设置默认值（如果需要的话）
UPDATE xyc.stake_user
SET lock_static_pool = 0.00000000, lock_dynamic_pool = 0.00000000
WHERE lock_static_pool IS NULL OR lock_dynamic_pool IS NULL;
