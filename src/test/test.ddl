
delete from fist.user_log;
delete from fist.user_project;
delete from fist.user_stake;
delete from fist.biz_log;
update fist.user_asset set balance=*********, version=0, reason='';

update fist.stake_user set pending_amount=0,current_amount=0, total_static=0,today_static=0,today_dynamic=0,total_dynamic=0,static_pool=0,dynamic_pool=0,can_receive=0,week_dynamic=0,team_perf=0,sum_amount=0,burn_limit=0,burn_limit=0,stake_first=0;



update fist.stake_user set current_amount=1500 where user_id=100005 and token_id='AAA';
update fist.stake_user set static_pool=100000,dynamic_pool=100000 where user_id=100005 and token_id='AAA';


insert into fist.sys_user(id, account, password, name, login_check, state, create_date)
    value (1000, 'sys', 'ad0f76a2c6d7e48fcaca652b8d7e21a9', 'sys', 'PasswordImgCodeCheck', 0, now());
