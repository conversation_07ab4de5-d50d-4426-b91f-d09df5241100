package com.aic.app;

import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class AppApplicationTests {
	
	@Resource
	ObjectMapper objectMapper;
	
	@Resource
    StakeUserMapper userMapper;
	
	@Test
	void contextLoads() throws Exception {
//		System.out.println(12);
//		Object value = redisTemplate.opsForValue().get("foo");
//		System.out.println("value = " + value);
//		Set<String> keys = redisTemplate.keys("*");
//		System.out.println(keys);
//		redisClient

		System.out.println(userMapper.selectCount(new QueryWrapper<>()));

//		var u = new ApiUser2("zhangsan", 18);

//		System.out.println(objectMapper.writeValueAsString(u));
		
//		String json = """
//				{"xx":12}
//				""";
//		
//		var u = objectMapper.readValue(json, ApiUser2.class);
//
//		System.out.println(u);
		
	}
	
	@Test
	public void testB() throws Exception {
		for (int i = 0; i < 100; i++) {
			System.out.println(Utils.genOrderNo());
			System.out.println(Utils.generateInviteCode());
		}
	}

}
