package com.aic.app.job;

import com.aic.app.model.Product;
import com.aic.app.model.StakeUser;
import com.aic.app.model.SysConfig;
import com.aic.app.model.UserProduct;
import com.aic.app.service.IUserProductService;
import com.aic.app.service.ISysConfigService;
import com.aic.app.mapper.StakeUserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 普通商品功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class Job3NormalProductTest {

    @Resource
    private Job3 job3;
    
    @Resource
    private IUserProductService userProductService;
    
    @Resource
    private ISysConfigService sysConfigService;
    
    @Resource
    private StakeUserMapper stakeUserMapper;

    @Test
    public void testNormalProductLinearRelease() {
        System.out.println("=== 测试普通商品线性释放功能 ===");
        
        // 1. 创建测试数据 - 普通商品订单
        UserProduct testProduct = createTestNormalProduct();
        
        // 2. 执行Job3，触发线性释放
        job3.run();
        
        // 3. 验证释放结果
        UserProduct updatedProduct = userProductService.getById(testProduct.getId());
        System.out.println("订单总金额: " + updatedProduct.getAmount());
        System.out.println("每日释放金额: " + updatedProduct.getDailyReleaseAmount());
        System.out.println("已释放总金额: " + updatedProduct.getTotalReleased());
        System.out.println("可提取金额: " + updatedProduct.getAvailableAmount());
        System.out.println("最后释放日期: " + updatedProduct.getLastReleaseDate());
        
        // 4. 验证静态收益
        StakeUser stakeUser = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, testProduct.getUserId())
        );
        
        if (stakeUser != null) {
            System.out.println("用户静态池: " + stakeUser.getStaticPool());
            System.out.println("今日静态收益: " + stakeUser.getTodayStatic());
            System.out.println("累计静态收益: " + stakeUser.getTotalStatic());
        }
        
        System.out.println("=== 测试完成 ===");
    }

    @Test
    public void testMultipleDaysRelease() {
        System.out.println("=== 测试多天连续释放 ===");
        
        // 创建测试订单
        UserProduct testProduct = createTestNormalProduct();
        
        // 模拟连续3天执行Job
        for (int day = 1; day <= 3; day++) {
            System.out.println("--- 第" + day + "天 ---");
            job3.run();
            
            UserProduct updatedProduct = userProductService.getById(testProduct.getId());
            System.out.println("已释放总金额: " + updatedProduct.getTotalReleased());
            System.out.println("可提取金额: " + updatedProduct.getAvailableAmount());
        }
        
        System.out.println("=== 多天测试完成 ===");
    }

    /**
     * 创建测试用的普通商品订单
     */
    private UserProduct createTestNormalProduct() {
        // 创建测试产品
        Product product = new Product();
        product.setId(999L);
        product.setType(2); // 普通商品
        product.setName("测试普通商品");
        product.setPrice(new BigDecimal("100"));
        product.setRate(new BigDecimal("0.004")); // 0.4%
        product.setDay(360);
        product.setEnable(true);
        
        // 创建用户订单
        UserProduct userProduct = new UserProduct();
        userProduct.setUserId(10001L);
        userProduct.setOrderNo("TEST" + System.currentTimeMillis());
        userProduct.setProductId(product.getId());
        userProduct.setType(2); // 普通商品
        userProduct.setAmount(new BigDecimal("3600")); // 3600元，每天释放10元
        userProduct.setPrice(new BigDecimal("100"));
        userProduct.setQuantity(36);
        userProduct.setStatus(1); // 有效状态
        userProduct.setDay(360);
        userProduct.setReleaseDay(0);
        userProduct.setRate(product.getRate());
        userProduct.setCreateTime(new Date());
        
        // 初始化线性释放字段
        BigDecimal dailyRelease = userProduct.getAmount().divide(new BigDecimal("360"), 8, BigDecimal.ROUND_HALF_UP);
        userProduct.setDailyReleaseAmount(dailyRelease);
        userProduct.setTotalReleased(BigDecimal.ZERO);
        userProduct.setAvailableAmount(BigDecimal.ZERO);
        userProduct.setLastReleaseDate(null);
        
        userProductService.save(userProduct);
        
        System.out.println("创建测试订单: " + userProduct.getOrderNo() + 
                          ", 总金额: " + userProduct.getAmount() + 
                          ", 每日释放: " + dailyRelease);
        
        return userProduct;
    }
}
