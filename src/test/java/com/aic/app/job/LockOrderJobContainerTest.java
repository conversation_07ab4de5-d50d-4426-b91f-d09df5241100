package com.aic.app.job;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.UserLogMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("锁仓订单结算集成测试")
@Testcontainers
@SpringBootTest
@Transactional
public class LockOrderJobContainerTest {

    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8")
            .withDatabaseName("xyc")
            .withUsername("root")
            .withPassword("dbadmin123")
            .withInitScript("lock-order-test-data.sql");

    @DynamicPropertySource
    static void overrideProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);
    }

    @Resource
    private LockOrderJob lockOrderJob;

    @Resource
    private ILockOrderService lockOrderService;

    @Resource
    private IProductPurchaseService productPurchaseService;

    @Resource
    private IProductService productService;

    @Resource
    private IUserService userService;

    @Resource
    private LockOrderMapper lockOrderMapper;

    @Resource
    private UserLogMapper userLogMapper;

    @BeforeEach
    void setUp() {
        // 每个测试前清理数据，确保测试独立性
        // 注意：由于@Transactional，数据会在测试结束后自动回滚
    }

    @Test
    @DisplayName("节点产品购买：应创建锁仓订单并正确设置参数")
    public void testNodeProductPurchase() {
        // 准备数据：获取节点产品
        Product nodeProduct = productService.getById(1001L); // 节点产品A，30天，收益率1%
        User user = userService.getById(100001L);
        
        assertNotNull(nodeProduct);
        assertNotNull(user);
        assertEquals(1, nodeProduct.getType()); // 节点产品
        
        BigDecimal purchaseAmount = new BigDecimal("1000");
        Integer quantity = 1;
        
        // 执行购买
        UserProduct userProduct = productPurchaseService.purchaseProduct(user, nodeProduct, purchaseAmount, quantity);
        
        // 验证购买记录
        assertNotNull(userProduct);
        assertEquals(user.getId(), userProduct.getUserId());
        assertEquals(nodeProduct.getId(), userProduct.getProductId());
        assertEquals(0, purchaseAmount.compareTo(userProduct.getAmount()));
        assertEquals(0, quantity.compareTo(userProduct.getQuantity()));
        
        // 验证产品已售数量更新
        Product updatedProduct = productService.getById(1001L);
        assertEquals(0, new BigDecimal("1.00000000").compareTo(updatedProduct.getSold()));
        
        // 验证锁仓订单创建
        LockOrder lockOrder = lockOrderService.getOne(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getSourceId, userProduct.getId())
                .eq(LockOrder::getSourceType, 1)
        );
        
        assertNotNull(lockOrder, "应该创建锁仓订单");
        assertEquals(user.getId(), lockOrder.getUserId());
        assertEquals(1, lockOrder.getSourceType()); // 节点产品
        assertEquals(0, purchaseAmount.compareTo(lockOrder.getLockAmount()));
        assertEquals(30, lockOrder.getTotalDays()); // 30天
        assertEquals(0, lockOrder.getReleasedDays()); // 初始为0
        assertEquals(1, lockOrder.getStatus()); // 锁仓中
        
        // 验证每日释放金额计算正确
        BigDecimal expectedDailyRelease = purchaseAmount.divide(new BigDecimal("30"), 8, BigDecimal.ROUND_HALF_UP);
        assertEquals(0, expectedDailyRelease.compareTo(lockOrder.getDailyReleaseAmount()));
        
        // 验证购买日志记录
        List<UserLog> purchaseLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, user.getId())
                .eq(UserLog::getType, UserLogType.PRODUCT_PURCHASE.getCode())
        );
        assertTrue(purchaseLogs.size() >= 1, "应该有至少1条购买日志");
        
        // 验证锁仓订单创建日志
        List<UserLog> lockOrderLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, user.getId())
                .eq(UserLog::getType, UserLogType.LOCK_ORDER_CREATE.getCode())
        );
        assertTrue(lockOrderLogs.size() >= 1, "应该至少有1条锁仓订单日志");
    }

    @Test
    @DisplayName("普通商品购买：应创建360天线性释放锁仓订单")
    public void testNormalProductPurchase() {
        lockOrderService.remove(new LambdaQueryWrapper<LockOrder>().ge(LockOrder::getId, 0));
        // 准备数据：获取普通商品
        Product normalProduct = productService.getById(1002L); // 普通商品A，360天
        User user = userService.getById(100002L);
        
        assertNotNull(normalProduct);
        assertEquals(2, normalProduct.getType()); // 普通商品
        
        BigDecimal purchaseAmount = new BigDecimal("3600");
        Integer quantity = 1;
        
        // 执行购买
        UserProduct userProduct = productPurchaseService.purchaseProduct(user, normalProduct, purchaseAmount, quantity);
        
        // 验证锁仓订单创建
        LockOrder lockOrder = lockOrderService.getOne(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getSourceId, userProduct.getId())
                .eq(LockOrder::getSourceType, 2)
        );
        
        assertNotNull(lockOrder);
        assertEquals(2, lockOrder.getSourceType()); // 普通商品
        assertEquals(360, lockOrder.getTotalDays()); // 固定360天

        // 验证每日释放金额：3600 / 360 = 10
        BigDecimal expectedDailyRelease = new BigDecimal("10.00000000");
        assertEquals(expectedDailyRelease, lockOrder.getDailyReleaseAmount());
    }

    @Test
    @DisplayName("债券产品购买：应根据产品配置创建锁仓订单")
    public void testBondProductPurchase() {
        // 准备数据：获取债券产品
        Product bondProduct = productService.getById(1003L); // 线性债券A，180天
        User user = userService.getById(100003L);
        
        assertNotNull(bondProduct);
        assertEquals(3, bondProduct.getType()); // 债券产品
        
        BigDecimal purchaseAmount = new BigDecimal("1800");
        Integer quantity = 1;
        
        // 执行购买
        UserProduct userProduct = productPurchaseService.purchaseProduct(user, bondProduct, purchaseAmount, quantity);
        
        // 验证锁仓订单创建
        LockOrder lockOrder = lockOrderService.getOne(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getSourceId, userProduct.getId())
                .eq(LockOrder::getSourceType, 3)
        );
        
        assertNotNull(lockOrder);
        assertEquals(3, lockOrder.getSourceType()); // 债券产品
        assertEquals(180, lockOrder.getTotalDays()); // 180天
        
        // 验证每日释放金额：1800 / 180 = 10
        BigDecimal expectedDailyRelease = new BigDecimal("10.00000000");
        assertEquals(expectedDailyRelease, lockOrder.getDailyReleaseAmount());
    }

    @Test
    @DisplayName("城主商品购买：不应创建锁仓订单，应增加4倍收益次数")
    public void testCityLordProductPurchase() {
        // 准备数据：获取城主商品
        Product cityLordProduct = productService.getById(1004L); // 城主商品
        User user = userService.getById(100004L);
        
        assertNotNull(cityLordProduct);
        assertEquals(4, cityLordProduct.getType()); // 城主商品
        
        BigDecimal purchaseAmount = new BigDecimal("100");
        Integer quantity = 1;
        
        // 执行购买
        UserProduct userProduct = productPurchaseService.purchaseProduct(user, cityLordProduct, purchaseAmount, quantity);
        
        // 验证购买记录创建
        assertNotNull(userProduct);
        assertEquals(cityLordProduct.getId(), userProduct.getProductId());
        
        // 验证产品已售数量更新
        Product updatedProduct = productService.getById(1004L);
        assertEquals(0, new BigDecimal("1.00000000").compareTo(updatedProduct.getSold()));
        
        // 验证没有创建锁仓订单
        LockOrder lockOrder = lockOrderService.getOne(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getSourceId, userProduct.getId())
                .eq(LockOrder::getSourceType, 4)
        );
        assertNull(lockOrder, "城主商品不应创建锁仓订单");
        
        // 验证城主商品购买日志
        List<UserLog> cityLordLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, user.getId())
                .eq(UserLog::getType, UserLogType.CITY_LORD_PURCHASE.getCode())
        );
        assertEquals(1, cityLordLogs.size());
        
        // 验证4倍收益次数增加日志
        List<UserLog> quadrupleLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, user.getId())
                .eq(UserLog::getType, UserLogType.QUADRUPLE_REWARD_ADD.getCode())
        );
        assertEquals(1, quadrupleLogs.size());
        assertEquals(0, new BigDecimal("2.00000000").compareTo(quadrupleLogs.get(0).getAmount())); // 增加2次
    }

    @Test
    @DisplayName("线性释放处理：应正确计算每日释放并更新状态")
    public void testLinearReleaseProcessing() {
        lockOrderService.remove(new LambdaQueryWrapper<LockOrder>().ge(LockOrder::getId, 0));
        // 准备数据：创建一个10天的锁仓订单，每天释放100
        LockOrder lockOrder = createTestLockOrder(100001L, new BigDecimal("1000"), 10, new BigDecimal("100"));
        
        // 验证初始状态
        assertEquals(0, lockOrder.getReleasedDays());
        assertEquals(0, BigDecimal.ZERO.compareTo(lockOrder.getReleasedAmount()));
        assertEquals(0, BigDecimal.ZERO.compareTo(lockOrder.getAvailableAmount()));
        assertEquals(1, lockOrder.getStatus()); // 锁仓中
        
        // 执行第一次释放
        lockOrderService.processLinearRelease();
        
        // 验证第一次释放结果
        LockOrder afterFirst = lockOrderService.getById(lockOrder.getId());
        assertEquals(1, afterFirst.getReleasedDays());
        assertEquals(0, new BigDecimal("100.00000000").compareTo(afterFirst.getReleasedAmount()));
        assertEquals(0, new BigDecimal("100.00000000").compareTo(afterFirst.getAvailableAmount()));
        assertEquals(1, afterFirst.getStatus()); // 仍在锁仓中
        
        // 验证释放日志记录
        List<UserLog> releaseLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, lockOrder.getUserId())
                .eq(UserLog::getType, UserLogType.LOCK_RELEASE_LINEAR.getCode())
        );
        assertTrue(releaseLogs.size() >= 1, "应该有至少1条释放日志");
        assertEquals(0, new BigDecimal("100.00000000").compareTo(releaseLogs.get(releaseLogs.size()-1).getAmount()));
        
        // 模拟连续释放到第10天（最后一天）
        for (int i = 2; i <= 10; i++) {
            lockOrderService.processLinearRelease();
        }
        
        // 验证最终释放结果
        LockOrder afterFinal = lockOrderService.getById(lockOrder.getId());
        assertTrue(afterFinal.getReleasedDays() >= 10, "释放天数应该大于等于10天");
        assertEquals(0, new BigDecimal("1000.00000000").compareTo(afterFinal.getReleasedAmount())); // 全部释放
        assertEquals(0, new BigDecimal("1000.00000000").compareTo(afterFinal.getAvailableAmount()));
        assertEquals(2, afterFinal.getStatus()); // 已完成
        
        // 验证总共有10条释放日志
        List<UserLog> allReleaseLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, lockOrder.getUserId())
                .eq(UserLog::getType, UserLogType.LOCK_RELEASE_LINEAR.getCode())
        );
        assertEquals(10, allReleaseLogs.size());
    }


    @Test
    @DisplayName("静态收益计算：应基于可提取金额计算收益")
    public void testStaticRewardCalculation() {
        lockOrderService.remove(new LambdaQueryWrapper<LockOrder>().ge(LockOrder::getId, 0));
        // 准备数据：创建有可提取金额的锁仓订单
        LockOrder lockOrder = createTestLockOrder(100003L, new BigDecimal("10000"), 100, new BigDecimal("100"));
        lockOrder.setAvailableAmount(new BigDecimal("1000")); // 设置可提取金额
        lockOrderService.updateById(lockOrder);
        
        // 执行静态收益计算
        lockOrderService.calculateStaticRewards();
        
        // 验证静态收益日志
        List<UserLog> rewardLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, lockOrder.getUserId())
                .eq(UserLog::getType, UserLogType.LOCK_STATIC_REWARD.getCode())
        );
        assertEquals(1, rewardLogs.size());
        
        // 预期收益 = 1000 * 0.01 = 10
        assertEquals(0, new BigDecimal("10.00000000").compareTo(rewardLogs.get(0).getAmount()));
    }

    @Test
    @DisplayName("完整Job执行：应处理所有类型的释放和收益计算")
    public void testCompleteJobExecution() {
        lockOrderService.remove(new LambdaQueryWrapper<LockOrder>().ge(LockOrder::getId, 0));

        // 准备多种类型的锁仓订单
        LockOrder linearOrder = createTestLockOrder(100001L, new BigDecimal("1000"), 10, new BigDecimal("100"));
        LockOrder oneTimeOrder = createTestLockOrder(100002L, new BigDecimal("2000"), 5, BigDecimal.ZERO);
        oneTimeOrder.setReleasedDays(5); // 已到期
        lockOrderService.updateById(oneTimeOrder);
        
        // 记录执行前的状态
        int initialLinearDays = linearOrder.getReleasedDays();
        int initialOneTimeStatus = oneTimeOrder.getStatus();
        
        // 执行完整的Job
        lockOrderJob.run8();
        
        // 验证线性释放处理
        LockOrder afterLinear = lockOrderService.getById(linearOrder.getId());
        assertEquals(initialLinearDays + 1, afterLinear.getReleasedDays());
        assertEquals(0, new BigDecimal("100.00000000").compareTo(afterLinear.getAvailableAmount()));
        
        // 验证一次性释放处理
        LockOrder afterOneTime = lockOrderService.getById(oneTimeOrder.getId());
        assertEquals(2, afterOneTime.getStatus()); // 应该变为已完成
        assertEquals(0, new BigDecimal("2000.00000000").compareTo(afterOneTime.getAvailableAmount()));
        
        // 验证日志记录
        List<UserLog> allLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .in(UserLog::getUserId, 100001L, 100002L)
                .in(UserLog::getType, 
                    UserLogType.LOCK_RELEASE_LINEAR.getCode(),
                    UserLogType.LOCK_RELEASE_ONETIME.getCode(),
                    UserLogType.LOCK_STATIC_REWARD.getCode(),
                    UserLogType.LOCK_DYNAMIC_REWARD.getCode())
        );
        assertTrue(allLogs.size() >= 2, "应该有释放和收益相关的日志记录");
    }

    @Test
    @DisplayName("用户锁仓汇总查询：应正确统计用户的锁仓信息")
    public void testUserLockSummary() {
        lockOrderService.remove(new LambdaQueryWrapper<LockOrder>().ge(LockOrder::getId, 0));

        Long userId = 100001L;
        
        // 创建多个锁仓订单
        LockOrder order1 = createTestLockOrder(userId, new BigDecimal("1000"), 30, new BigDecimal("100"));
        order1.setAvailableAmount(new BigDecimal("200")); // 设置可提取金额
        lockOrderService.updateById(order1);
        
        LockOrder order2 = createTestLockOrder(userId, new BigDecimal("2000"), 60, new BigDecimal("50"));
        order2.setAvailableAmount(new BigDecimal("300"));
        lockOrderService.updateById(order2);
        
        LockOrder order3 = createTestLockOrder(userId, new BigDecimal("500"), 10, new BigDecimal("50"));
        order3.setStatus(2); // 已完成
        lockOrderService.updateById(order3);
        
        // 测试锁仓总额查询
        BigDecimal totalLock = lockOrderService.getUserTotalLockAmount(userId, "XYC");
        assertTrue(totalLock.compareTo(new BigDecimal("3000")) >= 0, "锁仓总额应该大于等于3000"); // 只统计锁仓中的订单
        
        // 测试可提取金额查询
        BigDecimal available = lockOrderService.getUserAvailableAmount(userId, "XYC");
        assertEquals(0, new BigDecimal("500.00000000").compareTo(available)); // 200 + 300
        
        // 验证查询结果
        List<LockOrder> userOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>().eq(LockOrder::getUserId, userId)
        );
        assertEquals(3, userOrders.size());
        
        long lockingCount = userOrders.stream().filter(o -> o.getStatus() == 1).count();
        long completedCount = userOrders.stream().filter(o -> o.getStatus() == 2).count();
        
        assertEquals(2, lockingCount);
        assertEquals(1, completedCount);
    }

    /**
     * 创建测试用的锁仓订单
     */
    private LockOrder createTestLockOrder(Long userId, BigDecimal lockAmount, Integer totalDays, BigDecimal dailyAmount) {
        LockOrder lockOrder = new LockOrder();
        lockOrder.setOrderNo(lockOrderService.generateLockOrderNo());
        lockOrder.setUserId(userId);
        lockOrder.setSourceType(1);
        lockOrder.setSourceId(1L);
        lockOrder.setTokenId("XYC");
        lockOrder.setLockAmount(lockAmount);
        lockOrder.setReleasedAmount(BigDecimal.ZERO);
        lockOrder.setAvailableAmount(BigDecimal.ZERO);
        lockOrder.setTotalDays(totalDays);
        lockOrder.setReleasedDays(0);
        lockOrder.setDailyReleaseAmount(dailyAmount);
        lockOrder.setStatus(1);
        lockOrder.setCreateTime(new Date());
        
        lockOrderService.save(lockOrder);
        return lockOrder;
    }
}