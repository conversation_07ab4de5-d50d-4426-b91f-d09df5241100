package com.aic.app.job;

import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserProductMapper;
import com.aic.app.model.StakeUser;
import com.aic.app.model.UserProduct;
import com.aic.app.service.IUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@DisplayName("Job3 质押结算集成测试")
@Testcontainers
@SpringBootTest
@Transactional // <-- 添加事务注解，确保每个测试方法结束后数据回滚
public class Job3ContainerTest {

    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8")
            .withDatabaseName("xyc")
            .withUsername("root")
            .withPassword("dbadmin123")
            .withInitScript("test-data.sql");

    @DynamicPropertySource
    static void overrideProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);
    }

    @Resource
    private Job3 job3;

    @Resource
    private IUserService userService;

    @Resource
    private StakeUserMapper stakeUserMapper;

    @Resource
    private UserProductMapper userProductMapper;


    @Test
    @DisplayName("T+1结算：应将待确认质押转为已确认")
    public void testSettlePendingStake() {
        // 准备：从 test-data.sql 中我们知道 user 100002 有 500 的待确认金额
        StakeUser before = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 100002)
        );
        assertNotNull(before);
        assertEquals(0, new BigDecimal("500").compareTo(before.getPendingAmount()));
        assertEquals(0, new BigDecimal("5000").compareTo(before.getCurrentAmount()));

        // 执行
        job3.run();

        // 验收
        StakeUser after = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 100002)
        );
        assertNotNull(after);
        assertEquals(0, BigDecimal.ZERO.compareTo(after.getPendingAmount()), "待确认金额应归零");
        assertEquals(0, new BigDecimal("5500").compareTo(after.getCurrentAmount()), "已确认金额应增加500");
    }

    @Test
    @DisplayName("静态收益计算：应为所有质押用户计算静态收益")
    public void testCalculateStaticReward() {
        // 准备：user 100004 初始质押10000，静态池子100，收益率1%
        StakeUser before = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 100004)
        );
        assertNotNull(before);
        assertEquals(0, new BigDecimal("10000").compareTo(before.getCurrentAmount()));
        assertEquals(0, new BigDecimal("100").compareTo(before.getStaticPool()));

        // 执行
        job3.run();

        // 验收
        StakeUser after = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 100004)
        );
        assertNotNull(after);
        // 预期收益 = (已确认质押 + 静态池子) * 收益率 = (10000 + 100) * 0.01 = 101
        BigDecimal expectedReward = new BigDecimal("101.00000000");
        // 预期新的静态池子 = 100 (原有) + 101 (新收益) = 201
        BigDecimal expectedNewStaticPool = new BigDecimal("201.00000000");

        assertEquals(0, expectedReward.compareTo(after.getTodayStatic()), "今日静态收益计算应正确");
        assertEquals(0, expectedNewStaticPool.compareTo(after.getStaticPool()), "静态池子应正确累加");
    }

    @Test
    @DisplayName("普通商品线性释放：应正确计算每日释放金额")
    public void testNormalProductLinearRelease() {
        // 准备: user 100003 的订单 'NORMAL_ORDER_001'，总额3600，每日应释放 10
        UserProduct before = userProductMapper.selectOne(
                new LambdaQueryWrapper<UserProduct>().eq(UserProduct::getOrderNo, "NORMAL_ORDER_001")
        );
        assertNotNull(before, "数据库中应该存在订单 NORMAL_ORDER_001");
        assertEquals(0, BigDecimal.ZERO.compareTo(before.getAvailableAmount())); // 初始可提取为0

        // 执行
        job3.run();

        // 验收
        UserProduct after = userProductMapper.selectOne(
                new LambdaQueryWrapper<UserProduct>().eq(UserProduct::getOrderNo, "NORMAL_ORDER_001")
        );
        assertNotNull(after);
        // 预期每日释放 = 3600 / 360 = 10
        BigDecimal expectedRelease = new BigDecimal("10.00000000");
        assertEquals(0, expectedRelease.compareTo(after.getAvailableAmount()), "可提取金额应为每日释放量");
        assertEquals(0, expectedRelease.compareTo(after.getTotalReleased()), "累计释放金额也应更新");
    }
}
