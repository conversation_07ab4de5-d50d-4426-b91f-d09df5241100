package com.aic.app.job;

import com.aic.app.model.*;
import com.aic.app.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 锁仓订单系统简化测试
 * 不依赖Testcontainers，使用内存数据库进行快速测试
 */
@SpringBootTest
@Transactional
@DisplayName("锁仓订单系统完整测试")
public class LockOrderJobSimpleTest {

    @Resource
    private LockOrderJob lockOrderJob;

    @Resource
    private ILockOrderService lockOrderService;

    @Resource
    private IProductPurchaseService productPurchaseService;

    @Resource
    private IProductService productService;

    @Resource
    private IUserService userService;

    @Resource
    private IUserLogService userLogService;

    private User testUser1;
    private User testUser2;
    private User testUser3;
    private User testUser4;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser1 = createTestUser(200001L, "TEST_USER_001");
        testUser2 = createTestUser(200002L, "TEST_USER_002");
        testUser3 = createTestUser(200003L, "TEST_USER_003");
        testUser4 = createTestUser(200004L, "TEST_USER_004");
    }

    @Test
    @DisplayName("完整流程测试：购买不同类型产品并验证锁仓订单创建")
    public void testCompleteProductPurchaseFlow() {
        // 1. 测试节点产品购买
        Product nodeProduct = productService.getById(1);
        UserProduct nodeOrder = productPurchaseService.purchaseProduct(
            testUser1, nodeProduct, new BigDecimal("3600"), 1);
        
        // 验证节点产品锁仓订单
        LockOrder nodeLockOrder = findLockOrderBySource(nodeOrder.getId(), 1);
        assertNotNull(nodeLockOrder, "节点产品应创建锁仓订单");
        assertEquals(1, nodeLockOrder.getSourceType());
        // 打印每日释放
        System.out.println("节点产品每日释放: " + nodeLockOrder.getDailyReleaseAmount());

        assertEquals(0, new BigDecimal("10").compareTo(nodeLockOrder.getDailyReleaseAmount()));
        
        // 2. 测试普通商品购买
        Product normalProduct = productService.getById(7);
        UserProduct normalOrder = productPurchaseService.purchaseProduct(
            testUser2, normalProduct, new BigDecimal("3600"), 1);
        
        // 验证普通商品锁仓订单
        LockOrder normalLockOrder = findLockOrderBySource(normalOrder.getId(), 2);
        assertNotNull(normalLockOrder, "普通商品应创建锁仓订单");
        assertEquals(2, normalLockOrder.getSourceType());
        assertEquals(360, normalLockOrder.getTotalDays()); // 固定360天
        assertEquals(0, new BigDecimal("10.00000000").compareTo(normalLockOrder.getDailyReleaseAmount()));
        
        // 3. 测试债券产品购买
        Product bondProduct = productService.getById(1002);
        UserProduct bondOrder = productPurchaseService.purchaseProduct(
            testUser3, bondProduct, new BigDecimal("1800"), 1);
        
        // 验证债券产品锁仓订单
        LockOrder bondLockOrder = findLockOrderBySource(bondOrder.getId(), 3);
        assertNotNull(bondLockOrder, "债券产品应创建锁仓订单");
        assertEquals(3, bondLockOrder.getSourceType());
        assertEquals(180, bondLockOrder.getTotalDays());
        assertEquals(0, new BigDecimal("10.00000000").compareTo(bondLockOrder.getDailyReleaseAmount()));
        
        // 4. 测试城主商品购买
        Product cityLordProduct = productService.getById(2001);
        assertEquals(5, testUser4.getQuadrupleRewardTimes());

        UserProduct cityLordOrder = productPurchaseService.purchaseProduct(
            testUser4, cityLordProduct, new BigDecimal("0"), 1);

        // 验证用户应该有7次4倍加速
        testUser4 = userService.getById(testUser4.getId());
        assertEquals(7, testUser4.getQuadrupleRewardTimes());
        
        // 验证城主商品不创建锁仓订单
        LockOrder cityLordLockOrder = findLockOrderBySource(cityLordOrder.getId(), 4);
        assertNull(cityLordLockOrder, "城主商品不应创建锁仓订单");
        
        // 验证产品已售数量更新
        assertEquals(0, new BigDecimal("1").compareTo(productService.getById(1).getSold()));
        assertEquals(0, new BigDecimal("1").compareTo(productService.getById(7).getSold()));
        assertEquals(0, new BigDecimal("1").compareTo(productService.getById(1002).getSold()));
        assertEquals(0, new BigDecimal("1").compareTo(productService.getById(2001).getSold()));
        
        // 验证日志记录
        verifyPurchaseLogs();
    }

    @Test
    @DisplayName("线性释放完整流程测试：从创建到完成的全过程")
    public void testLinearReleaseCompleteFlow() {
        // 创建一个10天的测试锁仓订单
        LockOrder testOrder = createTestLockOrder(testUser1.getId(), new BigDecimal("1000"), 10);
        
        // 验证初始状态
        assertEquals(0, testOrder.getReleasedDays());
        assertEquals(0, BigDecimal.ZERO.compareTo(testOrder.getReleasedAmount()));
        assertEquals(0, BigDecimal.ZERO.compareTo(testOrder.getAvailableAmount()));
        assertEquals(1, testOrder.getStatus());
        
        // 模拟10天的释放过程
        for (int day = 1; day <= 10; day++) {
            // 执行线性释放
            lockOrderService.processLinearRelease();
            
            // 验证每天的释放结果
            LockOrder updated = lockOrderService.getById(testOrder.getId());
            assertEquals(day, updated.getReleasedDays(), "第" + day + "天释放天数应正确");
            
            if (day < 10) {
                // 前9天，每天释放100
                assertEquals(0, new BigDecimal(day * 100).compareTo(updated.getReleasedAmount()),
                    "第" + day + "天累计释放金额应正确");
                assertEquals(0, new BigDecimal(day * 100).compareTo(updated.getAvailableAmount()),
                    "第" + day + "天可提取金额应正确");
                assertEquals(1, updated.getStatus(), "第" + day + "天状态应为锁仓中");
            } else {
                // 第10天，全部释放完成
                assertEquals(0, new BigDecimal("1000").compareTo(updated.getReleasedAmount()),
                    "最后一天应释放全部金额");
                assertEquals(0, new BigDecimal("1000").compareTo(updated.getAvailableAmount()),
                    "最后一天可提取金额应为全部金额");
                assertEquals(2, updated.getStatus(), "最后一天状态应为已完成");
            }
        }
        
        // 验证释放日志记录
        List<UserLog> releaseLogs = userLogService.list(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, testUser1.getId())
                .eq(UserLog::getType, UserLogType.LOCK_RELEASE_LINEAR.getCode())
        );
        assertEquals(10, releaseLogs.size(), "应该有10条释放日志");
        
        // 验证每条日志的金额
        for (int i = 0; i < 10; i++) {
            UserLog log = releaseLogs.get(i);
            if (i < 9) {
                assertEquals(0, new BigDecimal("100").compareTo(log.getAmount()),
                    "前9天每条日志金额应为100");
            } else {
                assertEquals(0, new BigDecimal("100").compareTo(log.getAmount()),
                    "最后一天日志金额应为100");
            }
            assertTrue(log.getRemark().contains("锁仓订单线性释放"), "日志备注应包含释放信息");
        }
    }


    @Test
    @DisplayName("静态收益计算测试")
    public void testStaticRewardCalculation() {
        // 创建有可提取金额的锁仓订单
        LockOrder rewardOrder = createTestLockOrder(testUser3.getId(), new BigDecimal("10000"), 100);
        rewardOrder.setAvailableAmount(new BigDecimal("1000")); // 设置可提取金额
        lockOrderService.updateById(rewardOrder);
        
        // 执行静态收益计算
        lockOrderService.calculateStaticRewards();
        
        // 验证静态收益日志
        List<UserLog> rewardLogs = userLogService.list(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, testUser3.getId())
                .eq(UserLog::getType, UserLogType.LOCK_STATIC_REWARD.getCode())
        );
        assertEquals(1, rewardLogs.size());
        
        // 预期收益 = 1000 * 0.01 = 10
        assertEquals(0, new BigDecimal("10").compareTo(rewardLogs.get(0).getAmount()));
        assertTrue(rewardLogs.get(0).getRemark().contains("锁仓静态收益"));
    }

    @Test
    @DisplayName("完整Job执行测试：处理多种类型的释放和收益")
    public void testCompleteJobExecution() {
        // 准备多种类型的锁仓订单
        LockOrder linearOrder = createTestLockOrder(testUser1.getId(), new BigDecimal("1000"), 10);
        
        LockOrder oneTimeOrder = createTestLockOrder(testUser2.getId(), new BigDecimal("2000"), 5);
        oneTimeOrder.setReleasedDays(5); // 已到期
        lockOrderService.updateById(oneTimeOrder);
        
        LockOrder rewardOrder = createTestLockOrder(testUser3.getId(), new BigDecimal("5000"), 50);
        rewardOrder.setAvailableAmount(new BigDecimal("500")); // 有可提取金额
        lockOrderService.updateById(rewardOrder);
        
        // 记录执行前的状态
        int initialLinearDays = linearOrder.getReleasedDays();
        int initialOneTimeStatus = oneTimeOrder.getStatus();
        
        // 执行完整的Job
        lockOrderJob.run8();
        
        // 验证线性释放处理
        LockOrder afterLinear = lockOrderService.getById(linearOrder.getId());
        assertEquals(initialLinearDays + 1, afterLinear.getReleasedDays());
        assertEquals(0, new BigDecimal("100").compareTo(afterLinear.getAvailableAmount()));

        // 验证一次性释放处理
        LockOrder afterOneTime = lockOrderService.getById(oneTimeOrder.getId());
        assertEquals(2, afterOneTime.getStatus()); // 应该变为已完成
        assertEquals(0, new BigDecimal("2000").compareTo(afterOneTime.getAvailableAmount()));
        
        // 验证有相关的日志记录
        List<UserLog> allLogs = userLogService.list(
            new LambdaQueryWrapper<UserLog>()
                .in(UserLog::getUserId, testUser1.getId(), testUser2.getId(), testUser3.getId())
                .in(UserLog::getType, 
                    UserLogType.LOCK_RELEASE_LINEAR.getCode(),
                    UserLogType.LOCK_RELEASE_ONETIME.getCode(),
                    UserLogType.LOCK_STATIC_REWARD.getCode())
        );
        assertTrue(allLogs.size() >= 2, "应该有释放和收益相关的日志记录");
    }

    @Test
    @DisplayName("用户锁仓汇总统计测试")
    public void testUserLockSummaryStatistics() {
        Long userId = testUser1.getId();
        
        // 创建多个不同状态的锁仓订单
        LockOrder order1 = createTestLockOrder(userId, new BigDecimal("1000"), 30);
        order1.setAvailableAmount(new BigDecimal("200"));
        lockOrderService.updateById(order1);
        
        LockOrder order2 = createTestLockOrder(userId, new BigDecimal("2000"), 60);
        order2.setAvailableAmount(new BigDecimal("300"));
        lockOrderService.updateById(order2);
        
        LockOrder order3 = createTestLockOrder(userId, new BigDecimal("500"), 10);
        order3.setStatus(2); // 已完成
        order3.setAvailableAmount(new BigDecimal("500"));
        lockOrderService.updateById(order3);
        
        // 测试锁仓总额查询（只统计锁仓中的订单）
        BigDecimal totalLock = lockOrderService.getUserTotalLockAmount(userId, "XYC");
        assertEquals(0, new BigDecimal("3000").compareTo(totalLock));

        // 测试可提取金额查询（只统计锁仓中的订单）
        BigDecimal available = lockOrderService.getUserAvailableAmount(userId, "XYC");
        assertEquals(0, new BigDecimal("500").compareTo(available)); // 200 + 300
        
        // 验证订单状态统计
        List<LockOrder> userOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>().eq(LockOrder::getUserId, userId)
        );
        assertEquals(3, userOrders.size());
        
        long lockingCount = userOrders.stream().filter(o -> o.getStatus() == 1).count();
        long completedCount = userOrders.stream().filter(o -> o.getStatus() == 2).count();
        
        assertEquals(2, lockingCount);
        assertEquals(1, completedCount);
    }

    // 辅助方法

    private User createTestUser(Long userId, String code) {
        User user = new User();
        user.setId(userId);
        user.setCode(code);
        user.setType(1);
        user.setLevel(1);
        user.setQuadrupleRewardTimes(5);
        user.setCreateTime(new Date());
        userService.save(user);
        return user;
    }

    private Product createTestProduct(Long productId, Integer type, String name, Integer day, BigDecimal rate) {
        Product product = new Product();
        product.setId(productId);
        product.setType(type);
        product.setName(name);
        product.setDay(day);
        product.setRate(rate);
        product.setPrice(new BigDecimal("1000"));
        product.setFee(BigDecimal.ZERO);
        product.setEnable(true);
        product.setSold(BigDecimal.ZERO);
        productService.save(product);
        return product;
    }

    private LockOrder createTestLockOrder(Long userId, BigDecimal lockAmount, Integer totalDays) {
        LockOrder lockOrder = new LockOrder();
        lockOrder.setOrderNo(lockOrderService.generateLockOrderNo());
        lockOrder.setUserId(userId);
        lockOrder.setSourceType(1);
        lockOrder.setSourceId(1L);
        lockOrder.setTokenId("XYC");
        lockOrder.setLockAmount(lockAmount);
        lockOrder.setReleasedAmount(BigDecimal.ZERO);
        lockOrder.setAvailableAmount(BigDecimal.ZERO);
        lockOrder.setTotalDays(totalDays);
        lockOrder.setReleasedDays(0);
        lockOrder.setDailyReleaseAmount(lockAmount.divide(new BigDecimal(totalDays), 8, BigDecimal.ROUND_HALF_UP));
        lockOrder.setStatus(1);
        lockOrder.setCreateTime(new Date());
        
        lockOrderService.save(lockOrder);
        return lockOrder;
    }

    private LockOrder findLockOrderBySource(Long sourceId, Integer sourceType) {
        return lockOrderService.getOne(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getSourceId, sourceId)
                .eq(LockOrder::getSourceType, sourceType)
        );
    }

    private void verifyPurchaseLogs() {
        // 验证产品购买日志
        List<UserLog> purchaseLogs = userLogService.list(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getType, UserLogType.PRODUCT_PURCHASE.getCode())
        );
        assertEquals(4, purchaseLogs.size(), "应该有4条产品购买日志");

        // 验证城主商品相关日志
        List<UserLog> cityLordLogs = userLogService.list(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getType, UserLogType.CITY_LORD_PURCHASE.getCode())
        );
        assertEquals(1, cityLordLogs.size(), "应该有1条城主商品购买日志");
        
        List<UserLog> quadrupleLogs = userLogService.list(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getType, UserLogType.QUADRUPLE_REWARD_ADD.getCode())
        );
        assertEquals(1, quadrupleLogs.size(), "应该有1条4倍收益次数增加日志");
        assertEquals(0, new BigDecimal("2").compareTo(quadrupleLogs.get(0).getAmount()));

    }

    // test update index
    @Test
    @DisplayName("更新当前指数测试")
    public void testUpdateCurrentIndex() {
        lockOrderJob.updateCurrentIndex();
    }

    // test updateTotalAmount
    @Test
    @DisplayName("更新全网总只有量测试")
    public void testUpdateTotalAmount() {
        lockOrderJob.updateTotalAmount();
    }
}