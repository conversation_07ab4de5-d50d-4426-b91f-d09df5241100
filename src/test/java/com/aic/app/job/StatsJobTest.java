package com.aic.app.job;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Transactional
class StatsJobTest {

    @Resource
    StatsJob statsJob;

    @Test
    void testSaveXycPriceToRedis() {
        statsJob.saveXycPriceToRedis();
    }

    @Test
    void testSaveXycStakePrincipalToRedis() {
        statsJob.saveXycStakePrincipalToRedis();
    }

}