package com.aic.app.job;

import com.aic.app.model.User;
import com.aic.app.service.IUserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.io.Serializable;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class BaseTest {

    @Resource
    public MockMvc mockMvc;
   @Resource
   protected IUserService userService;
    @Resource
    protected StringRedisTemplate stringRedisTemplate;
    
    public String token = "";
    

    protected void login(Serializable id) {
//        System.out.println("get user " + userService);
       User phoneUser = userService.getById(id);
       System.out.println("user = " + phoneUser);
        String token = UUID.randomUUID().toString();
//        System.out.println("set token =" + token);
//        Long uid = phoneUser.getId();
        Long uid = Long.parseLong(id.toString());
        stringRedisTemplate.opsForValue().set("xyc:user:token:" + token, uid.toString(), Duration.ofHours(2));
        stringRedisTemplate.opsForHash().putAll("xyc:user:" + uid,
                Map.of("token", token));
        stringRedisTemplate.expire("xyc:user:" + uid, Duration.ofDays(30));
//        System.out.println("login success");
        this.token = token;
    }

    // 写一个方法格式化输出json字符串
    private String formatJson(String json) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Object jsonObject = objectMapper.readValue(json, Object.class);
        String formattedJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
        return formattedJson;
    }

    public void printJson(String json) throws Exception {
        String formattedJson = formatJson(json);
        System.out.println(formattedJson);
    }

    public void login2(String code) throws Exception {
        // post /api/login?code=31
        MvcResult result = mockMvc.perform(post("/api/login")
                        .param("code", code))
                .andExpect(status().isOk())
                .andReturn();
        String json = result.getResponse().getContentAsString();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> jsonObject = (Map<String, Object>) objectMapper.readValue(json, Object.class);
        Map<String, Map<String, Object>> data = (Map<String, Map<String, Object>>) jsonObject.get("data");
        this.token = data.get("token") + "";
    }


}
