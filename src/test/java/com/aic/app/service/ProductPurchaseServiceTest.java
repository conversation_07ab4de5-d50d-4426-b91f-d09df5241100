package com.aic.app.service;

import com.aic.app.model.*;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 产品购买服务测试
 */
@SpringBootTest
@ActiveProfiles("testcontainers")
@Transactional
public class ProductPurchaseServiceTest {
    
    @Resource
    private IProductPurchaseService productPurchaseService;
    
    @Resource
    private IProductService productService;
    
    @Resource
    private ILockOrderService lockOrderService;
    
    @Test
    public void testPurchaseNodeProduct() {
        // 创建节点产品
        Product product = new Product();
        product.setId(1L);
        product.setType(1);
        product.setName("节点产品A");
        product.setPrice(new BigDecimal("1000"));
        product.setRate(new BigDecimal("0.01"));
        product.setDay(30);
        product.setSold(BigDecimal.ZERO);
        productService.save(product);
        
        // 创建用户
        User user = new User();
        user.setId(1L);
        
        // 购买产品
        UserProduct userProduct = productPurchaseService.purchaseProduct(
            user, product, new BigDecimal("1000"), 1);
        
        // 验证购买记录
        assertNotNull(userProduct);
        assertEquals(1L, userProduct.getUserId());
        assertEquals(1L, userProduct.getProductId());
        assertEquals(new BigDecimal("1000"), userProduct.getAmount());
        assertEquals(1, userProduct.getQuantity());
        
        // 验证产品已售数量更新
        Product updatedProduct = productService.getById(1L);
        assertEquals(new BigDecimal("1"), updatedProduct.getSold());
        
        // 验证锁仓订单创建
        LockOrder lockOrder = lockOrderService.getOne(
            lockOrderService.lambdaQuery()
                .eq(LockOrder::getSourceId, userProduct.getId())
                .eq(LockOrder::getSourceType, 1)
        );
        assertNotNull(lockOrder);
        assertEquals(new BigDecimal("1000"), lockOrder.getLockAmount());
        assertEquals(1, lockOrder.getSourceType());
    }
    
    @Test
    public void testPurchaseCityLordProduct() {
        // 创建城主商品
        Product product = new Product();
        product.setId(4L);
        product.setType(4);
        product.setName("城主商品");
        product.setPrice(new BigDecimal("100"));
        product.setSold(BigDecimal.ZERO);
        productService.save(product);
        
        // 创建用户
        User user = new User();
        user.setId(1L);
        
        // 购买城主商品
        UserProduct userProduct = productPurchaseService.purchaseProduct(
            user, product, new BigDecimal("100"), 1);
        
        // 验证购买记录
        assertNotNull(userProduct);
        assertEquals(4L, userProduct.getProductId());
        
        // 验证产品已售数量更新
        Product updatedProduct = productService.getById(4L);
        assertEquals(new BigDecimal("1"), updatedProduct.getSold());
        
        // 验证没有创建锁仓订单（城主商品不创建锁仓订单）
        LockOrder lockOrder = lockOrderService.getOne(
            lockOrderService.lambdaQuery()
                .eq(LockOrder::getSourceId, userProduct.getId())
                .eq(LockOrder::getSourceType, 4)
        );
        assertNull(lockOrder);
    }
    
    @Test
    public void testUpdateProductSoldQuantity() {
        // 创建产品
        Product product = new Product();
        product.setId(1L);
        product.setType(1);
        product.setName("测试产品");
        product.setPrice(new BigDecimal("100"));
        product.setSold(new BigDecimal("5"));
        productService.save(product);
        
        // 更新已售数量
        productPurchaseService.updateProductSoldQuantity(1L, 3);
        
        // 验证更新结果
        Product updatedProduct = productService.getById(1L);
        assertEquals(new BigDecimal("8"), updatedProduct.getSold());
    }
}