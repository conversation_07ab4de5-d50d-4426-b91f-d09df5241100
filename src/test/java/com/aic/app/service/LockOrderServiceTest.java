package com.aic.app.service;

import com.aic.app.model.*;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 锁仓订单服务测试
 */
@SpringBootTest
@ActiveProfiles("testcontainers")
@Transactional
public class LockOrderServiceTest {
    
    @Resource
    private ILockOrderService lockOrderService;
    
    @Resource
    private IProductPurchaseService productPurchaseService;
    
    @Test
    public void testCreateNodeLockOrder() {
        // 创建节点产品
        Product product = new Product();
        product.setId(1L);
        product.setType(1);
        product.setName("节点产品A");
        product.setPrice(new BigDecimal("1000"));
        product.setRate(new BigDecimal("0.01"));
        product.setDay(30);
        
        // 创建用户购买记录
        UserProduct userProduct = new UserProduct();
        userProduct.setId(1L);
        userProduct.setUserId(1L);
        userProduct.setOrderNo("TEST001");
        userProduct.setProductId(1L);
        userProduct.setAmount(new BigDecimal("1000"));
        userProduct.setQuantity(1);
        
        // 创建锁仓订单
        LockOrder lockOrder = lockOrderService.createLockOrderByProduct(userProduct, product, new BigDecimal("1000"));
        
        // 验证锁仓订单
        assertNotNull(lockOrder);
        assertEquals(1, lockOrder.getSourceType()); // 节点产品
        assertEquals(30, lockOrder.getTotalDays()); // 30天
        assertEquals(new BigDecimal("1000"), lockOrder.getLockAmount());
        assertEquals(0, lockOrder.getReleasedDays());
        assertEquals(1, lockOrder.getStatus()); // 锁仓中
    }
    
    @Test
    public void testCreateNormalProductLockOrder() {
        // 创建普通商品
        Product product = new Product();
        product.setId(2L);
        product.setType(2);
        product.setName("普通商品A");
        product.setPrice(new BigDecimal("500"));
        product.setDay(1);
        
        UserProduct userProduct = new UserProduct();
        userProduct.setId(2L);
        userProduct.setUserId(1L);
        userProduct.setOrderNo("TEST002");
        userProduct.setProductId(2L);
        userProduct.setAmount(new BigDecimal("500"));
        userProduct.setQuantity(1);
        
        // 创建锁仓订单
        LockOrder lockOrder = lockOrderService.createLockOrderByProduct(userProduct, product, new BigDecimal("500"));
        
        // 验证锁仓订单
        assertNotNull(lockOrder);
        assertEquals(2, lockOrder.getSourceType()); // 普通商品
        assertEquals(360, lockOrder.getTotalDays()); // 固定360天
        assertEquals(new BigDecimal("500"), lockOrder.getLockAmount());
    }
    
    @Test
    public void testProcessLinearRelease() {
        // 创建一个线性释放的锁仓订单
        LockOrder lockOrder = new LockOrder();
        lockOrder.setOrderNo("LOCK_TEST001");
        lockOrder.setUserId(1L);
        lockOrder.setSourceType(1);
        lockOrder.setSourceId(1L);
        lockOrder.setTokenId("XYC");
        lockOrder.setLockAmount(new BigDecimal("1000"));
        lockOrder.setReleasedAmount(BigDecimal.ZERO);
        lockOrder.setAvailableAmount(BigDecimal.ZERO);
        lockOrder.setTotalDays(10);
        lockOrder.setReleasedDays(0);
        lockOrder.setDailyReleaseAmount(new BigDecimal("100"));
        lockOrder.setStatus(1);
        
        lockOrderService.save(lockOrder);
        
        // 执行线性释放
        lockOrderService.processLinearRelease();
        
        // 验证释放结果
        LockOrder updated = lockOrderService.getById(lockOrder.getId());
        assertEquals(1, updated.getReleasedDays());
        assertEquals(new BigDecimal("100"), updated.getReleasedAmount());
        assertEquals(new BigDecimal("100"), updated.getAvailableAmount());
        assertEquals(1, updated.getStatus()); // 还在锁仓中
    }
    
    @Test
    public void testGenerateLockOrderNo() {
        String orderNo1 = lockOrderService.generateLockOrderNo();
        String orderNo2 = lockOrderService.generateLockOrderNo();
        
        assertNotNull(orderNo1);
        assertNotNull(orderNo2);
        assertNotEquals(orderNo1, orderNo2);
        assertTrue(orderNo1.startsWith("LOCK"));
        assertTrue(orderNo2.startsWith("LOCK"));
    }
    
    @Test
    public void testGetUserLockAmount() {
        // 创建测试数据
        LockOrder lockOrder1 = new LockOrder();
        lockOrder1.setOrderNo("LOCK_TEST001");
        lockOrder1.setUserId(1L);
        lockOrder1.setSourceType(1);
        lockOrder1.setTokenId("XYC");
        lockOrder1.setLockAmount(new BigDecimal("1000"));
        lockOrder1.setAvailableAmount(new BigDecimal("100"));
        lockOrder1.setStatus(1);
        
        LockOrder lockOrder2 = new LockOrder();
        lockOrder2.setOrderNo("LOCK_TEST002");
        lockOrder2.setUserId(1L);
        lockOrder2.setSourceType(2);
        lockOrder2.setTokenId("XYC");
        lockOrder2.setLockAmount(new BigDecimal("500"));
        lockOrder2.setAvailableAmount(new BigDecimal("50"));
        lockOrder2.setStatus(1);
        
        lockOrderService.save(lockOrder1);
        lockOrderService.save(lockOrder2);
        
        // 测试获取用户锁仓总额
        BigDecimal totalLock = lockOrderService.getUserTotalLockAmount(1L, "XYC");
        assertEquals(new BigDecimal("1500"), totalLock);
        
        // 测试获取用户可提取金额
        BigDecimal available = lockOrderService.getUserAvailableAmount(1L, "XYC");
        assertEquals(new BigDecimal("150"), available);
    }
}