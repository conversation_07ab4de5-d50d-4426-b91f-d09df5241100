package com.aic.app.service;

import com.aic.app.form.WithdrawNormalProductForm;
import com.aic.app.model.User;
import com.aic.app.model.UserProduct;
import com.aic.app.model.Withdraw;
import com.aic.app.service.RustService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 普通商品提取功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class NormalProductWithdrawTest {

    @Resource
    private IUserProductService userProductService;
    
    @Resource
    private IUserService userService;
    
    @Resource
    private IWithdrawService withdrawService;

    @Test
    public void testNormalProductWithdraw() {
        System.out.println("=== 测试普通商品提取功能 ===");
        
        // 1. 创建测试用户
        User testUser = createTestUser();
        
        // 2. 创建测试普通商品订单（有可提取金额）
        UserProduct testProduct = createTestNormalProductWithAvailableAmount(testUser.getId());
        
        // 3. 创建提取表单
        WithdrawNormalProductForm form = new WithdrawNormalProductForm();
        form.setUserProductId(testProduct.getId().longValue());
        form.setAmount(new BigDecimal("50")); // 提取50
        form.setTokenId("XYC");
        
        try {
            // 4. 执行提取
            RustService.WithdrawSign withdrawSign = userProductService.withdrawNormalProductWallet(testUser, form);
            
            // 5. 验证结果
            System.out.println("提取签名生成成功:");
            System.out.println("  地址: " + withdrawSign.address);
            System.out.println("  金额: " + withdrawSign.amount);
            System.out.println("  类型: " + withdrawSign.type);
            System.out.println("  随机数: " + withdrawSign.nonce);
            
            // 6. 验证订单可提取金额是否正确扣减
            UserProduct updatedProduct = userProductService.getById(testProduct.getId());
            System.out.println("扣减后可提取金额: " + updatedProduct.getAvailableAmount());
            
            // 7. 验证提取记录是否创建
            Withdraw withdraw = withdrawService.list().stream()
                .filter(w -> w.getUserId().equals(testUser.getId()) && w.getType() == 5)
                .findFirst()
                .orElse(null);
            
            if (withdraw != null) {
                System.out.println("提取记录创建成功:");
                System.out.println("  提取ID: " + withdraw.getId());
                System.out.println("  提取金额: " + withdraw.getQuantity());
                System.out.println("  订单ID: " + withdraw.getProductId());
                System.out.println("  状态: " + withdraw.getState());
            }
            
        } catch (Exception e) {
            System.err.println("提取失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }

    @Test
    public void testWithdrawInsufficientAmount() {
        System.out.println("=== 测试可提取金额不足的情况 ===");
        
        User testUser = createTestUser();
        UserProduct testProduct = createTestNormalProductWithAvailableAmount(testUser.getId());
        
        WithdrawNormalProductForm form = new WithdrawNormalProductForm();
        form.setUserProductId(testProduct.getId().longValue());
        form.setAmount(new BigDecimal("200")); // 提取200，超过可提取金额100
        form.setTokenId("XYC");
        
        try {
            userProductService.withdrawNormalProductWallet(testUser, form);
            System.err.println("应该抛出异常但没有抛出");
        } catch (Exception e) {
            System.out.println("正确抛出异常: " + e.getMessage());
        }
        
        System.out.println("=== 测试完成 ===");
    }

    /**
     * 创建测试用户
     */
    private User createTestUser() {
        User user = new User();
        user.setId(20001L);
        user.setAddress("0xTestAddress123456789");
        user.setCode("TEST" + System.currentTimeMillis());
        user.setType(1); // 钱包用户
        user.setCreateTime(new Date());
        
        userService.save(user);
        return user;
    }

    /**
     * 创建有可提取金额的普通商品订单
     */
    private UserProduct createTestNormalProductWithAvailableAmount(Long userId) {
        UserProduct userProduct = new UserProduct();
        userProduct.setUserId(userId);
        userProduct.setOrderNo("WITHDRAW_TEST" + System.currentTimeMillis());
        userProduct.setProductId(999L);
        userProduct.setType(2); // 普通商品
        userProduct.setAmount(new BigDecimal("3600")); // 总金额3600
        userProduct.setPrice(new BigDecimal("100"));
        userProduct.setQuantity(36);
        userProduct.setStatus(1); // 有效状态
        userProduct.setDay(360);
        userProduct.setReleaseDay(0);
        userProduct.setRate(new BigDecimal("0.004"));
        userProduct.setCreateTime(new Date());
        
        // 设置线性释放相关字段
        userProduct.setDailyReleaseAmount(new BigDecimal("10")); // 每日释放10
        userProduct.setTotalReleased(new BigDecimal("100")); // 已释放100
        userProduct.setAvailableAmount(new BigDecimal("100")); // 可提取100
        userProduct.setLastReleaseDate(new Date());
        
        userProductService.save(userProduct);
        
        System.out.println("创建测试订单: " + userProduct.getOrderNo() + 
                          ", 可提取金额: " + userProduct.getAvailableAmount());
        
        return userProduct;
    }
}
