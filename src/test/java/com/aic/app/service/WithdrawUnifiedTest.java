package com.aic.app.service;

import com.aic.app.form.WithdrawUnifiedForm;
import com.aic.app.model.User;
import com.aic.app.model.LockOrder;
import com.aic.app.model.StakeUser;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

/**
 * 统一提取功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class WithdrawUnifiedTest {

    @Resource
    private ILockOrderService lockOrderService;
    
    @Resource
    private IStakeUserService stakeUserService;
    
    @Resource
    private IUserService userService;

    @Test
    public void testWithdrawLockOrderRelease() {
        System.out.println("=== 测试锁仓释放提取功能 ===");
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // 创建测试锁仓订单
        LockOrder testLockOrder = createTestLockOrder(testUser.getId());
        
        try {
            // 执行提取
            BigDecimal withdrawAmount = new BigDecimal("50");
            BigDecimal result = lockOrderService.withdrawLockOrderRelease(
                testUser.getId(), testLockOrder.getId(), withdrawAmount);
            
            System.out.println("提取成功，金额: " + result);
            
        } catch (Exception e) {
            System.out.println("提取失败: " + e.getMessage());
        }
    }

    @Test
    public void testWithdrawStakeStaticPool() {
        System.out.println("=== 测试质押静态池提取功能 ===");
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // 创建测试质押用户
        StakeUser testStakeUser = createTestStakeUser(testUser.getId());
        
        try {
            // 执行提取
            BigDecimal withdrawAmount = new BigDecimal("30");
            BigDecimal result = stakeUserService.withdrawStakeStaticPool(
                testUser.getId(), "XYC", withdrawAmount);
            
            System.out.println("提取成功，金额: " + result);
            
        } catch (Exception e) {
            System.out.println("提取失败: " + e.getMessage());
        }
    }

    private User createTestUser() {
        User user = new User();
        user.setAddress("0x1234567890123456789012345678901234567890");
        user.setType(1);
        userService.save(user);
        return user;
    }

    private LockOrder createTestLockOrder(Long userId) {
        LockOrder lockOrder = new LockOrder();
        lockOrder.setOrderNo("TEST" + System.currentTimeMillis());
        lockOrder.setUserId(userId);
        lockOrder.setSourceType(2);
        lockOrder.setSourceId(1L);
        lockOrder.setTokenId("XYC");
        lockOrder.setLockAmount(new BigDecimal("1000"));
        lockOrder.setReleasedAmount(new BigDecimal("200"));
        lockOrder.setAvailableAmount(new BigDecimal("100"));
        lockOrder.setTotalDays(180);
        lockOrder.setReleasedDays(36);
        lockOrder.setStatus(1);
        lockOrderService.save(lockOrder);
        return lockOrder;
    }

    private StakeUser createTestStakeUser(Long userId) {
        StakeUser stakeUser = new StakeUser();
        stakeUser.setUserId(userId);
        stakeUser.setTokenId("XYC");
        stakeUser.setCurrentAmount(new BigDecimal("500"));
        stakeUser.setStaticPool(new BigDecimal("100"));
        stakeUser.setDynamicPool(new BigDecimal("50"));
        stakeUserService.save(stakeUser);
        return stakeUser;
    }
}
