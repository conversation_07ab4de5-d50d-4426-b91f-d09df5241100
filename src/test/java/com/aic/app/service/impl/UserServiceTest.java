package com.aic.app.service.impl;

import com.aic.app.model.User;
import com.aic.app.service.IStakeUserService;
import com.aic.app.service.IUserService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class UserServiceTest {

    @Resource
    IUserService userService;
    @Resource
    IStakeUserService stakeUserService;

    @Test
    public void testInit() throws Exception {
        User user = stakeUserService.checkUser("0x22");
        user.setPid(1L);
        userService.updateById(user);

        user = stakeUserService.checkUser("0x33");
        user.setPid(1L);
        userService.updateById(user);

        user = stakeUserService.checkUser("0x222");
        user.setPid(221469L);
        userService.updateById(user);

    }

}
