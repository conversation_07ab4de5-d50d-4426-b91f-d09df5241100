package com.aic.app.service;

import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.model.*;
import com.aic.app.service.impl.StakeUserServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试质押事件处理
 */
@SpringBootTest
@Slf4j
@Transactional
@Rollback
public class StakeFromWalletTest {

    @Resource
    private StakeUserServiceImpl stakeUserService;
    
    @Resource
    private IEventsService eventsService;
    
    @Resource
    private IUserService userService;
    
    @Resource
    private StakeUserMapper stakeUserMapper;
    
    @Resource
    private BizLogService bizLogService;
    
    @Resource
    private IUserLogService userLogService;

    @Test
    public void testStakeFromWallet() {
        log.info("开始测试质押事件处理");
        
        // 1. 准备测试数据
        String testAddress = "******************************************";
        String contractAddress = "******************************************";
        String txid = "0x1111111111111111111111111111111111111111111111111111111111111111";
        BigDecimal stakeAmount = new BigDecimal("100.00000000");
        
        // 测试资产信息（不需要保存到数据库，在stakeFromWallet中会创建）
        log.info("测试使用资产: tokenId=XYC, stakeAddress={}", contractAddress);
        
        // 2. 创建测试事件
        Events events = new Events();
        events.setName("Stake");
        events.setTxid(txid);
        events.setContract(contractAddress);
        events.setHeight(1000L);
        events.setStatus(0);
        
        Events.Data data = new Events.Data();
        data.setTokenType(0);
        data.setOriAmount("1200");
        data.setAddress(testAddress);
        data.setStakeAmount(stakeAmount.toPlainString());
        data.setTxid(txid);
        events.setData(data);
        
        eventsService.save(events);
        
        // 3. 处理事件
        eventsService.handleEvent(events);
        
        // 4. 验证结果
        // 验证用户是否创建
        User user = userService.findByAddress(testAddress);
        assertNotNull(user, "用户应该被自动创建");
        
        // 验证质押用户记录
        StakeUser stakeUser = stakeUserService.getStakeUser(user.getId(), "XYC");
        assertNotNull(stakeUser, "质押用户记录应该存在");
        assertEquals(stakeAmount, stakeUser.getPendingAmount(), "待确认质押金额应该正确");
        
        // 验证业务日志
        List<BizLog> bizLogs = bizLogService.list(new LambdaQueryWrapper<BizLog>()
                .eq(BizLog::getUserId, user.getId())
                .eq(BizLog::getOperation, "质押"));
        assertFalse(bizLogs.isEmpty(), "应该有业务日志记录");
        
        // 验证用户日志
        List<UserLog> userLogs = userLogService.list(new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, user.getId()));
        assertFalse(userLogs.isEmpty(), "应该有用户日志记录");
        
        // 验证事件状态
        Events processedEvent = eventsService.getById(events.getId());
        assertEquals(1, processedEvent.getStatus(), "事件状态应该被标记为已处理");
        
        log.info("质押事件处理测试完成");
    }
    
}
