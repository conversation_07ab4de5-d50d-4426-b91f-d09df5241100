package com.aic.app.api;

import com.aic.app.AppApplication;
import com.aic.app.form.BindJuidForm;
import com.aic.app.job.BaseTest;
import com.aic.app.model.User;
import com.aic.app.service.IUserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.servlet.HandlerInterceptor;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = AppApplication.class)
@AutoConfigureMockMvc
public class BindJuidApiTest extends BaseTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean // Mock the LoginInterceptor
    private LoginInterceptor loginInterceptor;

    @BeforeEach
    void setUp() throws Exception {
        // Configure the mocked LoginInterceptor to always return true for preHandle
        when(loginInterceptor.preHandle(any(), any(), any())).thenReturn(true);

        // UsewebAppContextSetup to load the full Spring application context
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void testBindJuid() throws Exception {
        Long userId = 1L; // Assume user ID 1 exists or will be created
        // Ensure the user exists for the test
        User user = userService.getById(userId);
        if (user == null) {
            user = new User();
            user.setId(userId);
            user.setAddress("0xTestAddress" + userId);
            userService.add(user);
        }
        // No need to call login(userId) here, as the interceptor is mocked to always pass.
        // We just need to ensure the user exists in the DB for the service layer.

        String newJuid = "test_juid_123";
        BindJuidForm form = new BindJuidForm();
        form.setJuid(newJuid);

        mockMvc.perform(post("/api/user/bind-juid")
                        .header("Authorization", "mock-token") // Still send a token, but it's ignored by mocked interceptor
                        .requestAttr("user", user) // Manually set the user attribute for the controller
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andDo(print()); // Print request and response for debugging

        // Verify that juid in the database has been updated
        User updatedUser = userService.getById(userId);
        assertNotNull(updatedUser);
        assertEquals(newJuid, updatedUser.getJuid());
    }

    @Test
    void testBindJuidWithEmptyJuid() throws Exception {
        Long userId = 2L; // Use a different user ID for this test
        // Ensure the user exists for the test
        User user = userService.getById(userId);
        if (user == null) {
            user = new User();
            user.setId(userId);
            user.setAddress("0xTestAddress" + userId);
            userService.add(user);
        }

        BindJuidForm form = new BindJuidForm();
        form.setJuid(""); // Empty juid, should trigger validation error

        mockMvc.perform(post("/api/user/bind-juid")
                        .header("Authorization", "mock-token")
                        .requestAttr("user", user) // Manually set the user attribute
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isBadRequest()) // Expect 400 Bad Request due to NotBlank validation failure
                .andExpect(jsonPath("$.code").value(110)) // Assuming empty juid returns 110 error code
                .andDo(print());
    }
}
