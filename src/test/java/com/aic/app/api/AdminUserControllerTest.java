package com.aic.app.api;

import com.aic.app.admin.form.LoginForm;
import com.aic.app.model.admin.SysUser;
import com.aic.app.service.admin.ISysUserService;
import com.aic.app.util.JwtUtil;
import com.aic.app.vo.Result;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@SpringBootTest
@AutoConfigureMockMvc
public class AdminUserControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ISysUserService sysUserService;

    private String token = "eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************.gIpvn_GN3nu0prTD9Kxmlbo6RbAHGBiN5YK3QYIVjrU";

    private void login() throws Exception {
        String username = "admin";
        LambdaUpdateWrapper<SysUser> qw = new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getAccount, username);

        SysUser sysUser = sysUserService.getOne(qw);

        Map<String, Object> data = new HashMap<>();
        data.put("id", sysUser.getId());
        data.put("username", sysUser.getAccount());
        String json = objectMapper.writeValueAsString(data);
        String token = JwtUtil.createJWT(json);
        System.out.println("token: " + token);
        this.token = token;
    }

    @Test
    public void testAdminLogin() throws Exception {
        String username = "admin";
        LambdaUpdateWrapper<SysUser> qw = new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getAccount, username);

        SysUser sysUser = sysUserService.getOne(qw);

        Map<String, Object> data = new HashMap<>();
        data.put("id", sysUser.getId());
        data.put("username", sysUser.getAccount());
        String json = objectMapper.writeValueAsString(data);
        String token = JwtUtil.createJWT(json);
        System.out.println("token: " + token);
    }

    @Test
    public void testAdminUserAssets() throws Exception {
        this.login();
        String token = this.token;
        String url = "/admin/user/asset";
        MvcResult result = mockMvc.perform(get(url)
                .header("Authorization", "Bearer " + token))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();
        this.printJson(result.getResponse().getContentAsString());
    }

    // test /admin/user/stake
    @Test
    public void testAdminUserStake() throws Exception {
        this.login();
        String token = this.token;
        String url = "/admin/user/stake";
        MvcResult result = mockMvc.perform(get(url)
                .header("Authorization", "Bearer " + token))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();
        this.printJson(result.getResponse().getContentAsString());
    }

    // test /admin/user/today-amount
    @Test
    public void testAdminUserTodayAmount() throws Exception {
        this.login();
        String token = this.token;
        String url = "/admin/user/today-amount";
        MvcResult result = mockMvc.perform(get(url)
                .header("Authorization", "Bearer " + token))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();
        this.printJson(result.getResponse().getContentAsString());
    }

    // 写一个方法格式化输出json字符串
    private String formatJson(String json) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Object jsonObject = objectMapper.readValue(json, Object.class);
        String formattedJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
        return formattedJson;
    }

    private void printJson(String json) throws Exception {
        String formattedJson = formatJson(json);
        System.out.println(formattedJson);
    }

    // test /admin/user/product with search parameters
    @Test
    public void testAdminUserProduct() throws Exception {
        this.login();
        String token = this.token;
        String url = "/admin/user/product?page=1&size=10";
        MvcResult result = mockMvc.perform(get(url)
                .header("Authorization", "Bearer " + token))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();
        this.printJson(result.getResponse().getContentAsString());
    }

    // test /admin/user/product with search by order number
    @Test
    public void testAdminUserProductWithOrderNo() throws Exception {
        this.login();
        String token = this.token;
        String url = "/admin/user/product?page=1&size=10&orderNo=test123";
        MvcResult result = mockMvc.perform(get(url)
                .header("Authorization", "Bearer " + token))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();
        this.printJson(result.getResponse().getContentAsString());
    }

}
