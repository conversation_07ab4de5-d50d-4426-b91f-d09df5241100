package com.aic.app.api;

import com.aic.app.job.BaseTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import org.springframework.http.MediaType;

@SpringBootTest
@AutoConfigureMockMvc
public class UserControllerTest  extends BaseTest {

    @Resource
    LoginInterceptor loginInterceptor;
    @Resource
    UserController userController;
    @Resource
    UserAddressController userAddressController;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(userController, userAddressController).addInterceptors(loginInterceptor).build();
    }

    // test 邀请记录
    @Test
    public void inviteRecords() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/invite-records?page=1&size=10")
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test /user-log
    @Test
    public void userLog() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/user-log?page=1&size=10")
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test 抢城主接口 - 成功案例（城主产品2001）
    @Test
    public void buyCityLordSuccess() throws Exception {
        login(1);
        String requestBody = "{\"id\": 2001}";
        
        this.mockMvc.perform(post("/api/user/buy-city-lord")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.orderId").exists())
                .andExpect(jsonPath("$.data.productName").exists())
                .andExpect(jsonPath("$.data.addedQuadrupleRewardTimes").value(2))
                .andDo(print());
    }

    // test 抢城主接口 - 失败案例（节点产品1）
    @Test
    public void buyCityLordFailWithNodeProduct() throws Exception {
        login(1);
        String requestBody = "{\"id\": 1}";
        
        this.mockMvc.perform(post("/api/user/buy-city-lord")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("产品类型错误，必须为城主产品"))
                .andDo(print());
    }

    // test 抢城主接口 - 重复抢购失败
    @Test
    public void buyCityLordFailWithDuplicatePurchase() throws Exception {
        login(1);
        String requestBody = "{\"id\": 2001}";
        // 删除redis的key
        stringRedisTemplate.delete("xyc:city_lord_daily:1");
        
        // 第一次抢购 - 应该成功
        this.mockMvc.perform(post("/api/user/buy-city-lord")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(jsonPath("$.code").value(0))
               ;
        
        // 第二次抢购 - 应该失败
        this.mockMvc.perform(post("/api/user/buy-city-lord")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(jsonPath("$.code").value(500))
                ;
    }

    // test 抢城主接口 - 产品不存在
    @Test
    public void buyCityLordFailWithNonExistentProduct() throws Exception {
        login(1);
        String requestBody = "{\"id\": 9999}";
        
        this.mockMvc.perform(post("/api/user/buy-city-lord")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(jsonPath("$.code").value(500));
    }

    // test /stake-records
    @Test
    public void stakeRecords() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/stake/records?page=1&size=10")
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

    // test /lock-order
    @Test
    public void getUserLockOrders() throws Exception {
        login(1);
        this.mockMvc.perform(get("/api/user/lock-order?page=1&size=10")
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print());
    }

}
