package com.aic.app.api;

import com.aic.app.form.AddressForm;
import com.aic.app.job.BaseTest;
import com.aic.app.model.UserAddress;
import com.aic.app.service.IUserAddressService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 用户地址控制器测试类
 * 测试 PhoneAddressController 的四个接口：
 * 1. GET /api/user/address - 我的地址列表
 * 2. POST /api/user/address - 新增地址
 * 3. PUT /api/user/address - 更新地址
 * 4. DELETE /api/user/address/{id} - 删除地址
 */
@SpringBootTest
@AutoConfigureMockMvc
public class UserAddressControllerTest extends BaseTest {

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private IUserAddressService userAddressService;
    
    private Long testUserId = 1L;
    private UserAddress testAddress;

    @BeforeEach
    public void setUp() {
        // 登录测试用户
        login(testUserId);
        
        // 创建测试地址数据
        testAddress = new UserAddress(testUserId, "张三", "+86", "13800138000", "北京市朝阳区测试街道123号");
        userAddressService.save(testAddress);
    }

    /**
     * 测试获取我的地址列表接口
     * GET /api/user/address
     */
    @Test
    public void testGetAddressList() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/user/address")
                        .header("Authorization", token)
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        
        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 获取地址列表测试完成 ===");
    }

    /**
     * 测试获取地址列表 - 分页参数测试
     */
    @Test
    public void testGetAddressListWithPagination() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/user/address")
                        .header("Authorization", token)
                        .param("page", "2")
                        .param("size", "5"))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        
        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 分页参数测试完成 ===");
    }

    /**
     * 测试新增地址接口
     * POST /api/user/address
     */
    @Test
    public void testAddAddress() throws Exception {
        AddressForm addressForm = new AddressForm();
        addressForm.setContact("李四");
        addressForm.setArea("+86");
        addressForm.setPhone("13900139000");
        addressForm.setAddress("上海市浦东新区测试路456号");

        String requestBody = objectMapper.writeValueAsString(addressForm);

        MvcResult result = mockMvc.perform(post("/api/user/address")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 新增地址测试完成 ===");
    }

    /**
     * 测试新增地址 - 必填字段验证
     */
    @Test
    public void testAddAddressWithEmptyFields() throws Exception {
        AddressForm addressForm = new AddressForm();
        // 故意留空一些字段来测试验证

        String requestBody = objectMapper.writeValueAsString(addressForm);

        MvcResult result = mockMvc.perform(post("/api/user/address")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 空字段验证测试完成 ===");
    }

    /**
     * 测试更新地址接口
     * PUT /api/user/address
     */
    @Test
    public void testUpdateAddress() throws Exception {
        AddressForm addressForm = new AddressForm();
        addressForm.setId(testAddress.getId().intValue());
        addressForm.setContact("张三（已更新）");
        addressForm.setArea("+86");
        addressForm.setPhone("13800138001");
        addressForm.setAddress("北京市朝阳区测试街道123号（已更新）");

        String requestBody = objectMapper.writeValueAsString(addressForm);

        MvcResult result = mockMvc.perform(put("/api/user/address")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 更新地址测试完成 ===");
    }

    /**
     * 测试更新不存在的地址
     */
    @Test
    public void testUpdateNonExistentAddress() throws Exception {
        AddressForm addressForm = new AddressForm();
        addressForm.setId(99999); // 不存在的ID
        addressForm.setContact("不存在的地址");
        addressForm.setArea("+86");
        addressForm.setPhone("13800138000");
        addressForm.setAddress("不存在的地址");

        String requestBody = objectMapper.writeValueAsString(addressForm);

        MvcResult result = mockMvc.perform(put("/api/user/address")
                        .header("Authorization", token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 更新不存在地址测试完成 ===");
    }

    /**
     * 测试删除地址接口
     * DELETE /api/user/address/{id}
     */
    @Test
    public void testDeleteAddress() throws Exception {
        MvcResult result = mockMvc.perform(delete("/api/user/address/" + testAddress.getId())
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 删除地址测试完成 ===");
    }

    /**
     * 测试删除不存在的地址
     */
    @Test
    public void testDeleteNonExistentAddress() throws Exception {
        int nonExistentId = 99999;
        
        MvcResult result = mockMvc.perform(delete("/api/user/address/" + nonExistentId)
                        .header("Authorization", token))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 删除不存在地址测试完成 ===");
    }

    /**
     * 测试未登录访问接口
     */
    @Test
    public void testAccessWithoutLogin() throws Exception {
        // 不设置Authorization头，测试未登录访问
        MvcResult result = mockMvc.perform(get("/api/user/address"))
                .andExpect(status().isOk()) // 根据工作区规则，允许未登录访问，返回空数据
                .andDo(print())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        printJson(responseContent);
        System.out.println("=== 未登录访问测试完成 ===");
    }

    /**
     * 测试完整的CRUD流程
     */
    @Test
    @Transactional
    public void testFullCrudFlow() throws Exception {
        System.out.println("=== 开始完整CRUD流程测试 ===");
        
        // 1. 查询初始地址列表
        System.out.println("1. 查询初始地址列表");
        testGetAddressList();
        
        // 2. 新增地址
        System.out.println("2. 新增地址");
        testAddAddress();
        
        // 3. 查询更新后的地址列表
        System.out.println("3. 查询更新后的地址列表");
        testGetAddressList();
        
        // 4. 更新地址
        System.out.println("4. 更新地址");
        testUpdateAddress();
        
        // 5. 删除地址
        System.out.println("5. 删除地址");
        testDeleteAddress();
        
        // 6. 查询最终地址列表
        System.out.println("6. 查询最终地址列表");
        testGetAddressList();
        
        System.out.println("=== 完整CRUD流程测试完成 ===");
    }


}
