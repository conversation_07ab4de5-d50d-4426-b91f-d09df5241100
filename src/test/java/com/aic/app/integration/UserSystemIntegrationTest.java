package com.aic.app.integration;

import com.aic.app.BaseIntegrationTest;
import com.aic.app.form.AddressLoginForm;
import com.aic.app.form.ClaimForm;
import com.aic.app.form.StakeForm;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserMapper;
import com.aic.app.model.StakeUser;
import com.aic.app.model.User;
import com.aic.app.service.IStakeUserService;
import com.aic.app.service.IUserService;
import com.aic.app.service.RustService;
import com.aic.app.vo.LoginResult;
import com.aic.app.vo.Result;
import com.aic.app.vo.UserInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户系统集成测试
 * 测试用户注册、登录、质押等核心功能
 */
@DisplayName("用户系统集成测试")
public class UserSystemIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private IUserService userService;

    @Autowired
    private IStakeUserService stakeUserService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private StakeUserMapper stakeUserMapper;

    private static final String TEST_ADDRESS = "0x1234567890123456789012345678901234567890";
    private static final String TEST_SIGN = "test_signature";

    @Override
    protected void setupTestData() {
        // 清理测试数据
        userMapper.delete(new LambdaQueryWrapper<>());
        stakeUserMapper.delete(new LambdaQueryWrapper<>());
    }

    @Test
    @DisplayName("用户地址登录 - 新用户自动注册")
    public void testAddressLogin_NewUser() throws Exception {
        // Mock RustService 验证签名
        when(RustService.checkSignMsg(TEST_ADDRESS, TEST_SIGN)).thenReturn(true);

        // 准备登录请求
        AddressLoginForm loginForm = new AddressLoginForm();
        loginForm.setAddress(TEST_ADDRESS);
        loginForm.setSign(TEST_SIGN);

        // 执行登录请求
        MvcResult result = mockMvc.perform(post("/api/address/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(loginForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.address").value(TEST_ADDRESS))
                .andExpect(jsonPath("$.data.token").exists())
                .andReturn();

        // 验证响应
        String responseBody = result.getResponse().getContentAsString();
        Result<LoginResult> loginResult = fromJson(responseBody, Result.class);
        assertNotNull(loginResult.getData());

        // 验证数据库中用户已创建
        User user = userService.findByAddress(TEST_ADDRESS);
        assertNotNull(user);
        assertEquals(TEST_ADDRESS, user.getAddress());
        assertEquals(1, user.getType()); // 钱包账户
        assertNotNull(user.getCode()); // 邀请码已生成
    }

    @Test
    @DisplayName("用户地址登录 - 已存在用户")
    public void testAddressLogin_ExistingUser() throws Exception {
        // 先创建用户
        User existingUser = new User();
        existingUser.setAddress(TEST_ADDRESS);
        existingUser.setType(1);
        existingUser.setCode("TEST123");
        userService.add(existingUser);

        // Mock RustService 验证签名
        when(RustService.checkSignMsg(TEST_ADDRESS, TEST_SIGN)).thenReturn(true);

        // 准备登录请求
        AddressLoginForm loginForm = new AddressLoginForm();
        loginForm.setAddress(TEST_ADDRESS);
        loginForm.setSign(TEST_SIGN);

        // 执行登录请求
        mockMvc.perform(post("/api/address/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(loginForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.user.address").value(TEST_ADDRESS))
                .andExpect(jsonPath("$.data.user.code").value("TEST123"));
    }

    @Test
    @DisplayName("用户地址登录 - 签名验证失败")
    public void testAddressLogin_InvalidSignature() throws Exception {
        // Mock RustService 验证签名失败
        when(RustService.checkSignMsg(TEST_ADDRESS, TEST_SIGN)).thenReturn(false);

        // 准备登录请求
        AddressLoginForm loginForm = new AddressLoginForm();
        loginForm.setAddress(TEST_ADDRESS);
        loginForm.setSign(TEST_SIGN);

        // 执行登录请求，期望失败
        mockMvc.perform(post("/api/address/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(loginForm)))
                .andExpect(status().is4xxClientError());
    }

    @Test
    @DisplayName("获取用户信息")
    public void testGetUserInfo() throws Exception {
        // 创建测试用户
        User testUser = new User();
        testUser.setAddress(TEST_ADDRESS);
        testUser.setType(1);
        testUser.setCode("TEST123");
        testUser.setLevel(2);
        userService.add(testUser);

        // 模拟登录状态
        String token = "test-token";
        redisTemplate.opsForValue().set("xyc:user:token:" + token, testUser.getId().toString());

        // 获取用户信息
        mockMvc.perform(get("/api/userinfo")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.address").value(TEST_ADDRESS))
                .andExpect(jsonPath("$.data.code").value("TEST123"))
                .andExpect(jsonPath("$.data.level").value(2));
    }

    @Test
    @DisplayName("获取签名消息")
    public void testGetSignMessage() throws Exception {
        // Mock RustService 返回签名消息
        String expectedMessage = "Please sign this message to login: " + TEST_ADDRESS;
        when(RustService.getSignMsg(TEST_ADDRESS)).thenReturn(expectedMessage);

        // 获取签名消息
        mockMvc.perform(get("/api/sign-msg")
                .param("address", TEST_ADDRESS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(expectedMessage));
    }

    @Test
    @DisplayName("用户质押功能测试")
    public void testUserStakeFlow() throws Exception {
        // 1. 创建测试用户
        User testUser = new User();
        testUser.setAddress(TEST_ADDRESS);
        testUser.setType(1);
        testUser.setCode("TEST123");
        userService.add(testUser);

        // 2. 创建质押用户记录
        StakeUser stakeUser = new StakeUser();
        stakeUser.setUserId(testUser.getId());
        stakeUser.setTokenId("FIST");
        stakeUser.setCurrentAmount(new BigDecimal("1000"));
        stakeUser.setStaticPool(new BigDecimal("100"));
        stakeUserService.save(stakeUser);

        // 3. 测试质押操作
        StakeForm stakeForm = new StakeForm();
        stakeForm.setTokenId("FIST");
        stakeForm.setAmount(new BigDecimal("500"));

        // Mock 质押操作
        // 注意：这里需要根据实际的质押逻辑进行调整
        
        // 4. 验证质押后的状态
        StakeUser updatedStakeUser = stakeUserService.getStakeUser(testUser.getId(), "FIST");
        assertNotNull(updatedStakeUser);
        assertEquals("FIST", updatedStakeUser.getTokenId());
    }

    @Test
    @DisplayName("用户收益领取测试")
    public void testClaimRewards() throws Exception {
        // 1. 创建测试用户和质押记录
        User testUser = new User();
        testUser.setAddress(TEST_ADDRESS);
        testUser.setType(1);
        testUser.setCode("TEST123");
        userService.add(testUser);

        StakeUser stakeUser = new StakeUser();
        stakeUser.setUserId(testUser.getId());
        stakeUser.setTokenId("FIST");
        stakeUser.setStaticPool(new BigDecimal("100"));
        stakeUser.setCanReceive(new BigDecimal("50"));
        stakeUserService.save(stakeUser);

        // 2. 测试领取静态收益
        ClaimForm claimForm = new ClaimForm();
        claimForm.setTokenId("FIST");
        claimForm.setAmount(new BigDecimal("30"));

        // Mock 领取操作
        // 注意：这里需要根据实际的领取逻辑进行调整

        // 3. 验证领取后的状态
        StakeUser updatedStakeUser = stakeUserService.getStakeUser(testUser.getId(), "FIST");
        assertNotNull(updatedStakeUser);
    }

    @Test
    @DisplayName("数据库连接和基础查询测试")
    public void testDatabaseConnection() throws Exception {
        // 测试数据库连接是否正常
        assertNotNull(dataSource);
        assertNotNull(dataSource.getConnection());

        // 测试基础查询
        long userCount = userService.count();
        assertTrue(userCount >= 0);

        long stakeUserCount = stakeUserService.count();
        assertTrue(stakeUserCount >= 0);
    }

    @Test
    @DisplayName("Redis 连接测试")
    public void testRedisConnection() {
        // 测试 Redis 连接
        String testKey = "test:key";
        String testValue = "test:value";

        redisTemplate.opsForValue().set(testKey, testValue);
        Object retrievedValue = redisTemplate.opsForValue().get(testKey);

        assertEquals(testValue, retrievedValue);

        // 清理测试数据
        redisTemplate.delete(testKey);
    }
}
