package com.aic.app.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.context.annotation.Bean;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.utility.DockerImageName;

/**
 * TestContainers 配置类
 * 用于集成测试时启动 MySQL 容器
 */
@TestConfiguration(proxyBeanMethods = false)
public class TestContainersConfig {

    /**
     * MySQL 容器配置
     * 使用 @ServiceConnection 自动配置数据源
     */
    @Bean
    @ServiceConnection
    public MySQLContainer<?> mysqlContainer() {
        return new MySQLContainer<>(DockerImageName.parse("mysql:8"))
                .withDatabaseName("xyc")
                .withUsername("root")
                .withPassword("testpass")
                .withInitScript("test-schema.sql"); // 初始化脚本
    }


}
