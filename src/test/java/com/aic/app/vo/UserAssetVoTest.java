package com.aic.app.vo;

import com.aic.app.model.UserAsset;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class UserAssetVoTest {

    private UserAsset testAsset;
    private static final int ITERATIONS = 1000000; // 100万次测试

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testAsset = new UserAsset();
        testAsset.setId(1L);
        testAsset.setUserId(100L);
        testAsset.setTokenId("TOKEN123");
        testAsset.setLogo("logo.png");
        testAsset.setBalance(new BigDecimal("1000.00"));
//        testAsset.setPrice(new BigDecimal("50.00"));
        testAsset.setStake(true);
        testAsset.setTokenAddress("0x123456789");
        testAsset.setStakeAddress("0x987654321");
    }

    @Test
    void performanceComparisonTest() {
        // 预热 JVM
        warmup();

        // 测试 BeanUtils
        long beanUtilsStart = System.currentTimeMillis();
        for (int i = 0; i < ITERATIONS; i++) {
            UserAssetVo vo = new UserAssetVo();
            BeanUtils.copyProperties(testAsset, vo);
        }
        long beanUtilsTime = System.currentTimeMillis() - beanUtilsStart;

        // 测试 MapStruct
        long mapStructStart = System.currentTimeMillis();
        for (int i = 0; i < ITERATIONS; i++) {
            UserAssetVo vo = new UserAssetVo(testAsset);
        }
        long mapStructTime = System.currentTimeMillis() - mapStructStart;

        // 输出结果
        System.out.println("性能测试结果 (" + ITERATIONS + " 次迭代):");
        System.out.println("BeanUtils 耗时: " + beanUtilsTime + "ms");
        System.out.println("MapStruct 耗时: " + mapStructTime + "ms");
        System.out.println("性能提升: " + String.format("%.2f", ((double)beanUtilsTime/mapStructTime)) + "倍");

        // 测试内存使用
        testMemoryUsage();
    }

    private void warmup() {
        // JVM 预热，每种方法各执行1万次
        for (int i = 0; i < 10000; i++) {
            UserAssetVo vo1 = new UserAssetVo();
            BeanUtils.copyProperties(testAsset, vo1);
            
            UserAssetVo vo2 = new UserAssetVo(testAsset);
        }
    }

    private void testMemoryUsage() {
        System.gc(); // 建议进行垃圾回收
        
        // 测试 BeanUtils 的内存使用
        List<UserAssetVo> beanUtilsList = new ArrayList<>();
        long beanUtilsMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        for (int i = 0; i < 100000; i++) {
            UserAssetVo vo = new UserAssetVo();
            BeanUtils.copyProperties(testAsset, vo);
            beanUtilsList.add(vo);
        }
        
        long beanUtilsMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long beanUtilsMemoryUsed = beanUtilsMemoryAfter - beanUtilsMemoryBefore;
        beanUtilsList.clear();
        System.gc();

        // 测试 MapStruct 的内存使用
        List<UserAssetVo> mapStructList = new ArrayList<>();
        long mapStructMemoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        for (int i = 0; i < 100000; i++) {
            UserAssetVo vo = new UserAssetVo(testAsset);
            mapStructList.add(vo);
        }
        
        long mapStructMemoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long mapStructMemoryUsed = mapStructMemoryAfter - mapStructMemoryBefore;

        System.out.println("\n内存使用情况 (100000 个对象):");
        System.out.println("BeanUtils 内存使用: " + (beanUtilsMemoryUsed / 1024) + "KB");
        System.out.println("MapStruct 内存使用: " + (mapStructMemoryUsed / 1024) + "KB");
    }
} 
