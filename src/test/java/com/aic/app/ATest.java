package com.aic.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

public class ATest {

    public static void main(String[] args) throws Exception{
//        ObjectMapper om = new ObjectMapper();
//        String json = """
//                ["0.004","0.004"]
//                """;
//        // json 解析为 List<String>
//        List<String> list = om.readValue(json, new TypeReference<List<String>>(){});
//        System.out.println(list);

//        System.out.println(Calendar.getInstance().get(Calendar.DAY_OF_WEEK));

//        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
//        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
//        System.out.println(dayOfWeek);

        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
//        Calendar now = Calendar.getInstance(Locale.CHINA);
        now.setFirstDayOfWeek(Calendar.MONDAY);
        int dayOfWeek = now.get(Calendar.DAY_OF_WEEK);
        System.out.println("now.getFirstDayOfWeek() = " + now.getFirstDayOfWeek());
        System.out.println("Calendar.MONDAY = " + Calendar.MONDAY);
        System.out.println("dayOfWeek = " + dayOfWeek);
        if (dayOfWeek == Calendar.SUNDAY){
            dayOfWeek = 7;
        } else {
            dayOfWeek = dayOfWeek - 1;
        }
        System.out.println(dayOfWeek);

        System.out.println(Locale.getDefault());
        
    }
    
}
