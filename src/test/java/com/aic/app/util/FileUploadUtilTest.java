package com.aic.app.util;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

class FileUploadUtilTest {

    @Test
    void testUploadSuccess() throws IOException {
        // 创建模拟文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        // 执行上传
        String filePath = FileUploadUtil.upload(file);

        // 验证结果
        assertNotNull(filePath);
        assertTrue(filePath.startsWith("/upload/"));
        assertTrue(filePath.endsWith(".jpg"));

        // 验证文件是否真的被创建
        String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;
        String userDir = System.getProperty("user.dir");
        Path absolutePath = Paths.get(userDir, cleanPath);
        assertTrue(Files.exists(absolutePath));

        // 清理测试文件
        FileUploadUtil.deleteFile(filePath);
    }

    @Test
    void testUploadEmptyFile() {
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                new byte[0]
        );

        IOException exception = assertThrows(IOException.class, () -> {
            FileUploadUtil.upload(emptyFile);
        });

        assertEquals("上传文件不能为空", exception.getMessage());
    }

    @Test
    void testUploadUnsupportedFileType() {
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "test content".getBytes()
        );

        IOException exception = assertThrows(IOException.class, () -> {
            FileUploadUtil.upload(file);
        });

        assertTrue(exception.getMessage().contains("不支持的文件类型"));
    }

    @Test
    void testFileExists() throws IOException {
        // 创建测试文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.png",
                "image/png",
                "test image content".getBytes()
        );

        String filePath = FileUploadUtil.upload(file);

        // 测试文件存在检查
        assertTrue(FileUploadUtil.fileExists(filePath));

        // 删除文件后再检查
        FileUploadUtil.deleteFile(filePath);
        assertFalse(FileUploadUtil.fileExists(filePath));
    }
}
